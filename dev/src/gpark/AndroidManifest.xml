<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application>

        <activity
            android:name=".ui.main.MainActivity"
            android:configChanges="keyboardHidden|orientation|screenLayout|screenSize"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/Theme.MainActivity"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.APP_BROWSER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:host="${SCHEME_HOST}" />
                <data android:scheme="gpark" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.APP_BROWSER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:host="${SCHEME_HOST}" />
                <data android:scheme="https" />
            </intent-filter>
        </activity>

    </application>

</manifest>