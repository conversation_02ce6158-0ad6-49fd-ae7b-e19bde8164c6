<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_long_image_container"
    android:layout_width="@dimen/dp_300"
    android:layout_height="wrap_content"
    android:background="@color/color_FFDC1C"
    android:visibility="invisible"
    app:layout_constraintTop_toTopOf="parent"
    tools:visibility="visible">

    <View
        android:id="@+id/v_long_image_avatar_bg"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_18"
        android:background="@drawable/bg_white_round_32"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_long_image_avatar"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        android:src="@drawable/icon_default_avatar"
        app:cornerRadii="@dimen/dp_30"
        app:layout_constraintBottom_toBottomOf="@id/v_long_image_avatar_bg"
        app:layout_constraintEnd_toEndOf="@id/v_long_image_avatar_bg"
        app:layout_constraintStart_toStartOf="@id/v_long_image_avatar_bg"
        app:layout_constraintTop_toTopOf="@id/v_long_image_avatar_bg" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_uname"
        style="@style/MetaTextView.S20.PoppinsSemiBold600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_16"
        android:ellipsize="end"
        android:maxLines="1"
        android:minHeight="@dimen/dp_32"
        android:singleLine="true"
        android:textSize="@dimen/dp_20"
        app:layout_constraintBottom_toTopOf="@id/tv_long_image_id"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/v_long_image_avatar_bg"
        app:layout_constraintTop_toTopOf="@id/v_long_image_avatar_bg"
        app:layout_constraintVertical_chainStyle="packed"
        app:uiLineHeight="@dimen/dp_32"
        tools:text="adsflkkjasdklfd" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_id"
        style="@style/MetaTextView.S12.PoppinsLight300"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_2"
        android:drawableStart="@drawable/ic_emoji_id"
        android:drawablePadding="@dimen/dp_2"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minHeight="@dimen/dp_20"
        android:singleLine="true"
        android:textColor="@color/neutral_color_2"
        android:textSize="@dimen/dp_12"
        app:layout_constraintBottom_toBottomOf="@id/v_long_image_avatar_bg"
        app:layout_constraintEnd_toEndOf="@id/tv_long_image_uname"
        app:layout_constraintStart_toStartOf="@id/tv_long_image_uname"
        app:layout_constraintTop_toBottomOf="@id/tv_long_image_uname"
        app:uiLineHeight="@dimen/dp_20"
        tools:text="7489279875" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_long_image_stat_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="@dimen/dp_50" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_long_image_stat_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_end="@dimen/dp_50" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_followers_count"
        style="@style/MetaTextView.S16.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minHeight="@dimen/dp_20"
        android:singleLine="true"
        android:textSize="@dimen/dp_16"
        app:layout_constraintEnd_toEndOf="@id/guide_long_image_stat_left"
        app:layout_constraintStart_toStartOf="@id/guide_long_image_stat_left"
        app:layout_constraintTop_toBottomOf="@id/v_long_image_avatar_bg"
        app:uiLineHeight="@dimen/dp_20"
        tools:text="999.9K" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_followers_label"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minHeight="@dimen/dp_20"
        android:singleLine="true"
        android:text="@string/user_fans_title"
        android:textColor="@color/neutral_color_2"
        android:textSize="@dimen/dp_12"
        app:layout_constraintEnd_toEndOf="@id/tv_long_image_followers_count"
        app:layout_constraintStart_toStartOf="@id/tv_long_image_followers_count"
        app:layout_constraintTop_toBottomOf="@id/tv_long_image_followers_count"
        app:uiLineHeight="@dimen/dp_20" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_following_count"
        style="@style/MetaTextView.S16.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minHeight="@dimen/dp_20"
        android:singleLine="true"
        android:textSize="@dimen/dp_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_long_image_followers_count"
        app:uiLineHeight="@dimen/dp_20"
        tools:text="999.9K" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_following_label"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minHeight="@dimen/dp_20"
        android:singleLine="true"
        android:text="@string/user_follow_title"
        android:textColor="@color/neutral_color_2"
        android:textSize="@dimen/dp_12"
        app:layout_constraintEnd_toEndOf="@id/tv_long_image_following_count"
        app:layout_constraintStart_toStartOf="@id/tv_long_image_following_count"
        app:layout_constraintTop_toBottomOf="@id/tv_long_image_following_count"
        app:uiLineHeight="@dimen/dp_20" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_likes_count"
        style="@style/MetaTextView.S16.PoppinsBold700"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minHeight="@dimen/dp_20"
        android:singleLine="true"
        android:textSize="@dimen/dp_16"
        app:layout_constraintEnd_toEndOf="@id/guide_long_image_stat_right"
        app:layout_constraintStart_toStartOf="@id/guide_long_image_stat_right"
        app:layout_constraintTop_toTopOf="@id/tv_long_image_followers_count"
        app:uiLineHeight="@dimen/dp_20"
        tools:text="999.9K" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_likes_label"
        style="@style/MetaTextView.S12"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minHeight="@dimen/dp_20"
        android:singleLine="true"
        android:text="@string/user_like"
        android:textColor="@color/neutral_color_2"
        android:textSize="@dimen/dp_12"
        app:layout_constraintEnd_toEndOf="@id/tv_long_image_likes_count"
        app:layout_constraintStart_toStartOf="@id/tv_long_image_likes_count"
        app:layout_constraintTop_toBottomOf="@id/tv_long_image_likes_count"
        app:uiLineHeight="@dimen/dp_20" />

    <View
        android:id="@+id/v_long_image_bottom_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:background="@drawable/bg_white_corner_12"
        app:layout_constraintBottom_toBottomOf="@id/tv_long_image_scan_tips"
        app:layout_constraintTop_toBottomOf="@id/tv_long_image_followers_label" />

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_long_image_game_icon"
        android:layout_width="@dimen/dp_48"
        android:layout_height="@dimen/dp_48"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:scaleType="centerCrop"
        app:cornerRadii="@dimen/dp_8"
        app:layout_constraintStart_toStartOf="@id/v_long_image_bottom_bg"
        app:layout_constraintTop_toTopOf="@id/v_long_image_bottom_bg"
        tools:src="@drawable/placeholder" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_game_name"
        style="@style/MetaTextView.S14.PoppinsBold700"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginEnd="@dimen/dp_16"
        android:minHeight="@dimen/dp_22"
        android:singleLine="true"
        android:textSize="@dimen/dp_14"
        app:layout_constraintBottom_toTopOf="@id/tv_long_image_game_play_time"
        app:layout_constraintEnd_toEndOf="@id/v_long_image_bottom_bg"
        app:layout_constraintStart_toEndOf="@id/iv_long_image_game_icon"
        app:layout_constraintTop_toTopOf="@id/iv_long_image_game_icon"
        app:layout_constraintVertical_chainStyle="packed"
        app:uiLineHeight="@dimen/dp_22"
        tools:text="Lunch venueLunch ven…" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_game_play_time"
        style="@style/MetaTextView.S12"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:drawableStart="@drawable/icon_time"
        android:drawablePadding="@dimen/dp_4"
        android:gravity="center_vertical"
        android:minHeight="@dimen/dp_20"
        android:singleLine="true"
        android:textColor="@color/neutral_color_4"
        android:textSize="@dimen/dp_12"
        app:drawableTint="@color/neutral_color_4"
        app:layout_constraintBottom_toBottomOf="@id/iv_long_image_game_icon"
        app:layout_constraintEnd_toEndOf="@id/tv_long_image_game_name"
        app:layout_constraintStart_toStartOf="@id/tv_long_image_game_name"
        app:layout_constraintTop_toBottomOf="@id/tv_long_image_game_name"
        app:uiLineHeight="@dimen/dp_20"
        tools:text="12h 56m" />

    <ImageView
        android:id="@+id/iv_long_image_game_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_2"
        android:layout_marginHorizontal="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_12"
        android:src="@drawable/ic_share_profile_dash"
        app:layout_constraintTop_toBottomOf="@id/iv_long_image_game_icon" />

    <View
        android:id="@+id/v_long_image_game_divider_cover_left"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:layout_marginStart="@dimen/dp_10"
        android:background="@drawable/bg_ffdc1c_round_6"
        app:layout_constraintBottom_toBottomOf="@id/iv_long_image_game_divider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_long_image_game_divider" />

    <View
        android:id="@+id/v_long_image_game_divider_cover_right"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@drawable/bg_ffdc1c_round_6"
        app:layout_constraintBottom_toBottomOf="@id/iv_long_image_game_divider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_long_image_game_divider" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_long_image_game"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="iv_long_image_game_icon, tv_long_image_game_name,
        tv_long_image_game_play_time, v_long_image_game_divider_cover_left,
        iv_long_image_game_divider, v_long_image_game_divider_cover_right"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/iv_long_image_qr_code"
        android:layout_width="@dimen/dp_150"
        android:layout_height="@dimen/dp_150"
        android:layout_marginTop="@dimen/dp_20"
        android:padding="@dimen/dp_8"
        app:layout_constraintEnd_toEndOf="@id/v_long_image_bottom_bg"
        app:layout_constraintStart_toStartOf="@id/v_long_image_bottom_bg"
        app:layout_constraintTop_toBottomOf="@id/iv_long_image_game_divider"
        app:layout_constraintTop_toTopOf="@id/v_long_image_bottom_bg" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_long_image_scan_tips"
        style="@style/MetaTextView.S14"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_2"
        android:layout_marginEnd="@dimen/dp_16"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:minHeight="@dimen/dp_16"
        android:paddingBottom="@dimen/dp_14"
        android:text="@string/profile_share_qr"
        android:textColor="@color/neutral_color_3"
        android:textSize="@dimen/dp_14"
        app:layout_constraintEnd_toEndOf="@id/v_long_image_bottom_bg"
        app:layout_constraintStart_toStartOf="@id/v_long_image_bottom_bg"
        app:layout_constraintTop_toBottomOf="@id/iv_long_image_qr_code"
        app:uiLineHeight="@dimen/dp_16" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_29"
        android:layout_marginTop="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_11"
        android:src="@drawable/ic_gpark"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_long_image_scan_tips" />

</androidx.constraintlayout.widget.ConstraintLayout>