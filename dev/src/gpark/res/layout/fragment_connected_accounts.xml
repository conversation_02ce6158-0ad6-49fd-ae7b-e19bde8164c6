<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <View
        android:layout_height="@dimen/dp_0"
        android:layout_width="match_parent"
        android:background="@color/color_F6F6F6"
        app:layout_constraintTop_toBottomOf="@id/tbl_title_bar"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <!--顶栏-->
    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBarPlaceholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl_title_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isDividerVisible="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusBarPlaceholder"
        app:title_text="@string/account_setting_connected_accounts"
        app:title_text_color="@color/textColorPrimary" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:layout_margin="@dimen/dp_16"
        android:background="@drawable/bg_white_corner_12"
        app:layout_constraintTop_toBottomOf="@id/tbl_title_bar"
        >

        <com.socialplay.gpark.ui.view.preference.PreferenceItemView
            android:id="@+id/pivFacebook"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            app:layout_constraintTop_toTopOf="parent"
            app:show_divider="true"
            android:visibility="gone"
            app:style="custom">
            <include
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                layout="@layout/layout_custom_setting_link_accoount" />
        </com.socialplay.gpark.ui.view.preference.PreferenceItemView>

        <com.socialplay.gpark.ui.view.preference.PreferenceItemView
            android:id="@+id/pivGoogle"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            app:style="custom"
            app:layout_constraintTop_toBottomOf="@id/pivFacebook"
            >
            <include
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                layout="@layout/layout_custom_setting_link_accoount" />
        </com.socialplay.gpark.ui.view.preference.PreferenceItemView>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/vLoading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_10"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>