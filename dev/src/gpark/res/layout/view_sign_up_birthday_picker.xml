<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:clickable="true"
    android:focusable="true"
    tools:background="@color/colorAccentPrimary"
    android:paddingTop="28dp"
    android:background="@drawable/bg_white_top_round_24"
    android:orientation="vertical">

    <com.socialplay.gpark.ui.view.MetaTextView
        style="@style/MetaTextView.S18.PoppinsSemiBold600"
        android:text="@string/text_birthday"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="28dp"/>

    <!--此部分需要完整复制过去，删减或者更改ID会导致初始化找不到内容而报空-->
    <LinearLayout
        android:id="@+id/timepicker"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:background="@android:color/white"
        tools:background="@color/black"
        android:gravity="center"
        android:minHeight="100dp"
        android:orientation="horizontal">

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/month"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_weight="1.1" />

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/day"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_weight="1.1" />

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/year"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_weight="2" />

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/hour"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_weight="1.1" />

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/min"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_weight="1.1" />

        <com.socialplay.gpark.ui.view.wheelview.view.WheelView
            android:id="@+id/second"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_weight="1.1" />
    </LinearLayout>

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_finish"
        style="@style/Button.S18.PoppinsBlack900.Height48"
        android:text="@string/choose_main_home"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:layout_marginHorizontal="16dp"
        android:layout_width="match_parent"
        android:minHeight="48dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="28dp"
        android:layout_marginBottom="50dp"/>
</LinearLayout>