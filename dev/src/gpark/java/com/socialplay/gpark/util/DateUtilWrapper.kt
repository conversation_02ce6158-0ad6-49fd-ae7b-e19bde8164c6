package com.socialplay.gpark.util

import android.content.Context
import com.socialplay.gpark.R
import com.socialplay.gpark.util.DateUtil.formatJustTime
import com.socialplay.gpark.util.DateUtil.formatSimpleDate
import com.socialplay.gpark.util.DateUtil.formatWholeDate
import org.koin.core.context.GlobalContext
import java.util.Calendar
import java.util.concurrent.TimeUnit

object DateUtilWrapper {

    const val NEED_KEEP_DOUBLE_DIGITS = true

    fun getLabel4Year(context: Context): String = ""

    fun getLabel4Day(context: Context): String = ""

    fun getDefaultBirthCalendar(): Calendar {
        return Calendar.getInstance().apply {
            set(Calendar.YEAR, 1900)
            set(Calendar.MONTH, 0)
            set(Calendar.DATE, 1)
        }
    }

    fun getEarliestBirthCalendar(): Calendar {
        return Calendar.getInstance().apply {
            set(Calendar.YEAR, 1900)
            set(Calendar.MONTH, 0)
            set(Calendar.DATE, 1)
        }
    }

    fun getCreateFormatDate(context: Context, timeStamp: Long): String {
        return timeStamp.formatAgoStyleV2()
    }

    fun getUpdateFormatDate(context: Context, timeStamp: Long): String {
        return timeStamp.formatAgoStyleV2()
    }

    fun getFormatCommentDate(context: Context, timeStamp: Long): String {
        return timeStamp.formatAgoStyleV2()
    }

    fun getTransactionDate(context: Context, timeStamp: Long): String {
        return timeStamp.formatAgoStyleV2()
    }

    /**
     * 海外时间展示规则
     * 国内时间展示规则参考: party 下面的 DateUtilWrapper 中的 getCZHTime 方法
     * 文档: https://meta.feishu.cn/wiki/GV2Cwa3fVimwXNkgiuKcLlApnIc
     */
    private fun Long.formatAgoStyleV2(context: Context = GlobalContext.get().get()): String {
        val time = if (this <= 0L) System.currentTimeMillis() else this
        val now = System.currentTimeMillis()
        val span = now - time
        return when {
            // 1分钟内
            span <= TimeUnit.MINUTES.toMillis(1) -> context.getString(R.string.just)
            // 1小时内
            span <= TimeUnit.HOURS.toMillis(1) -> context.getString(
                R.string.few_minutes_ago,
                span / TimeUnit.MINUTES.toMillis(1)
            )
            // 24小时内
            span <= TimeUnit.DAYS.toMillis(1) -> context.getString(
                R.string.few_hours_ago,
                span / TimeUnit.HOURS.toMillis(1)
            )
            // 昨天
            span >= TimeUnit.DAYS.toMillis(1) && span <= TimeUnit.DAYS.toMillis(1) * 2 -> {
                "${context.getString(R.string.yesterday)} ${time.formatJustTime()}"
            }
            // 7天内
            span <= TimeUnit.DAYS.toMillis(1) * 7 -> {
                time.formatSimpleDate()
            }
            // 7天之前
            else -> time.formatWholeDate()
        }
    }
}