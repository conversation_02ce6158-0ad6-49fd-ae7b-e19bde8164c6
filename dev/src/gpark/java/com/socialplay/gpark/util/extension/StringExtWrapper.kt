package com.socialplay.gpark.util.extension

fun String?.friendListIndexChar(default: Char): Char {
    return if (this.isNullOrEmpty() || this.isBlank()) {
        default
    } else {
        val ch = (this.firstOrNull() ?: '#').uppercaseChar()
        if (ch in 'A'..'Z') {
            ch
        } else {
            default
        }
    }
}

fun String?.pinyin(default: String): String {
    return default
}

fun String.pinyinShort(): String {
    return this
}

fun String.pinyinList(): List<String> {
    return emptyList()
}
