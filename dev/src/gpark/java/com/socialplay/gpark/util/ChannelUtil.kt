package com.socialplay.gpark.util

import android.content.Context
import com.socialplay.gpark.data.kv.MetaKV
import org.koin.core.context.GlobalContext

object ChannelUtil {
    const val DEFAULT_CHANNEL = "default"
    const val DEFAULT_META_TRACKING = "all"

    /**
     * Meta Tracking
     * 类型暂时有三种：
     *  all:默认值，开启三种激活上报
     *  td:TalkingData
     *  ry:热云
     *  meta:233乐园自有
     */
    private val metaKV: MetaKV = GlobalContext.get().get()


    fun getApkChannelId(context: Context): String {
        return DEFAULT_CHANNEL
    }

    /**
     * 获取渠道内超级推荐位id
     */
    fun getSuperGameId(): Long {
        if (metaKV.appKV.superGameId == -1L) {
            // todo
        }
        return metaKV.appKV.superGameId
    }

    fun getSubChannelId(context: Context, apkChannelId: String?): String? {
        return null
    }
}