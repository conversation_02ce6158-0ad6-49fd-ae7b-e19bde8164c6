package com.socialplay.gpark.util

import android.content.Context
import android.os.Build
import com.socialplay.gpark.ui.permission.Permission
import com.socialplay.gpark.ui.permission.PermissionRequest

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/02/08
 *     desc   :
 * </pre>
 */
object PermissionUtil {

    fun diverse(context: Context, scene: Int, builder: PermissionRequest.Builder) {

    }

    fun needPermission4PublishPost(context: Context): <PERSON><PERSON><PERSON> {
        val permission =
            if (Build.VERSION.SDK_INT == Build.VERSION_CODES.TIRAMISU && !PermissionRequest.checkSelfPermission(
                    context,
                    *Permission.LOCAL_MEDIA.permissions
                )
            ) {
                Permission.LOCAL_MEDIA
            } else if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU && !PermissionRequest.checkSelfPermission(
                    context,
                    *Permission.EXTERNAL_STORAGE.permissions
                )
            ) {
                Permission.EXTERNAL_STORAGE
            } else {
                null
            }
        return permission != null
    }
}