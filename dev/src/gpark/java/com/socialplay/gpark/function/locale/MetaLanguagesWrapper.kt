package com.socialplay.gpark.function.locale

import android.content.Context
import com.socialplay.gpark.data.model.locale.LanguageOption
import com.socialplay.gpark.data.model.locale.MetaLanguageItem
import com.socialplay.gpark.function.locale.MetaLanguages.getFollowSystemLanguage
import kotlin.collections.listOf

object MetaLanguagesWrapper {
    fun getDefaultLanguage(): MetaLanguageItem {
        return MetaLanguageItem.ENGLISH
    }


    fun getSupportLanguageOpts(context: Context): List<LanguageOption> {
        return listOf(
            LanguageOption(getFollowSystemLanguage(context), true),
            LanguageOption(MetaLanguageItem.ENGLISH, false),
//            LanguageOption(MetaLanguageItem.JAPANESE, false),
//            LanguageOption(MetaLanguageItem.KOREAN, false),
//            LanguageOption(MetaLanguageItem.TRADITIONAL_CHINESE, false),
//            LanguageOption(MetaLanguageItem.SIMPLIFIED_CHINESE, false)
        )
    }
}