package com.socialplay.gpark.function.overseabridge.bridge

import android.content.Context
import org.json.JSONArray
import org.json.JSONObject

interface ISolarEngineBridge {
    fun preInit(context: Context, appKey: String)

    fun initialize(
        context: Context,
        appKey: String,
        config: SolarEngineConfigBridge,
        initializeCallback: IInitializationCallbackBridge
    )

    /**
     * 回归因结果信息，默认为null
     * 返回结果示例
     *
     * 归因结果attribution中归因字段含义如下：
     *
     * 参数名称	参数示例	描述
     * attribution_touch_type	click,impression	归因触点类型
     * ry_touchpoint_ts	归因的展示or点击的时间，年月日时分秒	归因触点时间
     * install_time	设备激活的时间，年月日，时分秒	激活时间
     * attribution_time	2022/5/11 15:00:03	归因时间
     * turl_campaign_id	3e0a9bad8455d685eaaf91bad71bdeb2	监测链接id
     * turl_campaign_name	Mintegral_tracking	监测链接名称
     * turl_id	UfeE7za	短链id
     * channel_id	1234	渠道id
     * channel_name	mintegral	归因的渠道名称
     * attribution_type	ua	UA（标识拉新）
     * adgroup_name	-	渠道推广广告组名称
     * adplan_id	-	渠道推广计划ID
     * adplan_name	-	渠道推广使用素材名称
     * adcreative_id	-	渠道推广使用素材ID
     * adcreative_name	-	渠道推广使用素材名称
     * adcreative_type	-	渠道推广使用素材类型
     * site_id	-	子渠道ID
     * site_name	-	子渠道名称
     * ad_type	-	渠道推广广告类型
     * placement_id	-	渠道推广广告位ID
     * conversion_id	-	广告位id
     * click_id	-	渠道推广点击唯一ID
     * impression_id	-	渠道推广展示唯一ID
     * request_id	-	广告请求id
     * callback_id	-	广告响应id
     * custom_params_1	-	渠道推广自定义参数1-10
     * custom_params_2	-	渠道推广自定义参数1-10
     * custom_params_3	-	渠道推广自定义参数1-10
     * custom_params_4	-	渠道推广自定义参数1-10
     * custom_params_5	-	渠道推广自定义参数1-10
     * custom_params_6	-	渠道推广自定义参数1-10
     * custom_params_7	-	渠道推广自定义参数1-10
     * custom_params_8	-	渠道推广自定义参数1-10
     * custom_params_9	-	渠道推广自定义参数1-10
     * custom_params_10	-	渠道推广自定义参数1-10
     *
     * {
     * 	"status": 0,
     * 	"data": {
     * 		"user_data": {
     * 			"account_id": "",
     * 			"ad_type": "",
     * 			"adcreative_id": "",
     * 			"adcreative_name": "",
     * 			"adcreative_type": "",
     * 			"adgroup_id": "",
     * 			"adgroup_name": "",
     * 			"adplan_id": "",
     * 			"adplan_name": "",
     * 			"attribution_time": "2023-08-31 11:16:35",
     * 			"attribution_touch_type": "",
     * 			"attribution_type": "",
     * 			"callback_id": "",
     * 			"channel_id": "-1",
     * 			"channel_name": "自然量",
     * 			"click_id": "",
     * 			"conversion_id": "",
     * 			"custom_params_1": "",
     * 			"custom_params_10": "",
     * 			"custom_params_2": "",
     * 			"custom_params_3": "",
     * 			"custom_params_4": "",
     * 			"custom_params_5": "",
     * 			"custom_params_6": "",
     * 			"custom_params_7": "",
     * 			"custom_params_8": "",
     * 			"custom_params_9": "",
     * 			"impression_id": "",
     * 			"install_time": "2023-08-31 11:16:32",
     * 			"placement_id": "",
     * 			"report_time": "2023-08-31 11:16:34",
     * 			"request_id": "",
     * 			"ry_touchpoint_ts": "",
     * 			"site_id": "",
     * 			"site_name": "",
     * 			"turl_campaign_id": "",
     * 			"turl_campaign_name": "",
     * 			"turl_id": ""
     * 		}
     * 	}
     * }
     */
    fun getAttribution(): JSONObject?

    /**
     * 获取设备 distinct_id
     * 返回当前设置的distinct_id，没有则为null
     */
    fun getDistinctId(): String?

    /**
     * 该属性用于标识应用的安装渠道。通过设置不同的渠道值，您可以追踪应用的下载来源，以更好地了解用户获取应用的途径，可在初始化 SDK 前设置属性值，如果未传入或传入null，则默认由SDK自己采集，可能为null
     */
    fun setChannel(channel: String?)

    /**
     * 热力引擎 SDK 提供了专门设置 GAID 的接口，方便开发者灵活集成，通常情况下 SDK 会自动获取 GAID，开发者不需要手动设置，手动设置时将进行保存，在下一次传入前不会修改、不会删除。重复设置时保存最后一次传入的值。
     */
    fun setGaid(gaid: String?)

    /**
     * customEventName	开发者自定义事件名称	String	是
     * preEventData	预置属性，key只支持_pay_amount 和 _currency_type 两个字段	JSONObject	否
     * customEventData	开发者自定义事件属性
     */
    fun logEvent(customEventName: String, preEventData: JSONObject?, customEventData: JSONObject?)

    /**
     * 检查缓存事件并立即上报
     */
    fun reportEventImmediately()

    /**
     * 如果您要上传一批用户属性，其中已经存在的用户属性不去更新属性值、不存在的属性进行创建并保存属性值，则可以调用 userInit 来进行设置。
     * 用户属性设置分为以下几种方式：userUpdate、userInit、userAdd、userUnset、userAppend、userDelete，由开发者调用，对用户属性进行设置。
     *
     * 注：
     * 用户属性格式要求与事件属性保持一致。
     * 用户属性设置事件上报时设置的自定义属性均不支持开发者传入"_"下划线开头的 key 值，SDK 会默认丢弃该条属性。
     *
     */
    fun userInit(properties: JSONObject)

    /**
     * 对于一般的用户属性，您可以调用 userUpdate 来进行设置，使用该接口上传的属性将会覆盖原有的属性值，如果之前不存在该用户属性，则会新建该用户属性，类型与传入属性的类型一致。此处以设置城市为例。
     */
    fun userUpdate(properties: JSONObject)


    /**
     * 当您要上传数值型的属性时，您可以调用 userAdd 来对该属性进行累加操作，如果该属性还未被设置，则会赋值 0 后再进行计算，可传入负值，等同于相减操作。此处以累计付费金额为例。
     */
    fun userAdd(properties: JSONObject)

    /**
     * 当您要清空用户的用户属性值时，您可以调用 userUnset 来对指定属性（字符串数组）进行清空操作，如果该属性还未在集群中被创建，则 不会 创建该属性。
     */
    fun userUnset(vararg keys: String)

    /**
     * 您可以调用 userAppend 对数组类型的用户属性进行追加操作。如果该属性不存在，则会新建该属性。
     */
    fun userAppend(properties: JSONObject)

    /**
     * 如果您要删除某个用户，可以调用 userDelete 将这名用户删除。用户删除后，您将无法再查询该名用户的用户属性，但该用户产生的事件仍然可以被查询到。
     */
    fun userDelete(userDeleteType: UserDeleteTypeBridge)

    /**
     * 设置访客 ID
     * 访客 ID 即 _visitor_id，是用户在设备上安装了应用之后，登录状态之前该用户的唯一标识。
     *
     * 我们提供访客 ID 自定义设置的接口，如果您有自己的访客管理体系需要替换访客 ID，应在 SDK 初始化之前进行设置。*
     * 数据上报时仅以最后一次传入的访客 ID 为准，应避免多次调用造成多个非正常访客 ID 先后上报数据的情况。
     */
    fun setVisitorID(visitorID: String)

    /**
     * 获取访客 ID
     * 返回已经设置的访客 ID，默认为""
     */
    fun getVisitorID(): String?

    /**
     * 如果应用有登录功能，应在登录成功后调用 login 设置账号 ID，设置成功后产生的事件中会带有此账号 ID，在用户退出登录时应调用 logout 清除账号 ID，后续事件中将不带有账号 ID。
     * 注意：在账号 ID 设置完成后，在调用 logout 清除账号 ID之前将一直保留，直到清除账号 ID。
     * 设置账号 ID
     */
    fun login(accountID: String)

    /**
     * 获取账号 ID
     * 返回已经设置的账号ID，默认为""
     */
    fun getAccountID(): String?

    /**
     * 清除账号ID
     */
    fun logout()

    fun setSuperProperties(context: Context, key: String, value: Int)

    fun setSuperProperties(context: Context, key: String, value: Long)

    fun setSuperProperties(context: Context, key: String, value: Float)

    fun setSuperProperties(context: Context, key: String, value: Double)

    fun setSuperProperties(context: Context, key: String, value: Boolean)

    fun setSuperProperties(context: Context, key: String, value: String)

    fun setSuperProperties(context: Context, key: String, values: JSONArray)

    fun setSuperProperties(context: Context, key: String, values: JSONObject)

    fun unsetSuperProperty(context: Context, key: String)

    fun clearSuperProperties(context: Context)

    interface IInitializationCallbackBridge {

        /**
         * 初始化完成 code为0则为初始化成功否则则为失败
         * code码释义
         * 0	初始化成功
         * 101	SDK未预初始化
         * 102	appkey非法
         * 103	context为null
         * 104	distinct_id生成失败
         */
        fun onInitializationCompleted(code: Int)
    }


    interface OnAttributionListener {
        fun onAttributionSuccess(attribution: JSONObject?)

        /**
         * 100: _appKey无效
         * 101:_distinct_id无效
         * 102: _distinct_id_type无效
         * 1001: 网络错误，SDK链接服务端失败
         * 1002: 当次启动请求超过10次还未获取到归因结果
         * 1003: 距离上次轮询请求归因结果小于5分钟，请5分钟后再试
         * 1004: 该用户超过15天未获取到归因结果，此次安装内将不再请求归因结果
         */
        fun onAttributionFail(errorCode: Int)
    }


    enum class UserDeleteTypeBridge {
        DELETE_BY_ACCOUNTID,
        DELETE_BY_VISITORID;
    }
}
