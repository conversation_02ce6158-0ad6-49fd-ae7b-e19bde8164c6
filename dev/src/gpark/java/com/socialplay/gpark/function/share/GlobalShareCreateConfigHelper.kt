package com.socialplay.gpark.function.share

import android.content.Context
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.locale.MetaLanguageItem
import com.socialplay.gpark.data.model.share.ShareConfig
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.function.pandora.PandoraToggleWrapper
import com.socialplay.gpark.ui.share.GlobalShareState
import com.socialplay.gpark.ui.share.GlobalShareViewModel
import java.util.Locale

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/29
 *     desc   :
 * </pre>
 */
object GlobalShareCreateConfigHelper {

    fun getLang(): String {
        return when (Locale.getDefault().language) {
//            MetaLanguageItem.JAPANESE.locale.language -> {
//                "ja"
//            }
//
//            MetaLanguageItem.KOREAN.locale.language -> {
//                "ko"
//            }

            else -> {
                "en"
            }
        }
    }

    fun createConfig4Profile(type: Int, platform: String): Int {
        when (platform) {
            SharePlatform.PLATFORM_SYSTEM,
            SharePlatform.PLATFORM_TIKTOK,
            SharePlatform.PLATFORM_SNAPCHAT -> {
                return GlobalShareViewModel.CONFIG_NOTHING
            }
        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4Pgc(type: Int, platform: String): Int {
        when (type) {
            GlobalShareState.TYPE_NORMAL -> {
                when (platform) {
                    SharePlatform.PLATFORM_TIKTOK -> {
                        return GlobalShareViewModel.CONFIG_NOTHING
                    }

                    SharePlatform.PLATFORM_SNAPCHAT -> {
                        return GlobalShareViewModel.CONFIG_LINK
                    }
                }
            }

            GlobalShareState.TYPE_LONG_IMAGE -> {
                when (platform) {
                    SharePlatform.PLATFORM_TIKTOK,
                    SharePlatform.PLATFORM_SNAPCHAT -> {
                        return GlobalShareViewModel.CONFIG_NOTHING
                    }
                }
            }
        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4Ugc(type: Int, platform: String): Int {
        when (type) {
            GlobalShareState.TYPE_NORMAL -> {
                when (platform) {
                    SharePlatform.PLATFORM_TIKTOK -> {
                        return GlobalShareViewModel.CONFIG_NOTHING
                    }

                    SharePlatform.PLATFORM_SNAPCHAT -> {
                        return GlobalShareViewModel.CONFIG_LINK
                    }
                }
            }

            GlobalShareState.TYPE_LONG_IMAGE -> {
                when (platform) {
                    SharePlatform.PLATFORM_TIKTOK,
                    SharePlatform.PLATFORM_SNAPCHAT -> {
                        return GlobalShareViewModel.CONFIG_NOTHING
                    }
                }
            }
        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4Post(type: Int, platform: String): Int {
        when (platform) {
            SharePlatform.PLATFORM_TIKTOK,
            SharePlatform.PLATFORM_SNAPCHAT -> {
                return GlobalShareViewModel.CONFIG_NOTHING
            }
        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4Video(type: Int, platform: String): Int {
        when (platform) {
            SharePlatform.PLATFORM_YOUTUBE,
            SharePlatform.PLATFORM_TIKTOK -> {
                return GlobalShareViewModel.CONFIG_NOTHING
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                return GlobalShareViewModel.CONFIG_LINK
            }
        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4OcMoment(type: Int, platform: String): Int {
        when (platform) {
            SharePlatform.PLATFORM_YOUTUBE -> {
                return GlobalShareViewModel.CONFIG_TTAI
            }

            SharePlatform.PLATFORM_TIKTOK -> {
                return if (PandoraToggleWrapper.tiktokShareWithTags) {
                    GlobalShareViewModel.CONFIG_TTAI
                } else {
                    GlobalShareViewModel.CONFIG_NOTHING
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                return if (PandoraToggleWrapper.snapchatShareWithSticker) {
                    GlobalShareViewModel.CONFIG_TTAI_TRANSFORM
                } else {
                    GlobalShareViewModel.CONFIG_NOTHING
                }
            }
        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4Screenshot(type: Int, platform: String): Int {
        when (platform) {
            SharePlatform.PLATFORM_TIKTOK -> {
                return if (PandoraToggleWrapper.tiktokShareWithTags) {
                    GlobalShareViewModel.CONFIG_TTAI
                } else {
                    GlobalShareViewModel.CONFIG_NOTHING
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                return if (PandoraToggleWrapper.snapchatShareWithSticker) {
                    GlobalShareViewModel.CONFIG_TTAI_TRANSFORM
                } else {
                    GlobalShareViewModel.CONFIG_NOTHING
                }
            }
        }
        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun ttaiConfigTransformer(
        context: Context,
        platform: String,
        shareConfig: ShareConfig
    ): ShareConfig {
        return when (platform) {
            SharePlatform.PLATFORM_SNAPCHAT -> {
                shareConfig.copy(snapchat = shareConfig.snapchat?.validate(context))
            }

            else -> {
                shareConfig
            }
        }
    }


    fun getProfileTitleContent(
        context: Context,
        config: ShareConfig?,
        nickname: String
    ): Pair<String, String> {
        return context.getString(
            R.string.global_share_content_2,
            nickname
        ) to context.getString(
            R.string.global_share_content_3
        )
    }
}