package com.socialplay.gpark.function.overseabridge.bridge

import android.app.Activity
import android.content.Intent
import android.net.Uri
import org.koin.core.context.GlobalContext

object DynamicLinksWrapper {
    fun parseDynamicLinks(intent: Intent, activity: Activity, succeed: (Uri?) -> Unit, failed: (Exception) -> Unit) {
        GlobalContext.get().get<IFirebaseSdkBridge>().parseDynamicLinks(intent, activity, succeed, failed)
    }

}