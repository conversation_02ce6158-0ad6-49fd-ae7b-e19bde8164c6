package com.socialplay.gpark.function.store

import androidx.fragment.app.Fragment
import com.socialplay.gpark.function.overseabridge.bridge.IGoogleSdkBridge
import org.koin.core.context.GlobalContext

class StoreInAppReview {

    fun showInAppReview(fragment: Fragment, showSuccessCallback: (Boolean) -> Unit) {
        GlobalContext.get().get<IGoogleSdkBridge>().showInAppReview(fragment, showSuccessCallback)
    }
}