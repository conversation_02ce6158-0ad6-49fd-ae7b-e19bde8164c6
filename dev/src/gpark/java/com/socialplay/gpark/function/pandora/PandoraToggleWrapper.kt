package com.socialplay.gpark.function.pandora

import androidx.annotation.Keep
import com.meta.pandora.Pandora
import com.socialplay.gpark.function.developer.DeveloperPandoraToggle

@Keep
object PandoraToggleWrapper {

    const val TIKTOK_SHARE_SYSTEM = 1
    const val TIKTOK_SHARE_OLD_SDK = 2
    const val TIKTOK_SHARE_NEW_SDK = 3

    @DevPandoraToggle(
        name = "tiktok分享模式开关",
        desc = "${TIKTOK_SHARE_SYSTEM}=系统," +
                "${TIKTOK_SHARE_OLD_SDK}=老sdk," +
                "${TIKTOK_SHARE_NEW_SDK}=新sdk",
        defValue = TIKTOK_SHARE_SYSTEM.toString()
    )
    private const val CONTROL_SHARE_TIKTOK = "control_share_tiktok"
    val tiktokShareVersion by lazyGetValue(CONTROL_SHARE_TIKTOK, TIKTOK_SHARE_SYSTEM)
    val tiktokShareWithTags get() = tiktokShareVersion == TIKTOK_SHARE_OLD_SDK


    const val SNAPCHAT_SHARE_SYSTEM = 1
    const val SNAPCHAT_SHARE_SDK = 2

    @DevPandoraToggle(
        name = "snapchat分享模式开关",
        desc = "${SNAPCHAT_SHARE_SYSTEM}=系统," +
                "${SNAPCHAT_SHARE_SDK}=sdk",
        defValue = SNAPCHAT_SHARE_SYSTEM.toString()
    )
    private const val CONTROL_SHARE_SNAPCHAT = "control_share_snapchat"
    val snapchatShareVersion by lazyGetValue(CONTROL_SHARE_SNAPCHAT, SNAPCHAT_SHARE_SYSTEM)
    val snapchatShareWithSticker get() = snapchatShareVersion == SNAPCHAT_SHARE_SDK

    val enableAvatarPopup = true

    @DevPandoraToggle(name = "订阅开关", desc = "默认：开 1", defValue = "1")
    private const val CONTROL_VIP_PLUS = "control_iap_premium"
    val IAP_PREMIUM by lazyGetValue(CONTROL_VIP_PLUS, "1")
    fun isVipPlusOpen(): Boolean {
        return IAP_PREMIUM == "1"
    }

    fun isVipStatusOpen(): Boolean {
        return IAP_PREMIUM == "2"
    }

    private inline fun <reified T> lazyGetValue(key: String, default: T): Lazy<T> {
        return lazy { getValue(key, default) }
    }

    /**
     * 获取开关的值
     * @param key 开关Key
     * @param defaultValue 默认开关值
     * @return T 获取开关的结果
     *
     */
    private fun <T> getValue(key: String, defaultValue: T): T {
        return if (DeveloperPandoraToggle.isEnable()) {
            DeveloperPandoraToggle.getValue(key, defaultValue) ?: defaultValue
        } else {
            Pandora.getAbConfig(key, defaultValue)
        }
    }
}