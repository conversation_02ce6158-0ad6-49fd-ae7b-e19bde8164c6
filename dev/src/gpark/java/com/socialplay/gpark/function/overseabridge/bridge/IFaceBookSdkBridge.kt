package com.socialplay.gpark.function.overseabridge.bridge

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle

interface IFaceBookSdkBridge {

    fun isEnable(): <PERSON><PERSON>an {
        return true
    }
    fun init(application: Application)
    fun initAnalytics(application: Application, debug: Boolean)
    fun logEvent(kind: String, params: Bundle)
    fun debugLogEvent(context: Context, kind: String)

    fun createAuth(): IAuthBridge
    fun logOut()
    fun share(context: Context, videoPath: Uri, shareText: String)

    interface IAuthBridge {
        fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?)
        fun login(activity: Activity, callback: IAuthResultCallback)
        fun isExpired(): Boolean?
        fun onDestroy()
    }


    interface IAuthResultCallback {
        fun onSuccess(token: String)

        fun onCancel()

        fun onError(error: Exception)

        fun onGetProfileSucceed(token: String, userId: String, genderInt: Int, birthday: String, name: String, picture: String)
    }
}