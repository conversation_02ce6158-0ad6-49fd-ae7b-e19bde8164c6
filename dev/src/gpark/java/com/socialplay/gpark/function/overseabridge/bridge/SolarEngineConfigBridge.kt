package com.socialplay.gpark.function.overseabridge.bridge

/*
*
*    https://help.solar-engine.com/cn/docs/wu-biao-ti-wen-zhang
*
*    logEnabled	void	否	是否开启本地调试日志，默认为不开启
*    enable2GReporting	boolean	否	是否在2G网络时上报事件，默认为不上报
*    isDebugModel	boolean	否	是否开启 Debug 模式，默认为不开启，使用前请查看功能说明
*    isGDPRArea	boolean	否	如果你的应用在欧盟地区运营，则需要符合欧盟隐私保护法律的规定（关于GDPR），请务必在用户拒绝采集设备敏感信息时设置 isGDPRArea(true) ，默认为采集
*    adPersonalizationEnabled	boolean	否	如果你的应用在欧盟地区运营并且在Google投放您的应用，请务必将用户是否允许Google将其数据用于个性化广告的意见结果传入该属性，以确保您符合Google对欧盟用户意见征求政策的新政策
*    adUserDataEnabled	boolean	否	如果你的应用在欧盟地区运营并且在Google投放您的应用，请务必将用户是否同意将其数据发送到Google的意见结果传入该属性，以确保您符合Google对欧盟用户意见征求政策的新政策
*    setCoppaEnabled	boolean	否	如果您的应用需要符合《儿童在线隐私权保护法》(COPPA) 规定，设置 setCoppaEnabled = true
*    setKidsAppEnabled	boolean	否	如果您的应用会定向到不满 13 周岁的儿童，则需要将其标记为儿童应用 (Kids App)，设置 setKidsAppEnabled = true
*    setFbAppID String	否	如果海外开发者需要用到meta归因，此处设置meta appid
*
* */
data class SolarEngineConfigBridge(
    val logEnabled: Boolean = false,
    val enable2GReporting: Boolean = false,
    val isDebugModel: Boolean = false,
    val isGDPRArea: Boolean = false,
    val adPersonalizationEnabled: Boolean = false,
    val adUserDataEnabled: Boolean = false,
    val coppaEnabled: Boolean = false,
    val kidsAppEnabled: Boolean = false,
    /*没有作用，Product环境会自动设置生产的FaceBook AppId}*/
    val fbAppID: String? = null,
    val attributionListener: ISolarEngineBridge.OnAttributionListener? = null
)