package com.socialplay.gpark.function.analytics.kernel

import android.app.Application
import com.meta.pandora.PandoraConfig
import com.meta.pandora.data.entity.Event
import com.meta.pandora.data.entity.ImmutableParams
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.interactor.DeviceInteractorWrapper
import com.socialplay.gpark.data.interactor.SolarEngineInteractor
import com.socialplay.gpark.di.CommonParamsProvider
import org.koin.core.context.GlobalContext

object PandoraInitWrapper {
    private val solarEngineInteractor by lazy { GlobalContext.get().get<SolarEngineInteractor>() }
    private var openFacebookAnalytics = BuildConfig.OPEN_FACEBOOK_ANALYTICS
    private var openFirebaseAnalytics = BuildConfig.OPEN_FIREBASE_ANALYTICS

    fun getABPublicParams(commonParamsProvider: CommonParamsProvider): MutableMap<String, Any> {
        val params = HashMap<String, Any>()
        params.put("firebase_id", DeviceInteractorWrapper.firebaseId)
        params.put("googlead_id", DeviceInteractorWrapper.googleAdId)
        params.put("user_pseudo_id", DeviceInteractorWrapper.user_pseudo_id)
        val solarEngineParams = solarEngineInteractor.collectEventsConversionData.value ?: emptyMap()
        solarEngineParams.forEach { entry -> params.put(entry.key, entry.value) }
        return params
    }

    fun getPublicParams(commonParamsProvider: CommonParamsProvider): MutableMap<String, Any> {
        val params = HashMap<String, Any>()
        params.put("firebase_id", DeviceInteractorWrapper.firebaseId)
        params.put("googlead_id", DeviceInteractorWrapper.googleAdId)
        params.put("user_pseudo_id", DeviceInteractorWrapper.user_pseudo_id)
        val solarEngineParams = solarEngineInteractor.collectEventsConversionData.value ?: emptyMap()
        solarEngineParams.forEach { entry -> params.put(entry.key, entry.value) }
        return params
    }

    fun getServer(): PandoraConfig.Server {
        return PandoraConfig.Server.GLOBAL
    }

    fun onParamsAsciiChecked(event: Event, params: ImmutableParams) {
        val originParams = params.unboxing()
        if (openFacebookAnalytics) {
            FacebookAnalytics.track(Event("gpark_" + event.kind), originParams)
        }
        if (openFirebaseAnalytics) {
            FirebaseAnalyticsWrapper.track(Event("gpark_" + event.kind), originParams)
        }

        SolarEngineAnalyticsWrapper.track(event, originParams)
    }

    fun init(context: Application) {
        if (openFacebookAnalytics) {
            FacebookAnalytics.init(context)
        }
        if (openFirebaseAnalytics) {
            FirebaseAnalyticsWrapper.init()
        }
    }
}

fun PandoraConfig.Builder.setFlavorConfig(): PandoraConfig.Builder {
    return this.enableParamsAsciiChecking(PandoraInitWrapper::onParamsAsciiChecked)
}