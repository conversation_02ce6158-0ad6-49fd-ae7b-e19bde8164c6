package com.socialplay.gpark.function.overseabridge.bridge

import android.app.Activity
import android.net.Uri
import com.socialplay.gpark.data.model.share.ShareData

interface ITiktokSdkBridge {

    fun isEnable(): Boolean {
        return true
    }
    fun init()

    fun share(videoUri: Uri, context: Activity, shareText: String)

    fun share(context: Activity, shareData: ShareData): <PERSON><PERSON><PERSON>
}