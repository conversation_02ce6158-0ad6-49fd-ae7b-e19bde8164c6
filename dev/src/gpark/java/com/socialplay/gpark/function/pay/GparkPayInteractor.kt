package com.socialplay.gpark.function.pay

import android.app.Activity
import android.app.Application
import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.bin.cpbus.CpEventBus
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.BodyRequestOrder
import com.socialplay.gpark.data.model.BodyRequestOrderRuntime
import com.socialplay.gpark.data.model.Product
import com.socialplay.gpark.data.model.TTaiConfig
import com.socialplay.gpark.data.model.event.PayResult
import com.socialplay.gpark.data.model.member.MemberRequest
import com.socialplay.gpark.data.model.member.MemberType
import com.socialplay.gpark.data.model.pay.CoinType
import com.socialplay.gpark.data.model.pay.CommonPayParams
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.data.model.pay.IAPConstants.IAP_SCENE_VIP_PLUS
import com.socialplay.gpark.data.model.pay.IAPConstants.IAP_SCENE_VIP_PLUS_RENEW
import com.socialplay.gpark.data.model.pay.PayParams
import com.socialplay.gpark.data.model.pay.SubsData
import com.socialplay.gpark.data.model.pay.UserBalance
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.overseabridge.bridge.IGoogleSdkBridge
import com.socialplay.gpark.function.pay.way.BasePayPlatform
import com.socialplay.gpark.function.pay.way.GooglePayPlatform
import com.socialplay.gpark.function.pay.way.IPayCallback
import com.socialplay.gpark.function.pay.way.SimulationPlatform
import com.socialplay.gpark.function.startup.core.ProcessType
import com.socialplay.gpark.ui.gamepay.PayChannel
import com.socialplay.gpark.ui.gamepay.PayInfo
import com.socialplay.gpark.ui.gamepay.PayType
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.toLongOrZero
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import org.greenrobot.eventbus.Subscribe
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.UUID
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.abs

class GparkPayInteractor(
    val metaRepository: com.socialplay.gpark.data.IMetaRepository,
    val metaApp: Application,
    val accountInteractor: AccountInteractor,
    val metaKV: MetaKV
) : IPayInteractor {
    private val TAG = "GparkPayInteractor"
    private val scopeIO = CoroutineScope(Dispatchers.IO)

    private val billingClientBridge: IGoogleSdkBridge.IBillingClientBridge by lazy {
        GlobalContext.get().get<IGoogleSdkBridge>().billingClientBridge
    }

    override val products: LiveData<MutableList<Product>>
        get() = billingClientBridge.productLiveData

    /**
     * 订阅商品列表
     */
    override val subsProducts: LiveData<MutableList<Product>>
        get() = billingClientBridge.subsProductLiveData

    //pg币产品ID列表
    private val pgCoinProductIds = metaRepository.getTTaiConfigById(20301)

    private val scope = CoroutineScope(Dispatchers.IO)

    private val _userBalance = MutableLiveData<UserBalance?>()
    override val userBalance: LiveData<UserBalance?> = _userBalance
    private var payPlatformMap: MutableMap<Int, BasePayPlatform<PayParams>>? = null

    //支付中
    private var isPayIng: AtomicBoolean = AtomicBoolean(false)
    private var lastUser: MetaUserInfo? = null
    private val accountChangeListener = Observer<MetaUserInfo?> {
        if (lastUser?.uuid != it?.uuid) {
            scope.launch {
                getBalance(null)
            }
            lastUser = it
        }
    }
    private var mPayResultCallback: ((payResult: PayResult) -> Unit)? = null

    override val supportPointsPayment: Boolean = true

    override suspend fun init(processType: ProcessType) {
        initPlatform()
        initData()
        connectGooglePay()

    }

    /**
     * 支付平台注册，目前是模拟支付和google支付
     */
    private fun initPlatform() {
        registerPayPlatform(GooglePayPlatform())
        registerPayPlatform(SimulationPlatform())
    }

    private suspend fun initData() {
        withContext(Dispatchers.Main) {
            accountInteractor.accountLiveData.observeForever(accountChangeListener)
        }
        getBalance(null)
        CpEventBus.register(this)
    }

    /**
     * 根据业务类型获取TTai配置的产品ID
     */
    private fun getProductIdsByIapScene(iapScene: String): Flow<DataResult<TTaiConfig>> {
        //根据业务类型进行产品加载
        return if (iapScene == IAPConstants.IAP_SCENE_PG_COIN) {
            pgCoinProductIds
        } else {
            flow { emit(DataResult.Error(-1000, "not_product")) }
        }
    }

    private fun getProductType(iapScene: String): String {
        return if (iapScene == IAPConstants.IAP_SCENE_VIP_PLUS || iapScene == IAP_SCENE_VIP_PLUS_RENEW) {
            Product.SUBS
        } else {
            Product.INAPP
        }
    }

    /**
     * 检查google链接状态
     */
    fun checkGoogleConnect() {
        connectGooglePay()
        scope.launch {
            billingClientBridge.checkConnected()
        }
    }

    fun queryPurchasesAsync() {
        billingClientBridge.queryPurchasesAsync()
    }

    override suspend fun loadProducts(
        scene: String,
        list: List<SubsData>?,
        callBack: (Pair<String?, MutableList<Product>>) -> Unit?
    ) {
        if (!IAPConstants.isSceneSupport(scene)) {
            Timber.tag(TAG).e("loadProducts-unknown iap scene")
            return
        }

        billingClientBridge.checkConnected()
        if (!list.isNullOrEmpty()) {
            //特定的商品信息查询
            billingClientBridge.queryProductDetailsAsync(list, getProductType(scene), callBack)
            Timber.tag(TAG).d("loadProducts-queryProductDetailsAsync")
            return
        }
        Timber.tag(TAG).d("loadProducts-getProductIdsByIapScene")
        //根据业务类型进行产品加载
        getProductIdsByIapScene(scene).collectLatest { tResult ->
            if (!tResult.succeeded) {
                billingClientBridge.clearProducts()
                Timber.tag(TAG).d("loadProducts-getProductIdsByIapScene-result-failed")
                return@collectLatest
            }

            Timber.tag(TAG).i("ttaiconfig=${tResult.data?.value}")

            val list: List<String> =
                GsonUtil.gsonSafeParseCollection(tResult.data?.value) ?: return@collectLatest
            val dataList = list.map { SubsData(it, null) }
            Timber.tag(TAG).d("loadProducts-queryProductDetailsAsync")
            billingClientBridge.queryProductDetailsAsync(dataList, getProductType(scene), callBack)
        }
    }

    override fun getRechargeViewData(
        balance: Long,
        rechargePayInfo: PayInfo,
        callback: (DataResult<RechargeViewData>) -> Unit
    ) {
        Timber.tag(TAG).d("getRechargeViewData")
        val request = MemberRequest(arrayListOf(MemberType.VIP_PLUS))
        scopeIO.launch {
            combine(
                metaRepository.getSubsProduct(IAPConstants.IAP_PRODUCT_TYPE_PG_COIN),
                metaRepository.getUserMemberInfoList(request)
            ) { productsResult, userInfoResult ->
                Timber.tag(TAG).d("getRechargeViewData-request-response")
                if (!productsResult.succeeded || productsResult.data == null) {
                    callback(DataResult.Error(productsResult.code ?: 0, productsResult.message ?: ""))
                    Timber.tag(TAG).d("getRechargeViewData-request-productsResult-failed")
                    return@combine
                }
                if (!userInfoResult.succeeded || userInfoResult.data == null) {
                    callback(DataResult.Error(userInfoResult.code ?: 0, userInfoResult.message ?: ""))
                    Timber.tag(TAG).d("getRechargeViewData-request-userInfoResult-failed")
                    return@combine
                }
                val needAdditional = (rechargePayInfo.payAmount - balance).coerceAtLeast(0L)

                val productsInfos = productsResult.data!!.sortedBy { productInfo ->
                    productInfo.coinInfo?.baseLecoinNum.toLongOrZero
                }
                // 选择充值项
                var targetProductInfo = productsInfos.firstOrNull { productInfo ->
                    val baseLecoinNum = productInfo.coinInfo?.baseLecoinNum.toLongOrZero
                    val awardCoinNum = productInfo.coinInfo?.awardLecoinNum.toLongOrZero
                    val memberRewardCoinNum = productInfo.memberInfo?.rewardCoinNum.toLongOrZero
                    baseLecoinNum + awardCoinNum + memberRewardCoinNum >= needAdditional
                }
                if (targetProductInfo == null && productsInfos.isNotEmpty()) {
                    targetProductInfo = productsInfos.last()
                }
                if (targetProductInfo == null) {
                    val context = GlobalContext.get().get<Context>()
                    callback(DataResult.Error(0, context.getString(R.string.iap_recharge_toast_get_products_from_server_failed)))
                    Timber.tag(TAG).d("getRechargeViewData-rechargeResult list is empty")
                    return@combine
                }
                // 是否是会员
                val isMember = userInfoResult.data!!.firstOrNull()?.isActive() ?: false

                var loadProductsSuccess = false
                Timber.tag(TAG).d("getRechargeViewData-loadProducts")
                loadProducts(
                    IAPConstants.IAP_SCENE_PG_COIN,
                    listOf(SubsData(targetProductInfo.productId)),
                ) { result ->
                    Timber.tag(TAG).d("loadProducts-result-loadProductsSuccess=${loadProductsSuccess}")
                    if (!loadProductsSuccess) {
                        loadProductsSuccess = true
                        Timber.tag(TAG).d(
                            "loadProducts-result-targetProductId=${targetProductInfo.productId} and callbackData=${ GsonUtil.safeToJson(result) }"
                        )
                        val priceProduct =
                            result.second.firstOrNull { it.productId == targetProductInfo.productId }
                        Timber.tag(TAG).d("loadProducts-result-priceProduct=${priceProduct}")
                        if (priceProduct == null) {
                            scopeIO.launch {
                                Timber.tag(TAG).d("loadProducts-result: cannot find target product")
                                val context = GlobalContext.get().get<Context>()
                                callback(DataResult.Error(0, context.getString(R.string.iap_recharge_toast_get_product_from_google_failed)))
                            }
                        } else {
                            scopeIO.launch {
                                Timber.tag(TAG).d("loadProducts-result-callback-success")
                                callback(
                                    DataResult.Success(
                                        RechargeViewData(
                                            isMember = isMember,
                                            productId = targetProductInfo.productId,
                                            parentProductId = targetProductInfo.parentProductId ?: "",
                                            needAdditional = needAdditional,
                                            baseCoinNum = targetProductInfo.coinInfo?.baseLecoinNum.toLongOrZero,
                                            awardCoinNum = targetProductInfo.coinInfo?.awardLecoinNum.toLongOrZero,
                                            memberRewardCoinNum = targetProductInfo.memberInfo?.rewardCoinNum.toLongOrZero,
                                            currencyCode = priceProduct.priceCurrencyCode,
                                            price = priceProduct.realPrice.toInt(),
                                            currencyCodePrice = priceProduct.price,
                                            showPrivacy = false,
                                        )
                                    )
                                )
                            }
                        }
                    }
                    null
                }
                delay(5000)
                // 5 秒过后, 如果还是找不到商品, 就回调失败
                if (!loadProductsSuccess) {
                    Timber.tag(TAG).d("loadProducts-result: load products from google failed")
                    val context = GlobalContext.get().get<Context>()
                    callback(DataResult.Error(0, context.getString(R.string.iap_recharge_toast_get_product_from_google_failed)))
                }
            }.stateIn(scopeIO, SharingStarted.Eagerly, false)
        }
    }

    override fun loadRechargeProducts(callback:(DataResult<List<RechargeProductCompat>>) -> Unit) {
        scopeIO.launch {
            metaRepository.getSubsProduct(IAPConstants.IAP_PRODUCT_TYPE_PG_COIN).collect {
                if (it.succeeded && it.data != null) {
                    val originProducts = it.data!!.filter {
                        product-> product.show ?: true
                    }
                    val rechargeProducts1 = originProducts.map { product->
                        RechargeProductCompat(
                            ourProductId = product.id ?: "",
                            productId = product.productId,
                            parentProductId = product.productId,
                            baseCoinNum = product.coinInfo?.baseLecoinNum.toLongOrZero,
                            awardCoinNum = product.coinInfo?.awardLecoinNum.toLongOrZero,
                            memberRewardCoinNum = product.memberInfo?.rewardCoinNum.toLongOrZero,
                            currencyCode = "",
                            price = 0,
                            currencyCodePrice = "",
                        )
                    }
                    // 先给一次回调, 让界面先显示数据, 商品的价格要去google查询, 当网络比较差时 google 查询商品的价格比较慢
                    callback(DataResult.Success(rechargeProducts1))

                    val subsDataList =
                        originProducts.map { product -> SubsData(product.productId, null) }

                    Timber.tag(TAG).d("getRechargeViewData-loadProducts")
                    var loadProductsSuccess = false
                    loadProducts(IAPConstants.IAP_SCENE_PG_COIN, subsDataList) { result ->
                        Timber.tag(TAG)
                            .d("loadProducts-result-loadProductsSuccess=${loadProductsSuccess}")
                        if (!loadProductsSuccess) {
                            loadProductsSuccess = true
                            Timber.tag(TAG).d(
                                "loadProducts-result-callbackData=${ GsonUtil.safeToJson(result) }"
                            )
                            val productMap = originProducts.groupBy { product -> product.productId }
                            val rechargeProducts = result.second.map { googleProduct ->
                                val targetProduct =
                                    productMap[googleProduct.productId]?.firstOrNull()
                                RechargeProductCompat(
                                    ourProductId = targetProduct?.id ?: "",
                                    productId = targetProduct?.productId ?: "",
                                    parentProductId = targetProduct?.productId ?: "",
                                    baseCoinNum = targetProduct?.coinInfo?.baseLecoinNum.toLongOrZero,
                                    awardCoinNum = targetProduct?.coinInfo?.awardLecoinNum.toLongOrZero,
                                    memberRewardCoinNum = targetProduct?.memberInfo?.rewardCoinNum.toLongOrZero,
                                    currencyCode = googleProduct.priceCurrencyCode,
                                    price = googleProduct.realPrice.toInt(),
                                    currencyCodePrice = googleProduct.price,
                                )
                            }
                            if (rechargeProducts.isEmpty()) {
                                // 当连不上google商店时, 无法查询到价格
                                // 上面已经回调了一次商品列表了, 这里就不用再次回调了.
                                Timber.tag(TAG).d(
                                    "loadProducts-result-get recharge products price failed"
                                )
                            } else {
                                callback(DataResult.Success(rechargeProducts))
                            }
                        }
                        null
                    }
                } else {
                    callback(DataResult.Error(it.code ?: 0, it.message ?: ""))
                }
            }
        }
    }

    /**
     * 支付平台注册
     */
    private fun registerPayPlatform(platform: BasePayPlatform<PayParams>) {
        if (payPlatformMap == null) {
            payPlatformMap = HashMap()
        }
        if (payPlatformMap?.containsKey(platform.platformType()) != true) {
            payPlatformMap?.put(platform.platformType(), platform)
            platform.setPayCallback(getIPayCallback())
        }
    }

    private fun connectGooglePay() {
        //请求连接到GooglePay
        billingClientBridge.init(metaApp, true)
    }

    private fun onPayResult(
        payParams: PayParams,
        isSuccess: Boolean,
        failedCode: Int,
        failedMsg: String? = null,
        coinsBalance: Long? = null,
    ) {
        // 重新消耗的订单结果不属于本次支付
        val isConsumeAgain = payParams.googleResultData?.consumeAgain == true
        if (!isConsumeAgain) {
            isPayIng.set(false)
        }
        val map = hashMapOf<String, Any>()
        map["source"] = payParams.currentSource ?: ""
        map["page_type"] = payParams.pageType.orEmpty()
        map["gamecode"] = payParams.currentGameId ?: ""
        map["orderid"] = payParams.currentOrderId ?: ""
        map["productid"] = payParams.productId ?: ""
        map["product"] = payParams.currentRequestBody?.productName ?: ""
        map["currencycode"] = payParams.currentRequestBody?.payUnit ?: ""
        map["price"] = payParams.currentRequestBody?.productPrice ?: ""
        map["result"] = if (isSuccess) "1" else "2"
        map["consume_again"] = payParams.googleResultData?.consumeAgain ?: false
        if (!isSuccess) {
            map["failedcode"] = failedCode.toString()
            map["failedmsg"] = failedMsg ?: ""
        }

        map["service_orderid"] = payParams.googleResultData?.googleOrderId ?: ""
        map["purchasetype"] = payParams.tripartiteInfo?.purchaseType ?: ""
        if (payParams.productType == Product.SUBS) {
            subsProducts.value?.find { it.productId == payParams.productId }?.let {
                map["baseid"] = it.subscriptionOfferDetails?.baseid ?: ""
                if (it.subscriptionOfferDetails?.originalPrice != it.subscriptionOfferDetails?.salePeriod) {
                    map["offerid"] = it.subscriptionOfferDetails?.offerid ?: ""
                }
                map["billing_period"] = it.subscriptionOfferDetails?.period ?: ""

                map["user_substate"] =
                    if (payParams.currentIapScene == IAP_SCENE_VIP_PLUS) "empty" else "renew"
            }

            map["emailAddress"] = payParams.tripartiteInfo?.emailAddress ?: ""
            map["bonus_gcoin"] = _userBalance.value?.leCoinAwardNum ?: 0
            Analytics.track(EventConstants.EVENT_SUBSCRIBE_PAY_RESULT, map)
        } else {
            map["base_gcoin"] = _userBalance.value?.leCoinBaseNum ?: 0
            map["bonus_gcoin"] = _userBalance.value?.leCoinAwardNum ?: 0
            Analytics.track(EventConstants.EVENT_PAY_RESULT, map)
        }
        if (payParams.googleResultData?.needUpdate == true || !isSuccess) {
            //需要通知支付结果
            sendTimeEvent(payParams, isSuccess, false)
            // 重新消耗的订单结果不属于本次支付
            if (!isConsumeAgain) {
                payPlatformMap?.get(payParams.payChannel)?.setAgentPayParams(null)
            }
        }
        // 重新消耗的订单结果不属于本次支付
        if (!isConsumeAgain) {
            mPayResultCallback?.invoke(
                PayResult(
                    isSuccess,
                    failedMsg ?: "",
                    payParams.currentIapScene,
                    failedCode,
                    payParams.currentRequestBody?.amount ?: 0,
                    coinsBalance = coinsBalance
                )
            )
            mPayResultCallback = null
        }
    }

    /**
     * 整个支付流程埋点
     */
    private fun sendTimeEvent(payParams: PayParams, isSuccess: Boolean, isShow: Boolean) {
        val map = hashMapOf<String, Any>()
        map["gamecode"] = payParams.currentGameId ?: ""
        map["success"] = isSuccess
        map["time"] =
            System.currentTimeMillis() - (payParams.startTime ?: System.currentTimeMillis())
        if (isShow) {
            Analytics.track(EventConstants.EVENT_IAP_PAY_DIALOG_SHOW, map)
        } else {
            Analytics.track(EventConstants.EVENT_IAP_PAY_ALL_TIME, map)
        }
    }


    /**
     * 把当前购买的商品信息存储到服务端
     */
    private suspend fun submit(payParams: PayParams) {
        val map = HashMap<String, Any>()
        map["channel"] = payParams.payChannel ?: ""
        map["sceneCode"] = payParams.sceneCode
        map["nativeOrderNo"] = payParams.googleResultData?.orderId ?: ""
        if (payParams.payChannel == IAPConstants.PAY_CHANNEL_GOOGLE) {
            val jo =
                GsonUtil.gsonSafeParseCollection<HashMap<String, Any>>(payParams.googleResultData?.originalJson)
            jo?.let {
                // 赋值productId的代码感觉可以删掉
                // 如果说当前 productId 和实际的 productId, 那代表当前的支付的订单和google实际支付的结果不一致, 可能是历史未消耗订单重新消耗
                it["productId"] = payParams.currentRequestBody?.productCode ?: ""
                map["googlePaymentVoucher"] = it
            }
        }
        Timber.d("onConsumeGood_submit %s ", payParams)
        metaRepository.payResultSubmit(map).collect {
            if (it.succeeded) {
                payParams.currentOrderId = it.data?.orderNo ?: ""
                withContext(Dispatchers.IO) {
                    payPlatformMap?.get(payParams.payChannel)?.consumeGoods(
                        payParams.productType,
                        payParams.googleResultData?.purchaseToken ?: ""
                    )
                }
                if (payParams.googleResultData?.needUpdate == true) {
                    getOrderResult(payParams)
                } else {
                    getBalance(null) { userBalance ->
                        //不需要更新页面，消耗成功后也需要上报埋点
                        onPayResult(payParams, true, 200, null, coinsBalance = userBalance?.leCoinNum)
                    }
                }
            } else {
                onPayResult(payParams, false, it.code ?: 0, it.message)
            }
        }
    }

    private suspend fun getOrderResult(payParams: PayParams) {
        val isSuccess = repeatGetResult(payParams.currentOrderId ?: "")
        val reason = if (isSuccess) {
            "OK"
        } else {
            // pay_success_but_loop_get_result_error
            ""
        }

        if (isSuccess) {
            //发货有延时，延时1.2秒
            delay(1200)
            payParams.currentOrderId?.let {
                combine(
                    metaRepository.getTripartiteInfo(
                        payParams.googleResultData?.googleOrderId ?: ""
                    ), metaRepository.getBalance(CoinType.gParkCoin)
                ) { tripartiteInfo, balance ->
                    tripartiteInfo.data to balance
                }.collect {
                    updateBalance(0, it.second, null)
                    payParams.tripartiteInfo = it.first
                    onPayResult(payParams, true, 200, reason, coinsBalance = it.second.data?.leCoinNum)
                }
            }
        } else {
            //查询订单结果失败
            onPayResult(payParams, false, IPayInteractor.FAIL_LOOPER_ORDER, reason)
        }
    }

    private suspend fun repeatGetResult(orderId: String): Boolean {
        var result: Boolean? = false
        var retryTimes = 0
        //重试间隔
        val retryIntervals =
            listOf<Long>(0L, 1500L, 1500L, 2000L, 2000L, 3000L, 3000L, 5000L, 5000L, 5000L)
        val countTimes = retryIntervals.size
        while (retryTimes < countTimes) {
            delay(retryIntervals[retryTimes])
            Timber.i("round ${retryTimes + 1}, interval ${retryIntervals[retryTimes] / 1000.0f}s")
            result = withTimeoutOrNull(1000) {
                metaRepository.rechargingLoop(orderId)
            }
            if (result != true) {
                retryTimes += 1
                Timber.i("polling result： ${result}, one more time")
            } else {
                Timber.i("polling end:$result")
                break
            }
        }
        return result == true
    }

    override fun getPayChannel(payType: PayType): Int? {
        return when (payType) {
            // Gpark币支付
            PayType.COINS -> {
                67
            }
            // 积分支付
            PayType.POINTS -> {
                74
            }
        }
    }

    override fun startPay(
        activity: Activity,
        commonPayParams: CommonPayParams,
        extra:Any?,
        pageSource: String?,
        payResultCallback: ((payResult: PayResult) -> Unit)?
    ) {
        if (isPayIng.get()) {
            Timber.e("pay is ing")
            return
        }
        mPayResultCallback = payResultCallback
        //检查l
        isPayIng.set(true)
        checkGoogleConnect()
        queryPurchasesAsync()
        val productId =
            if (commonPayParams.parentProductId.isNullOrEmpty()) commonPayParams.productId
                ?: "" else commonPayParams.parentProductId
        createRequestParams(
            productId,
            commonPayParams.scene,
            commonPayParams.source,
            commonPayParams.pageType,
            commonPayParams.gameId,
            commonPayParams.productId,
        ) { payParams, message, code ->
            if (productId.isNullOrEmpty()) {
                //传入参数productId 为空
                onPayResult(
                    payParams,
                    false,
                    IPayInteractor.FAIL_OTHER,
                    code.toString() + " product id is empty"
                )
                return@createRequestParams
            }
            if (payParams.currentRequestBody == null) {
                //当前请求参数为空
                onPayResult(
                    payParams,
                    false,
                    IPayInteractor.FAIL_OTHER,
                    code.toString() + " " + message
                )
                return@createRequestParams
            }
            scope.launch {
                metaRepository.privilegePlaceOrder(payParams.currentRequestBody!!)
                    .collect {
                        if (it.succeeded) {
                            payParams.takeOderResult = it.data
                            payParams.currentOrderId = it.data?.orderCode
                            startThirdPay(activity, payParams)
                        } else {
                            onPayResult(payParams, false, it.code ?: 0, it.message ?: "")
                        }
                    }
            }

        }

    }

    private fun startThirdPay(activity: Activity, payParams: PayParams) {
        val platform = payPlatformMap?.get(payParams.payChannel)
        if (platform == null) {
            onPayResult(payParams, false, IPayInteractor.FAIL_OTHER)
        } else {
            platform.setAgentPayParams(payParams)
            platform.startPay(activity, payParams)
        }
    }

    private fun createRequestParams(
        productId: String,
        scene: String,
        source: String? = null,
        pageType: String? = null,
        gameId: String? = null,
        planId: String? = null,
        callback: (PayParams, String?, Int?) -> Unit
    ) {
        val startTime = System.currentTimeMillis()
        val payParams = PayParams()
        payParams.startTime = startTime
        payParams.payChannel = IAPConstants.getPayChannel()
        payParams.currentIapScene = scene
        payParams.sceneCode = IAPConstants.getSceneCodeOrDefault(scene)
        payParams.currentSource = source
        payParams.pageType = pageType
        payParams.currentGameId = gameId
        payParams.productType = getProductType(scene)
        payParams.uuid = metaKV.account.uuid
        payParams.planId = planId
        payParams.productId = productId
        if (!productId.isNullOrEmpty()) {
            billingClientBridge.getRequestOrderBody(
                productId,
                1,
                1,
                payParams.payChannel,
                IAPConstants.getSceneCodeOrDefault(scene),
                payParams.productType
            ) { result ->
                val currentRequestBody = result?.second?.body
                currentRequestBody?.productCode = if (planId.isNullOrEmpty()) productId else planId
                payParams.currentRequestBody = currentRequestBody
                callback.invoke(payParams, result?.second?.message, result?.second?.code)
            }
        } else {
            callback.invoke(payParams, IPayInteractor.ERROR_PARAMS_ERROR, Product.FAILD)
        }
    }

    override fun checkConnect() {
        checkGoogleConnect()
    }

    override suspend fun getBalance(messageId: Int?, callback: (suspend (userBalance: UserBalance?) -> Unit)?) {
        metaRepository.getBalance(CoinType.gParkCoin).collect {
            updateBalance(messageId ?: 0, it, callback)
        }
    }

    override suspend fun getPointsBalance(callback: (suspend (userBalance: UserBalance?) -> Unit)?){
        metaRepository.getPoint().collect { result ->
            val pointNum = result.data?.leCoinNum
            Timber.i("getPointsBalance: $pointNum")
            if (result.succeeded && result.data != null) {
                arkCallback(0, point = pointNum)
            }
            callback?.invoke(result.data)
        }
    }

    private suspend fun updateBalance(
        messageId: Int,
        result: DataResult<UserBalance>,
        callback: (suspend (userBalance: UserBalance?) -> Unit)? = null
    ) {
        val leCoinNum = result.data?.leCoinNum
        Timber.i("getBalance: $leCoinNum")
        if (result.succeeded && result.data != null) {
            CpEventBus.post(result.data!!)
        }
        callback?.invoke(result.data)
        val point = metaRepository.getPoint().singleOrNull()?.data?.leCoinNum ?: 0
        arkCallback(messageId, leCoinNum ?: 0, point)
    }

    @Subscribe
    fun onEvent(balance: UserBalance) {
        _userBalance.postValue(balance)
    }

    private fun arkCallback(
        messageId: Int,
        ark: Long? = null,
        point: Long? = null,
    ) {
        UGCProtocolSender.sendProtocol(
            ProtocolSendConstant.PROTOCOL_PAY_ARK_CALLBACK,
            messageId,
            HashMap<String, Any>().apply {
                if (ark != null) {
                    this["ark"] = ark
                }
                if (point != null) {
                    this["point"] = point
                }
            }
        )
    }

    override suspend fun mwPay(
        hashMap: Map<String, Any?>,
        callback: suspend (DataResult<*>) -> Unit
    ) {
        metaRepository.mwPay(hashMap).collect {
            callback.invoke(it)
        }
    }

    //支付结果回调
    private fun getIPayCallback(): IPayCallback<PayParams> {
        return object : IPayCallback<PayParams> {

            override fun onPayFailed(param: PayParams, errorMessage: String?, code: Int) {
                onPayResult(param, false, code, errorMessage)

            }

            override fun onThirdPaySuccess(param: PayParams) {
                scope.launch {
                    //正式支付需要提交支付凭证
                    submit(param)
                }
            }

            override fun onPaySuccess(param: PayParams) {
                scope.launch {
                    //模拟支付只需要查询订单
                    getOrderResult(param)
                }
            }

            override fun onPayDialogShow(payParams: PayParams, isSuccess: Boolean?) {
                //支付弹窗展示.埋点发送
                sendTimeEvent(payParams, isSuccess ?: false, true)
            }

            override fun onRenewPaySuccess(param: PayParams) {
                //恢复订阅商品
                scope.launch {
                    delay(1200)
                    getBalance(null) { userBalance ->
                        onPayResult(param, true, 200, coinsBalance = userBalance?.leCoinNum)
                    }
                }
            }

        }
    }

    override fun coinsPay(payInfo: PayInfo, callback: (DataResult<String>) -> Unit){
        scope.launch {
            val requestBody = BodyRequestOrder(
                amount = payInfo.payAmount,
                payAmount = payInfo.payAmount,
                payUnit = BodyRequestOrder.PAY_UNIT_GPARK_COINS,
                productCode = payInfo.productCode,
                productName = payInfo.productName,
                count = payInfo.productCount,
                // 1:APP应用支付,2:扫码支付,4:h5支付,8:JSAPI支付方式,16:PC支付
                payTunnel = 1,
                payChannel = PayChannel.G_PARK_COIN.value,
                // 随机数
                nonce = abs(UUID.randomUUID().leastSignificantBits),
                productPrice = payInfo.productPrice,
                sceneCode = payInfo.sceneCode ?: 0,
                runtime = BodyRequestOrderRuntime(
                    gameId = payInfo.gameId
                )
            )
            metaRepository.privilegePlaceOrder(requestBody).collect { payResult ->
                if (payResult.succeeded && payResult.data != null) {
                    scope.launch {
                        val orderCode = payResult.data!!.orderCode
                        // 查询订单结果
                        val payResult = repeatGetResult(orderCode)
                        if (payResult) {
                            callback(
                                DataResult.Success(orderCode)
                            )
                        } else {
                            callback(
                                DataResult.Error(0, "")
                            )
                        }
                    }
                } else {
                    callback(
                        DataResult.Error(
                            payResult.code ?: 0,
                            payResult.message ?: "",
                            payResult.exception
                        )
                    )
                }
            }
        }
    }
}