package com.socialplay.gpark.function.overseabridge.bridge

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.lifecycle.LiveData
import com.socialplay.gpark.data.model.FireBaseIdToken
import com.socialplay.gpark.data.model.LoginFromType

interface IFirebaseSdkBridge {
    val analytics: IAnalyticsBridge
    val firebaseIdLiveData: LiveData<String?>
    val appInstanceIdLiveData: LiveData<String?>

    val auth: IAuthBridge

    fun isEnable(): Boolean {
        return true
    }

    /**
     * 并不是一个获取作用，是一个初始化作用，所以即使没有业务在直接使用，也需要保留调用
     */
    fun getFirebaseId(context: Context)
    fun getAppInstanceId(context: Context)
    fun parseDynamicLinks(intent: Intent, activity: Activity, succeed: (Uri?) -> Unit, failed: (Exception) -> Unit)
    fun createDynamicLink(deepLink: Uri):Uri


    interface IAnalyticsBridge {
        fun setUserProperty(name: String, value: String)
        fun logEvent(kind: String, params: Bundle)
        fun setUserId(uuid: String?)
    }

    interface IAuthBridge {
        fun signOut()
        suspend fun awaitSignInWithCredential(loginFromType: LoginFromType?, token: String): Any
        suspend fun getIdToken(customToken: String?): FireBaseIdToken?
        suspend fun unLinkWithCredential(loginFromType: LoginFromType)
        suspend fun linkWithCredential(loginFromType: LoginFromType, token: String): Any
    }
}