package com.socialplay.gpark.function.share

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.meta.biz.mgs.data.model.MgsGameShareResult
import com.meta.pandora.data.entity.Event
import com.meta.web.contract.model.WebShareNativeParams
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.share.ShareData
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.function.analytics.EventConstantsWrapper
import com.socialplay.gpark.function.overseabridge.bridge.IFaceBookSdkBridge
import com.socialplay.gpark.function.overseabridge.bridge.ISnapchatSdkBridge
import com.socialplay.gpark.function.overseabridge.bridge.ITiktokSdkBridge
import com.socialplay.gpark.function.pandora.PandoraToggleWrapper
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.function.share.platform.SystemShare
import com.socialplay.gpark.util.BitmapRecycleUtil
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.Md5Util.md5
import com.socialplay.gpark.util.ToastUtil
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream

object ShareWrapper {

    const val TTAI_ID_GAME_SHARE_CONFIG = 23303
    const val TTAI_ID_SCENE_SHARE_CONFIG = 25040201

    const val OFFICIAL_WEBSITE = "https://gpark.fun/"
    const val GPARK_TAG = "GPark"

    fun getRecordShareChannelList(): MutableList<RecordShareChannelInfo> {
        val channelList = mutableListOf<RecordShareChannelInfo>()
        channelList.add(RecordShareChannelInfo.Community)
        channelList.add(RecordShareChannelInfo.Discord)
        channelList.add(RecordShareChannelInfo.Tiktok)
        channelList.add(RecordShareChannelInfo.Facebook)
        channelList.add(RecordShareChannelInfo.X)
        channelList.add(RecordShareChannelInfo.Youtube)
        return channelList
    }

    val DISCORD = ComponentName("com.discord", "com.discord.share.ShareActivity")
    val TIKTOK = ComponentName("com.zhiliaoapp.musically", "com.ss.android.ugc.aweme.share.SystemShareActivity")
    val TIKTOK_UGC = ComponentName("com.ss.android.ugc.trill", "com.ss.android.ugc.aweme.share.SystemShareActivity")
    val TWITTER = ComponentName("com.twitter.android", "com.twitter.composer.ComposerActivity")
    val FACEBOOK = ComponentName("com.facebook.katana", "com.facebook.composer.shareintent.ImplicitShareIntentHandlerDefaultAlias")
    val YOUTUBE = ComponentName("com.google.android.youtube", "com.google.android.apps.youtube.app.application.Shell_UploadActivity")
    val SNAPCHAT = ComponentName("com.snapchat.android", "com.snap.mushroom.MainActivity")

    sealed class RecordShareChannelInfo(
        @StringRes val name: Int,
        @DrawableRes val icon: Int,
        val platform: List<ComponentName>?,
        val videoTitleUseTextField: Boolean = false,
        val sharePlatform: SharePlatform? = null
    ) {
        object Community : RecordShareChannelInfo(R.string.community, R.drawable.ic_share_post, null)
        object Discord : RecordShareChannelInfo(R.string.share_discord, R.drawable.ic_share_discord, listOf(DISCORD))
        object Tiktok : RecordShareChannelInfo(R.string.share_tiktok, R.drawable.ic_share_tiktok, listOf(TIKTOK, TIKTOK_UGC))
        object X : RecordShareChannelInfo(R.string.share_twitter, R.drawable.ic_share_x, listOf(TWITTER), true)
        object Facebook : RecordShareChannelInfo(R.string.share_facebook, R.drawable.ic_share_facebook, listOf(FACEBOOK))
        object Youtube : RecordShareChannelInfo(R.string.share_you_tu_be, R.drawable.ic_share_youtube, listOf(YOUTUBE))
    }


    private fun shareVideoToTikTok(videoUri: Uri, context: Activity, shareText: String) {
        GlobalContext.get().get<ITiktokSdkBridge>().share(videoUri, context, shareText)
    }

    /**
     * 分享到系统
     */
    fun shareVideo(context: Activity, videoUri: Uri, shareChannel: RecordShareChannelInfo, index: Int, shareText: String = "") {
        val platform = shareChannel.platform!![index]
        when (platform) {
            FACEBOOK -> {
                GlobalContext.get().get<IFaceBookSdkBridge>().share(context, videoUri, shareText)
            }

            TIKTOK, TIKTOK_UGC -> {
                shareVideoToTikTok(videoUri, context, shareText)
            }

            else -> SystemShare.shareVideoToPlatform(context, videoUri, shareChannel, index, shareText)
        }
    }

    fun share(context: Activity, shareData: ShareData) {
        when (shareData.platform) {
            SharePlatform.PLATFORM_YOUTUBE -> {
                val componentName = if (InstallUtil.isInstalledYoutube(context)) {
                    YOUTUBE
                } else {
                    shareData.notifyFail(
                        ShareHelper.CODE_NOT_INSTALLED,
                        null
                    )
                    return
                }
                when (shareData.mode) {
                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        val videoPath = shareData.videos?.singleOrNull()
                        if (videoPath.isNullOrEmpty()) {
                            shareData.notifyFail(
                                ShareHelper.CODE_INVALID_PARAMS,
                                null
                            )
                            return
                        }
                        SystemShare.shareVideoPathBySystem(
                            context,
                            videoPath,
                            shareData = shareData,
                            componentName = componentName
                        )
                    }

                    else -> {
                        shareData.notifyFail(
                            ShareHelper.CODE_UNSUPPORTED_MODE,
                            null
                        )
                    }
                }
            }

            SharePlatform.PLATFORM_TIKTOK -> {
                when (PandoraToggleWrapper.tiktokShareVersion) {
                    PandoraToggleWrapper.TIKTOK_SHARE_SYSTEM -> {
                        val componentName = if (InstallUtil.isInstalledTiktok(context)) {
                            TIKTOK_UGC
                        } else if (InstallUtil.isInstalledTiktokLite(context)) {
                            TIKTOK
                        } else {
                            shareData.notifyFail(
                                ShareHelper.CODE_NOT_INSTALLED,
                                null
                            )
                            return
                        }
                        when (shareData.mode) {
                            ShareHelper.MODE_SINGLE_VIDEO -> {
                                val videoPath = shareData.videos?.firstOrNull()
                                if (videoPath.isNullOrEmpty()) {
                                    shareData.notifyFail(
                                        ShareHelper.CODE_INVALID_PARAMS,
                                        null
                                    )
                                    return
                                }
                                SystemShare.shareVideoPathBySystem(
                                    context,
                                    videoPath,
                                    shareData = shareData,
                                    componentName = componentName
                                )
                            }

                            ShareHelper.MODE_SINGLE_IMAGE,
                            ShareHelper.MODE_MULTI_IMAGES -> {
                                if (shareData.images.isNullOrEmpty()) {
                                    shareData.notifyFail(
                                        ShareHelper.CODE_INVALID_PARAMS,
                                        null
                                    )
                                    return
                                }
                                SystemShare.shareImagePathsBySystem(
                                    context,
                                    shareData.images,
                                    shareData = shareData,
                                    componentName = componentName
                                )
                            }
                        }
                    }

                    PandoraToggleWrapper.TIKTOK_SHARE_OLD_SDK,
                    PandoraToggleWrapper.TIKTOK_SHARE_NEW_SDK -> {
                        if (GlobalContext.get().get<ITiktokSdkBridge>().share(context, shareData)) {
                            ShareHelper.saveRequestId(shareData)
                        }
                    }

                    else -> {
                        shareData.notifyFail(
                            ShareHelper.CODE_UNSUPPORTED_MODE,
                            null
                        )
                    }
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                val componentName = if (InstallUtil.isInstalledSnapchat(context)) {
                    SNAPCHAT
                } else {
                    shareData.notifyFail(
                        ShareHelper.CODE_NOT_INSTALLED,
                        null
                    )
                    return
                }
                when (PandoraToggleWrapper.snapchatShareVersion) {
                    PandoraToggleWrapper.SNAPCHAT_SHARE_SYSTEM -> {
                        when (shareData.mode) {
                            ShareHelper.MODE_SINGLE_VIDEO -> {
                                val videoPath = shareData.videos?.firstOrNull()
                                if (videoPath.isNullOrEmpty()) {
                                    shareData.notifyFail(
                                        ShareHelper.CODE_INVALID_PARAMS,
                                        null
                                    )
                                    return
                                }
                                SystemShare.shareVideoPathBySystem(
                                    context,
                                    videoPath,
                                    shareData = shareData,
                                    componentName = componentName
                                )
                            }

                            ShareHelper.MODE_SINGLE_IMAGE,
                            ShareHelper.MODE_MULTI_IMAGES -> {
                                if (shareData.images.isNullOrEmpty()) {
                                    shareData.notifyFail(
                                        ShareHelper.CODE_INVALID_PARAMS,
                                        null
                                    )
                                    return
                                }
                                SystemShare.shareImagePathsBySystem(
                                    context,
                                    shareData.images,
                                    shareData = shareData,
                                    componentName = componentName
                                )
                            }

                            else -> {
                                shareData.notifyFail(
                                    ShareHelper.CODE_UNSUPPORTED_MODE,
                                    null
                                )
                            }
                        }
                    }

                    PandoraToggleWrapper.SNAPCHAT_SHARE_SDK -> {
                        when (shareData.mode) {
                            ShareHelper.MODE_MULTI_IMAGES -> {
                                if (shareData.images.isNullOrEmpty()) {
                                    shareData.notifyFail(
                                        ShareHelper.CODE_INVALID_PARAMS,
                                        null
                                    )
                                    return
                                }
                                SystemShare.shareImagePathsBySystem(
                                    context,
                                    shareData.images,
                                    shareData = shareData,
                                    componentName = componentName
                                )
                            }

                            else -> {
                                GlobalContext.get().get<ISnapchatSdkBridge>()
                                    .share(context, shareData)
                            }
                        }
                    }

                    else -> {
                        shareData.notifyFail(
                            ShareHelper.CODE_UNSUPPORTED_MODE,
                            null
                        )
                    }
                }
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                when (shareData.mode) {
                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        val videoPath = shareData.videos?.firstOrNull()
                        if (videoPath.isNullOrEmpty()) {
                            shareData.notifyFail(
                                ShareHelper.CODE_INVALID_PARAMS,
                                null
                            )
                            return
                        }
                        SystemShare.shareVideoPathBySystem(
                            context,
                            videoPath
                        )
                    }

                    ShareHelper.MODE_SINGLE_IMAGE,
                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (shareData.images.isNullOrEmpty()) {
                            shareData.notifyFail(
                                ShareHelper.CODE_INVALID_PARAMS,
                                null
                            )
                            return
                        }
                        SystemShare.shareImagePathsBySystem(
                            context,
                            shareData.images
                        )
                    }
                }
            }
        }
    }

    fun sharePlatformToTypeId(platform: String): String? {
        return when (platform) {
            SharePlatform.PLATFORM_TIKTOK -> "1"
            SharePlatform.PLATFORM_YOUTUBE -> "2"
            SharePlatform.PLATFORM_SNAPCHAT -> "3"
            else -> null
        }
    }

    //后端定好的 分享到SNAPCHAT的请求参数
    const val TYPE_SNAPCHAT = "SNAPCHAT"

    fun mgsShare(shareChannel: String, activity: Activity?, shareInfo: MgsGameShareResult?, metaApp: Context): Boolean {
        if (shareInfo == null) {
            ToastUtil.gameShowShort(metaApp.getString(R.string.mgs_room_un_create))
            return false
        }
        when (shareChannel) {
            TYPE_SNAPCHAT -> {
                mgsShareBySnapchat(activity, shareInfo, metaApp)
            }

            else -> {
                return false
            }
        }
        return true
    }

    fun mgsShareBySnapchat(
        activity: Activity?,
        shareInfo: MgsGameShareResult,
        metaApp: Context
    ) {
        activity?.let {
            val sendIntent: Intent = Intent().apply {
                `package` = "com.snapchat.android"
                action = Intent.ACTION_SEND
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, shareInfo.jumpUrl)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            if (sendIntent.resolveActivity(activity.packageManager) != null) {
                activity.startActivity(sendIntent)
            } else {
                ToastUtil.gameShowShort(metaApp.getString(R.string.msg_invite_snapchat_not_installed))
            }
        }

    }

    fun getMgsShareAnalyticsEvent(shareType: String): Event? {
        return when (shareType) {
            TYPE_SNAPCHAT -> {
                EventConstantsWrapper.EVENT_INVITE_SNAPCHAT_CLICK
            }

            else -> {
                null
            }
        }
    }

    fun cacheSticker(
        context: Context,
        url: String,
        width: Int = 100,
        height: Int = 100
    ): File? {
        val filename = "sticker_${url.md5()}.png"
        val (exists, targetFile) = ShareHelper.getFile(context, filename)
        if (exists) return targetFile
        return if (saveSticker(context, url, targetFile, width, height)) {
            targetFile
        } else {
            null
        }
    }

    private fun saveSticker(context: Context, target: Any, file: File, width: Int, height: Int) =
        kotlin.runCatching {
            val bitmap = Glide.with(context)
                .asBitmap()
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .override(width.coerceIn(1, 300), height.coerceIn(1, 300))
                .encodeFormat(Bitmap.CompressFormat.PNG)
                .centerCrop()
                .load(target)
                .submit()
                .get()
            if (bitmap != null && !bitmap.isRecycled) {
                val out = FileOutputStream(file)
                if (bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)) {
                    out.flush()
                    out.close()
                }
                BitmapRecycleUtil.safeRecycle(bitmap)
            }
            true
        }.getOrElse {
            Timber.e(it)
            false
        }

    fun mapScene2ConfigScene(scene: String): String {
        return when (scene) {
            ShareHelper.SCENE_AVATAR_SCREENSHOT -> {
                "avatar"
            }

            else -> {
                scene
            }
        }
    }

    suspend fun webShare(
        activity: Activity,
        nativeParams: WebShareNativeParams,
        webShareParam: com.meta.lib.web.core.model.WebShareParam
    ) {

    }
}
