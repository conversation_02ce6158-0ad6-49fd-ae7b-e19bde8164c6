package com.socialplay.gpark.function.oss

import com.meta.upload.core.MetaUpload
import com.meta.upload.qiniu.QiNiuUpload
import com.socialplay.gpark.function.download.DownloadFileProvider
import com.tencent.cos.xml.BuildConfig
import org.koin.core.context.GlobalContext

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/02/05
 *     desc   :
 * </pre>
 */
object OssHelper {

    fun init() {
        val recorderFile = DownloadFileProvider.qiNiuFileRecorder
        if (!recorderFile.exists()) {
            recorderFile.mkdirs()
        }
        // 可选多个供应商
        MetaUpload.init(
            GlobalContext.get().get(),
            listOf(QiNiuUpload(recorderFile.absolutePath)),
            true,
            BuildConfig.DEBUG
        )
    }
}