package com.socialplay.gpark.function.analytics

import com.meta.pandora.data.entity.Event

object EventConstantsWrapper {

    @EventDesc("邀请好友_snapchat_点击")
    val EVENT_INVITE_SNAPCHAT_CLICK = Event("c_invite_snapchat_click")

    @EventDesc("Firebase专用事件-新用户")
    val EVENT_FIREBASE_NEW_USER = Event("event_firebase_new_user")

    @EventDesc("Firebase专用事件-老用户")
    val EVENT_FIREBASE_OLD_USER = Event("event_firebase_old_user")
}