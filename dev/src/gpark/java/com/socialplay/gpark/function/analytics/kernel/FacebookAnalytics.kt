package com.socialplay.gpark.function.analytics.kernel

import android.app.Application
import android.os.Bundle
import androidx.core.os.bundleOf
import com.meta.pandora.data.entity.Event
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.function.overseabridge.bridge.IFaceBookSdkBridge
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.*
import kotlin.collections.HashMap

/**
 * Created by bo.li
 * Date: 2021/9/14
 * Desc: facebook发送埋点
 */
object FacebookAnalytics {

    private val facebookSdkBridge: IFaceBookSdkBridge by lazy { GlobalContext.get().get() }

    fun init(application: Application) {
        facebookSdkBridge.initAnalytics(application, BuildConfig.DEBUG)
    }

    fun track(event: Event, params: Map<String, Any>? = null) {
        val paramsMap = mutableMapOf<String, Any>()
        params?.let { paramsMap.putAll(it) }
        val facebookParam = FacebookParams(paramsMap).toBundle()
        if (BuildConfig.DEBUG) {
            Timber.d("meta_analytics--facebook-- \n event：${event.kind},${event.desc} \n params：$facebookParam")
        }
        facebookSdkBridge.logEvent(event.kind, facebookParam)
    }

    class FacebookParams(internal val params: MutableMap<String, Any> = HashMap()) {
        fun toBundle(): Bundle {
            return bundleOf(
                *params.map {
                    val key = it.key.lowercase(Locale.getDefault())
                    val value = if (it.value !is String && it.value !is Int) {
                        it.value.toString()
                    } else {
                        it.value
                    }
                    key to value
                }.toTypedArray())
        }
    }
}