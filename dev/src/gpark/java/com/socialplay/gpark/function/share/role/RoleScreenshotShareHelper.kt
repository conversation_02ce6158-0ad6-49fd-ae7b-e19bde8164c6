package com.socialplay.gpark.function.share.role

import androidx.fragment.app.FragmentActivity
import com.socialplay.gpark.data.model.share.ShareConfig
import com.socialplay.gpark.data.model.share.ShareData
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.function.pandora.PandoraToggleWrapper
import com.socialplay.gpark.function.share.MetaShare
import com.socialplay.gpark.function.share.ShareWrapper

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/22
 *     desc   :
 * </pre>
 */
object RoleScreenshotShareHelper {

    fun initThirdPartyPlatforms(list: MutableList<SharePlatform>) {
        list.add(SharePlatform.tiktok())
//        list.add(SharePlatform.snapchat())
        list.add(SharePlatform.save())
        list.add(SharePlatform.system())
    }

    fun share(
        activity: FragmentActivity,
        platform: String,
        images: List<String>,
        config: ShareConfig?,
        requestId: String,
    ) {
        when (platform) {
            SharePlatform.PLATFORM_TIKTOK -> {
                MetaShare.share(
                    activity,
                    ShareData.tiktokMultiImages(
                        requestId,
                        images,
                        config = config
                    )
                )
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                MetaShare.share(
                    activity,
                    ShareData.snapchatMultiImages(
                        requestId,
                        images
                    )
                )
            }
        }
    }
}