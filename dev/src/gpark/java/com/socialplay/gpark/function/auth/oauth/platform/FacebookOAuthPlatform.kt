package com.socialplay.gpark.function.auth.oauth.platform

import android.app.Activity
import android.app.Application
import android.content.Intent
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.function.overseabridge.bridge.IFaceBookSdkBridge
import org.koin.core.context.GlobalContext

/**
 * @author: ning.wang
 * @date: 2021-09-23 8:22 下午
 * @desc:
 */
class FacebookOAuthPlatform : OAuthPlatform {

    private val facebookSdkBridge by lazy { GlobalContext.get().get<IFaceBookSdkBridge>() }

    override fun login(activity: Activity?, source: String?, loginType: LoginType?) {
        val context =  activity ?: (GlobalContext.get().get() as Application)
        context.startActivity(Intent(context, FacebookCallbackActivity::class.java).apply {
            putExtra(FacebookCallbackActivity.EXTRA_SOURCE, source)
            putExtra(FacebookCallbackActivity.EXTRA_LOGIN_TYPE, loginType?.type)
            if (context !is Activity) {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
        })
    }

    override fun logout() {
        facebookSdkBridge.logOut()
    }
}