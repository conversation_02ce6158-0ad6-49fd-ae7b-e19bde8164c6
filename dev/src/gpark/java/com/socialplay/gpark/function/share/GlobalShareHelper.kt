package com.socialplay.gpark.function.share

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.data.model.editor.UgcGameDetail
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.data.model.post.PostShareDetail
import com.socialplay.gpark.data.model.share.ShareData
import com.socialplay.gpark.data.model.share.SharePendingData
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.videofeed.VideoFeedItem
import com.socialplay.gpark.databinding.ViewShareScreenshotBinding
import com.socialplay.gpark.function.pandora.PandoraToggleWrapper
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.editor.share.AvatarShareViewModelState
import com.socialplay.gpark.ui.share.GlobalShareState
import com.socialplay.gpark.ui.share.GlobalShareViewModel
import com.socialplay.gpark.ui.share.IGlobalShareDialogMixin
import com.socialplay.gpark.util.QRCode
import com.socialplay.gpark.util.extension.dp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/22
 *     desc   :
 * </pre>
 */
object GlobalShareHelper {

    fun shareProfile(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_TIKTOK -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.tiktokSingleImage(
                            requestId,
                            it,
                            tags = if (PandoraToggleWrapper.tiktokShareWithTags) {
                                listOf(ShareWrapper.GPARK_TAG)
                            } else {
                                null
                            }
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.snapchatSingleImage(
                            requestId,
                            it
                        )
                    )
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun sharePgc(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String,
        pgcGame: ShareRawData.Game,
        scope: CoroutineScope
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_TIKTOK -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    MetaShare.share(
                        activity,
                        ShareData.tiktokMultiImages(
                            requestId,
                            s.paths,
                            tags = if (PandoraToggleWrapper.tiktokShareWithTags) {
                                listOf(ShareWrapper.GPARK_TAG)
                            } else {
                                null
                            }
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    val imagePath = s.paths.first()
                    scope.launch {
                        MetaShare.share(
                            activity,
                            ShareData.snapchatSingleImage(
                                requestId,
                                imagePath,
                                sticker = if (PandoraToggleWrapper.snapchatShareWithSticker) {
                                    ShareData.Sticker(
                                        icon = withContext(Dispatchers.IO) {
                                            ShareWrapper.cacheSticker(
                                                activity,
                                                imagePath
                                            )?.absolutePath
                                        },
                                        desc = activity.getString(
                                            R.string.global_share_content_1,
                                            pgcGame.name.orEmpty()
                                        ),
                                        url = data.info?.jumpUrl
                                    )
                                } else {
                                    null
                                }
                            )
                        )
                    }
                }
            }

            SharePlatform.PLATFORM_LONG_IMAGE -> {
                when (data.subPlatform) {
                    SharePlatform.PLATFORM_TIKTOK -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.tiktokSingleImage(
                                    requestId,
                                    it,
                                    tags = if (PandoraToggleWrapper.tiktokShareWithTags) {
                                        listOf(ShareWrapper.GPARK_TAG)
                                    } else {
                                        null
                                    }
                                )
                            )
                        }
                    }

                    SharePlatform.PLATFORM_SNAPCHAT -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.snapchatSingleImage(
                                    requestId,
                                    it
                                )
                            )
                        }
                    }
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun sharePgcSupportLongImage(platform: String?): Boolean {
        when (platform) {
            SharePlatform.PLATFORM_TIKTOK,
            SharePlatform.PLATFORM_SNAPCHAT -> {
                return true
            }
        }

        return false
    }

    fun shareUgc(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String,
        ugcGame: UgcGameDetail,
        scope: CoroutineScope
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_TIKTOK -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    MetaShare.share(
                        activity,
                        ShareData.tiktokMultiImages(
                            requestId,
                            s.paths,
                            tags = if (PandoraToggleWrapper.tiktokShareWithTags) {
                                listOf(ShareWrapper.GPARK_TAG)
                            } else {
                                null
                            }
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    val imagePath = s.paths.first()
                    scope.launch {
                        MetaShare.share(
                            activity,
                            ShareData.snapchatSingleImage(
                                requestId,
                                imagePath,
                                sticker = if (PandoraToggleWrapper.snapchatShareWithSticker) {
                                    ShareData.Sticker(
                                        icon = withContext(Dispatchers.IO) {
                                            ShareWrapper.cacheSticker(
                                                activity,
                                                imagePath
                                            )?.absolutePath
                                        },
                                        desc = activity.getString(
                                            R.string.global_share_content_1,
                                            ugcGame.ugcGameName.orEmpty()
                                        ),
                                        url = data.info?.jumpUrl
                                    )
                                } else {
                                    null
                                }
                            )
                        )
                    }
                }
            }

            SharePlatform.PLATFORM_LONG_IMAGE -> {
                when (data.subPlatform) {
                    SharePlatform.PLATFORM_TIKTOK -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.tiktokSingleImage(
                                    requestId,
                                    it,
                                    tags = if (PandoraToggleWrapper.tiktokShareWithTags) {
                                        listOf(ShareWrapper.GPARK_TAG)
                                    } else {
                                        null
                                    }
                                )
                            )
                        }
                    }

                    SharePlatform.PLATFORM_SNAPCHAT -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.snapchatSingleImage(
                                    requestId,
                                    it
                                )
                            )
                        }
                    }
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun shareUgcSupportLongImage(platform: String?): Boolean {
        when (platform) {
            SharePlatform.PLATFORM_TIKTOK,
            SharePlatform.PLATFORM_SNAPCHAT -> {
                return true
            }
        }

        return false
    }

    fun sharePost(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String,
        postDetail: PostShareDetail,
        scope: CoroutineScope
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_TIKTOK -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else if (s.raw.hasVideo) {
                    val videoPath = s.paths.first()
                    MetaShare.share(
                        activity,
                        ShareData.tiktokSingleVideo(
                            requestId,
                            videoPath,
                            tags = if (PandoraToggleWrapper.tiktokShareWithTags) {
                                listOf(ShareWrapper.GPARK_TAG)
                            } else {
                                null
                            }
                        )
                    )
                } else {
                    MetaShare.share(
                        activity,
                        ShareData.tiktokMultiImages(
                            requestId,
                            s.paths,
                            tags = if (PandoraToggleWrapper.tiktokShareWithTags) {
                                listOf(ShareWrapper.GPARK_TAG)
                            } else {
                                null
                            }
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else if (s.raw.hasVideo) {
                    val videoPath = s.paths.first()
                    scope.launch {
                        MetaShare.share(
                            activity,
                            ShareData.snapchatSingleVideo(
                                requestId,
                                videoPath,
                                sticker = if (PandoraToggleWrapper.snapchatShareWithSticker) {
                                    ShareData.Sticker(
                                        icon = withContext(Dispatchers.IO) {
                                            ShareWrapper.cacheSticker(
                                                activity,
                                                videoPath
                                            )?.absolutePath
                                        },
                                        desc = postDetail.content,
                                        url = data.info?.jumpUrl
                                    )
                                } else {
                                    null
                                }
                            )
                        )
                    }
                } else if (s.paths.size == 1) {
                    val imagePath = s.paths.first()
                    scope.launch {
                        MetaShare.share(
                            activity,
                            ShareData.snapchatSingleImage(
                                requestId,
                                imagePath,
                                sticker = if (PandoraToggleWrapper.snapchatShareWithSticker) {
                                    ShareData.Sticker(
                                        icon = withContext(Dispatchers.IO) {
                                            ShareWrapper.cacheSticker(
                                                activity,
                                                imagePath
                                            )?.absolutePath
                                        },
                                        desc = postDetail.content,
                                        url = data.info?.jumpUrl
                                    )
                                } else {
                                    null
                                }
                            )
                        )
                    }
                } else {
                    MetaShare.share(
                        activity,
                        ShareData.snapchatMultiImages(
                            requestId,
                            s.paths
                        )
                    )
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun shareVideo(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String,
        videoFeed: VideoFeedItem,
        scope: CoroutineScope
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_YOUTUBE -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    val videoPath = s.paths.first()
                    MetaShare.share(
                        activity,
                        ShareData.youtubeSingleVideo(
                            requestId,
                            videoPath,
                            title = videoFeed.videoContent
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_TIKTOK -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    val videoPath = s.paths.first()
                    MetaShare.share(
                        activity,
                        ShareData.tiktokSingleVideo(
                            requestId,
                            videoPath,
                            tags = if (PandoraToggleWrapper.tiktokShareWithTags) {
                                listOf(ShareWrapper.GPARK_TAG)
                            } else {
                                null
                            }
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    val videoPath = s.paths.first()
                    scope.launch {
                        MetaShare.share(
                            activity,
                            ShareData.snapchatSingleVideo(
                                requestId,
                                videoPath,
                                sticker = if (PandoraToggleWrapper.snapchatShareWithSticker) {
                                    ShareData.Sticker(
                                        icon = withContext(Dispatchers.IO) {
                                            ShareWrapper.cacheSticker(
                                                activity,
                                                videoPath
                                            )?.absolutePath
                                        },
                                        desc = videoFeed.videoContent,
                                        url = data.info?.jumpUrl
                                    )
                                } else {
                                    null
                                }
                            )
                        )
                    }
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun shareOcMoment(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_YOUTUBE -> {
                if (s.raw.hasVideo) {
                    MetaShare.share(
                        activity,
                        ShareData.youtubeSingleVideo(
                            requestId,
                            s.raw.video!!,
                            config = s.config
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_TIKTOK -> {
                if (s.raw.hasVideo) {
                    MetaShare.share(
                        activity,
                        ShareData.tiktokSingleVideo(
                            requestId,
                            s.raw.video!!,
                            config = s.config
                        )
                    )
                } else if (s.raw.hasImage) {
                    MetaShare.share(
                        activity,
                        ShareData.tiktokMultiImages(
                            requestId,
                            s.raw.images!!,
                            config = s.config
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                if (s.raw.hasVideo) {
                    MetaShare.share(
                        activity,
                        ShareData.snapchatSingleVideo(
                            requestId,
                            s.raw.video!!,
                            config = s.config
                        )
                    )
                } else if (s.raw.hasImage) {
                    MetaShare.share(
                        activity,
                        ShareData.snapchatSingleImage(
                            requestId,
                            s.raw.image!!,
                            config = s.config
                        )
                    )
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun shareScreenshot(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_TIKTOK -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.tiktokSingleImage(
                            requestId,
                            it,
                            config = s.config
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.snapchatSingleImage(
                            requestId,
                            it,
                            config = s.config
                        )
                    )
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun shareAvatar(
        fragment: Fragment,
        activity: FragmentActivity,
        s: AvatarShareViewModelState,
        platform: String,
        requestId: String,
        images: List<File>
    ) {
        when (platform) {
            SharePlatform.PLATFORM_TIKTOK -> {
                MetaShare.share(
                    activity,
                    ShareData.tiktokMultiImages(
                        requestId,
                        images.map { it.absolutePath },
                        config = s.config
                    )
                )
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                MetaShare.share(
                    activity,
                    ShareData.snapchatMultiImages(
                        requestId,
                        images.map { it.absolutePath }
                    )
                )
            }
        }
    }

    fun longImageScreenshot(
        raw: ShareRawData,
        vm: GlobalShareViewModel,
        container: View,
        isPortrait: Boolean,
        mixin: IGlobalShareDialogMixin
    ) {
        val screenshot = raw.image
        if (screenshot.isNullOrBlank()) {
            vm.longImageFail()
            return
        }
        val tempKey = vm.longImageLoading(listOf(screenshot))
        val tempBinding = ViewShareScreenshotBinding.bind(container)
        val qrCodeBitmap = QRCode.newQRCodeUtil()
            .margin("0")
            .content(ShareHelper.downloadPage)
            .size(tempBinding.dp(150))
            .build()
        tempBinding.ivLongImageQrCode.setImageBitmap(qrCodeBitmap)
        mixin.longImageHelper3(
            raw,
            tempKey,
            screenshot,
            tempBinding.ivLongImageScreenshot,
            tempBinding.root
        )
    }
}