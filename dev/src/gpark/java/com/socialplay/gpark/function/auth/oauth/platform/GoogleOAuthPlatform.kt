package com.socialplay.gpark.function.auth.oauth.platform

import android.app.Activity
import android.app.Application
import android.content.Intent
import com.socialplay.gpark.data.model.LoginType
import org.koin.core.context.GlobalContext

/**
 * @author: ning.wang
 * @date: 2021-09-23 8:21 下午
 * @desc:
 */
class GoogleOAuthPlatform : OAuthPlatform {
    companion object {
        private const val TAG = "GoogleLoginPlatform"
    }


    override fun login(activity: Activity?, source: String?, loginType: LoginType?) {
        val context =  activity ?: (GlobalContext.get().get() as Application)
        context.startActivity(Intent(context, GoogleCallbackActivity::class.java).apply {
            putExtra(GoogleCallbackActivity.EXTRA_IS_LOGIN, true)
            putExtra(GoogleCallbackActivity.EXTRA_SOURCE, source)
            putExtra(GoogleCallbackActivity.EXTRA_LOGIN_TYPE, loginType?.type)
            if (context !is Activity) {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
        })
    }

    override fun logout() {
        val context = (GlobalContext.get().get() as Application)
        context.startActivity(Intent(context, GoogleCallbackActivity::class.java).apply {
            putExtra("isLogin", false)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        })
    }

}