package com.socialplay.gpark.function.overseabridge.bridge

import android.app.Activity
import android.app.Application
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.lifecycle.Lifecycle
import com.socialplay.gpark.ui.ad.AdActivity

interface IAdSdkBridge {

    fun isEnable(): Boolean {
        return true
    }
    fun preInit(application: Application, enableLog: Boolean)

    fun init()

    fun addInitCallback(runnable: Runnable?)

    fun preLoadAds(gameId: String?, gamePkg: String?, activity: Activity)

    fun isRewardedAdReady(): Boolean

    fun showRewardedAd(
        activity: Activity,
        container: ViewGroup,
        lifecycle: Lifecycle,
        placement: String,
        gameId: String? = null,
        gamePkg: String? = null,
        callback: VideoAdCallback?,
    )

    fun showMediationDebugger(activity: Activity)

    interface VideoAdCallback {
        fun onShowError(error: String?)
        fun onShow()
        fun onShowSkip()
        fun onShowReward()
        fun onShowClose()
        fun onShowClick()
    }
}

