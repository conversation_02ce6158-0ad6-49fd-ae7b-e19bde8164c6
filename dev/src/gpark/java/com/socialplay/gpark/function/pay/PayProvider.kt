package com.socialplay.gpark.function.pay

import android.app.Application
import com.meta.box.biz.h5config.model.H5PageConfigItem
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.kv.MetaKV
import org.koin.core.context.GlobalContext

object PayProvider {

    const val ENABLE_RECHARGE = true
    const val ENABLE_DAILY = true

    /**
     * 是否启用会员
     */
    const val ENABLE_PREMIUM = true
    /**
     * 会员页 url 地址
     */
    val PREMIUM_PAGE_URL = BuildConfig.GPARK_PLUS_STATUS

    /**
     * 用户隐私协议
     */
    fun getBuyCoinsPageRechargeH5ConfigItem(): H5PageConfigItem {
        val h5PageConfig: H5PageConfigInteractor = GlobalContext.get().get()
        return h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.PRIVACY_AGREEMENT)
    }

    fun getPayInteractor(
        metaRepository: IMetaRepository,
        metaApp: Application,
        accountInteractor: AccountInteractor,
        metaKV: MetaKV
    ): IPayInteractor {
        return GparkPayInteractor(metaRepository, metaApp, accountInteractor, metaKV)
    }
}