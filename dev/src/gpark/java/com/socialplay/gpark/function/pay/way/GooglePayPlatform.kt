package com.socialplay.gpark.function.pay.way

import android.app.Activity
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.GooglePayResultData
import com.socialplay.gpark.data.model.Product
import com.socialplay.gpark.data.model.pay.IAPConstants
import com.socialplay.gpark.data.model.pay.PayParams
import com.socialplay.gpark.data.model.pay.SubsData
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.overseabridge.bridge.IGoogleSdkBridge
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.util.GsonUtil
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/03/18
 *     desc   :
 *
 */
class GooglePayPlatform : BasePayPlatform<PayParams>() {
    private val billingClientBridge: IGoogleSdkBridge.IBillingClientBridge by lazy { GlobalContext.get().get<IGoogleSdkBridge>().billingClientBridge }
    private val accountInteractor by lazy { GlobalContext.get().get<AccountInteractor>() }
    init {
        billingClientBridge.setPurchasesUpdatedListener(
            onSuccess = { google ->
                analyticsTrack(
                    callbackCode = 1,
                    payParams = getAgentPayParams(),
                    googleResult = google
                )
                var payParams = getAgentPayParams()
                if (payParams == null || google.orderId != payParams.takeOderResult?.payCode) {
                    if (accountInteractor.accountLiveData.value?.uuid == google.uuid) {
                        Timber.d("GooglePay-onSuccess-history-currentUser")
                        // 当前回调属于未消耗订单重新消耗
                        val googleResult = google.copy(
                            consumeAgain = true,
                            needUpdate = false
                        )
                        payParams = PayParams()
                        updatePrams(googleResult, payParams) {
                            Timber.d("GooglePay-onSuccess-history-currentUser-onThirdPaySuccess")
                            onThirdPaySuccess(payParams)
                        }
                    } else {
                        Timber.d("GooglePay-onSuccess-history-otherUser-ignore")
                    }
                } else {
                    //支付成功
                    payParams.googleResultData = google
                    Timber.d("GooglePay-onSuccess-normal")
                    onThirdPaySuccess(payParams)
                }
            },
            onCancel = {message->
                analyticsTrack(
                    callbackCode = 2,
                    payParams = getAgentPayParams(),
                    errorMessage = message
                )
                if (getAgentPayParams() == null) {
                    return@setPurchasesUpdatedListener
                }
                //支付弹窗关闭
                payFailed(message, IPayInteractor.FAIL_CANCEL)
            },
            onFail = { msg ,code ->
                analyticsTrack(
                    callbackCode = 3,
                    payParams = getAgentPayParams(),
                    errorCode = code,
                    errorMessage = msg
                )
                if (getAgentPayParams() == null) {
                    return@setPurchasesUpdatedListener
                }
                //拉起失败
                payFailed(msg, code)
            },
            onConsumeGood = {google->
                analyticsTrack(
                    callbackCode = 4,
                    payParams = getAgentPayParams(),
                    googleResult = google
                )
                //未消耗订单重新消耗
                if (accountInteractor.accountLiveData.value?.uuid == google.uuid) {
                    Timber.d("GooglePay-onConsumeGood-currentUser-needUpdate = ${google.needUpdate}")
                    var payParams: PayParams? = null
                    if (google.needUpdate == false) {
                        //只需要提交到后端，页面不需要做任何处理
                        payParams = PayParams()
                        updatePrams(google, payParams) {
                            Timber.d("GooglePay-onConsumeGood-onThirdPaySuccess")
                            onThirdPaySuccess(payParams)
                        }
                    } else {
                        //未消耗的商品需要再次消耗
                        payParams = getAgentPayParams()
                        if (payParams == null) {
                            payParams = PayParams()
                        }
                        updatePrams(google, payParams) {
                            Timber.d("GooglePay-onConsumeGood-onThirdPaySuccess")
                            onThirdPaySuccess(payParams)
                        }
                    }
                } else {
                    Timber.d("GooglePay-onConsumeGood-otherUser-ignore")
                }
            },
            onPayDialogShow = {isScuccess->
                analyticsTrack(
                    callbackCode = 5,
                    payParams = getAgentPayParams(),
                    dialogShow = isScuccess
                )
                onPayDialogShow(isScuccess)
            }
        )
    }

    private fun analyticsTrack(
        callbackCode: Int,
        payParams: PayParams? = null,
        googleResult: GooglePayResultData? = null,
        errorCode: Int? = null,
        errorMessage: String? = null,
        dialogShow: Boolean? = null,
    ) {
        val map = mutableMapOf<String, Any?>()
        map["callback_code"] = callbackCode
        if (errorCode != null) {
            map["error_code"] = errorCode
        }
        if (errorMessage != null) {
            map["error_message"] = errorMessage
        }
        if (dialogShow != null) {
            map["dialog_show"] = if(dialogShow) 1 else 0
        }
        if(payParams!=null){
            map["pay_channel"] = payParams.payChannel
            map["pay_iap_scene"] = payParams.currentIapScene
            map["pay_scene_code"] = payParams.sceneCode
            map["pay_source"] = payParams.currentSource
            map["pay_gameid"] = payParams.currentGameId
            map["pay_product_type"] = payParams.productType
            map["pay_uuid"] = payParams.uuid
            map["pay_plan_id"] = payParams.planId
            map["pay_product_id"] = payParams.productId

            val payBody = payParams.currentRequestBody
            if(payBody!=null){
                map["pay_body_product_code"] = payBody.productCode
                map["pay_body_tunnel"] = payBody.payTunnel
                map["pay_body_channel"] = payBody.payChannel
                map["pay_body_scene_code"] = payBody.sceneCode
            }
            map["pay_order_id"] = payParams.currentOrderId

            val orderResult = payParams.takeOderResult
            if(orderResult!=null){
                map["pay_order_pay_code"] = orderResult.payCode
                map["pay_order_code"] = orderResult.orderCode
            }
        }
        if (googleResult != null) {
            map["google_order_id"] = googleResult.orderId
            map["google_token"] = googleResult.purchaseToken
            map["google_need_update"] = if (googleResult.needUpdate) 1 else 0
            map["google_product_type"] = googleResult.productType
            map["google_g_order_id"] = googleResult.googleOrderId
            map["google_uuid"] = googleResult.uuid
            if (googleResult.consumeAgain != null) {
                map["google_consume_again"] = if (googleResult.consumeAgain) 1 else 0
            }

            val jo =
                GsonUtil.gsonSafeParseCollection<HashMap<String, Any>>(googleResult.originalJson)
            if (jo != null) {
                map["google_jo_product_id"] = jo["productId"]?.toString()
                map["google_jo_purchase_state_str"] = jo["purchaseState"]?.toString()
                map["google_jo_purchase_token"] = jo["purchaseToken"]?.toString()
                map["google_jo_obf_account_id"] = jo["obfuscatedAccountId"]?.toString()
                map["google_jo_obf_profile_id"] = jo["obfuscatedProfileId"]?.toString()
                map["google_jo_quantity"] = jo["quantity"]?.toString()
                map["google_jo_acknowledged"] = jo["acknowledged"]?.toString()
                map["google_jo_order_id"] = jo["orderId"]?.toString()
            }
        }
        Analytics.track(
            EventConstants.EVENT_GPARK_PAY_GOOGLE_CALLBACK,
            map.filterValues { it != null }.mapValues { it.value!! }.toMap()
        )
    }

    private fun updatePrams(
        google: GooglePayResultData,
        payParams: PayParams,
        onSuccess:()->Unit,
    ){
        val jo = GsonUtil.gsonSafeParseCollection<HashMap<String, Any?>>(google.originalJson)
        val productId = jo?.getValue("productId") as String?
        payParams.payChannel = IAPConstants.PAY_CHANNEL_GOOGLE
        payParams.googleResultData = google
        payParams.productId = productId ?: ""
        // google 支付 onSuccess 回调不会给 productType
        if(google.productType.isNotEmpty()){
            Timber.d("GooglePay-updatePrams-productType=${google.productType}")
            if (google.productType == Product.SUBS) {
                payParams.sceneCode = IAPConstants.IAP_SCENE_CODE_SUBS
            } else {
                payParams.sceneCode = IAPConstants.IAP_SCENE_CODE_PG_COIN
            }
            payParams.productType = google.productType
            onSuccess()
        } else if (productId != null) {
            Timber.d("GooglePay-updatePrams-productId=$productId")
            // 尝试从google支付查询商品, 来分辨当前商品是什么类型的商品
            var callbackInvoked = false
            billingClientBridge.queryProductDetailsAsync(
                listOf(SubsData(productId)),
                Product.SUBS
            ) { result ->
                Timber.d("GooglePay-updatePrams-queryProductDetailsAsync-callback-result=${result}")
                if (!callbackInvoked) {
                    callbackInvoked = true
                    val products = result.second
                    // 因为查询的是subs商品, 所以只要能匹配上, 就是subs商品
                    val isSubsType = products.indexOfFirst { it.productId == productId } >= 0
                    if (isSubsType) {
                        payParams.sceneCode = IAPConstants.IAP_SCENE_CODE_SUBS
                        payParams.productType = Product.SUBS
                    } else {
                        payParams.sceneCode = IAPConstants.IAP_SCENE_CODE_PG_COIN
                        payParams.productType = Product.INAPP
                    }
                    onSuccess()
                }
            }
        } else {
            Timber.d("GooglePay-updatePrams-failed")
        }
    }
    override fun platformType(): Int = IAPConstants.PAY_CHANNEL_GOOGLE

    override fun startPay(activity: Activity, payParams: PayParams) {
        billingClientBridge.startPay(activity, payParams)
    }

    override suspend fun consumeGoods(type: String, purchaseToken: String) {
        if (type == Product.INAPP) {
            //一次性商品消耗
            billingClientBridge.consumeAsync(purchaseToken)
        } else {
            billingClientBridge.acknowledgePurchase(purchaseToken)
        }
    }
}