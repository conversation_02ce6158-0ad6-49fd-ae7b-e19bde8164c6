package com.socialplay.gpark.function.analytics.kernel

import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.lifecycle.Observer
import com.meta.pandora.data.entity.Event
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.interactor.*
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.function.overseabridge.bridge.IFirebaseSdkBridge
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.*

/**
 * Created by bo.li
 * Date: 2021/9/14
 * Desc: firebaseAnalytics发送埋点
 */
object FirebaseAnalyticsWrapper {

    private val firebaseSdkBridge: IFirebaseSdkBridge by lazy { GlobalContext.get().get() }
    private val deviceInteractor: DeviceInteractor by lazy { GlobalContext.get().get() }
    private val accountInteractor: AccountInteractor by lazy { GlobalContext.get().get() }
    private var accountChangeObserver: Observer<MetaUserInfo?>? = null
    private val googleInteractor: GoogleInteractor by lazy { GlobalContext.get().get() }
    private val solarEngineInteractor: SolarEngineInteractor by lazy { GlobalContext.get().get() }
    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }

    fun init() {
        // 事件参数和用户属性限制文档 https://support.google.com/firebase/answer/9237506?hl=en
        firebaseSdkBridge.analytics.setUserProperty("device_id", deviceInteractor.deviceId)
        firebaseSdkBridge.analytics.setUserProperty("android_id", deviceInteractor.androidId)

        solarEngineInteractor.collectEventsConversionData.observeForever {
            it.forEach { entry ->
                firebaseSdkBridge.analytics.setUserProperty(entry.key, entry.value)
            }
        }

        googleInteractor.firebaseId.observeForever {
            firebaseSdkBridge.analytics.setUserProperty("m_firebase_id", it ?: "")
        }

        accountChangeObserver = Observer<MetaUserInfo?> { info ->
            info ?: return@Observer
            if (info.uuid?.isNotEmpty() == true) {
                firebaseSdkBridge.analytics.setUserId(info.uuid)
                accountChangeObserver?.also {
                    accountInteractor.accountLiveData.removeObserver(it)
                }
            }
        }.also {
            accountInteractor.accountLiveData.observeForever(it)
        }
    }

    /**
     * 转换格式，发埋点
     */
    fun track(event: Event, params: Map<String, Any>? = null) {
        val paramsMap = mutableMapOf<String, Any>()
        params?.let { paramsMap.putAll(it) }
        val firebaseParam = FirebaseAnalyticsParams(paramsMap).toBundle()
        firebaseParam.putString("uid", metaKV.account.uuid)
        if (BuildConfig.DEBUG) {
            Timber.d("meta_analytics--firebase-- \n event：${event.kind},${event.desc} \n params：$firebaseParam")
        }
        sendAnalytics(event.kind, firebaseParam)
    }

    /**
     * 发送埋点
     */
    private fun sendAnalytics(kind: String, params: Bundle) {
        try {
            // 如果没有初始化，就不发埋点了
            firebaseSdkBridge.analytics.logEvent(kind, params)
        } catch (e: Exception) {
            Timber.e(e, "meta_analytics--firebase-- \n error：${e.message}")
        }
    }

    class FirebaseAnalyticsParams(internal val params: MutableMap<String, Any> = HashMap()) {
        fun toBundle(): Bundle {
            return bundleOf(
                *params.map {
                    val key = it.key.lowercase(Locale.getDefault())
                    val value = it.value
                    key to value
                }.toTypedArray()
            )
        }
    }
}