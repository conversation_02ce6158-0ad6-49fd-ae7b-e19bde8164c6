package com.socialplay.gpark.function.analytics

import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.repository.MetaRepositoryWrapper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.withContext

/**
 *
 * 启动时，请求后端获取是否是新用户。如果已经确定是老用户了，以后就不会请求了
 * 不管是新用户还是老用户，都会上传一遍Firebase
 */
class NewUserCheckAnalytics(
    private val metaKV: MetaKV,
    private val repository: IMetaRepositoryWrapper
) {
    suspend fun initialize(scope: CoroutineScope) {
        val isNewUserWsKV = metaKV.accountWrapper.is_new_user_ws
        if (isNewUserWsKV != null && isNewUserWsKV == "0") {
            // 确定老用户，不需要请求了
            Analytics.track(EventConstantsWrapper.EVENT_FIREBASE_OLD_USER)
            return
        }
        withContext(scope.coroutineContext) {
            repository.isNewUser().collect { isNewUser ->
                if (isNewUser.succeeded) {
                    if (isNewUser.data == true) {
                        Analytics.track(EventConstantsWrapper.EVENT_FIREBASE_NEW_USER)
                    } else {
                        metaKV.accountWrapper.is_new_user_ws = "0"
                        Analytics.track(EventConstantsWrapper.EVENT_FIREBASE_OLD_USER)
                    }
                }
            }
        }
    }
}