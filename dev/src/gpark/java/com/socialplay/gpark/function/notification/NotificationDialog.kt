package com.socialplay.gpark.function.notification

import android.os.Build
import android.os.Bundle
import androidx.core.app.NotificationManagerCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.dialog.DialogScene
import com.socialplay.gpark.ui.dialog.IDialogManager
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.notice.NotificationPermissionManager
import com.socialplay.gpark.ui.permission.Permission
import com.socialplay.gpark.ui.permission.PermissionRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext
import timber.log.Timber

class NotificationDialog : IDialogManager {

    val metaKV = GlobalContext.get().get<MetaKV>()
    var isGoSys = false
    val TAG = "NotificationDialog"

    override suspend fun initData(finishCallback: (Boolean) -> Unit) {

        finishCallback.invoke(true)
    }

    override fun needShow(fragment: Fragment, scene: DialogScene, args: Bundle?, needShowCallback: (Boolean) -> Unit) {
        if (!BuildConfig.IS_NEED_LEGAL) {

            if (isGoSys) {
                // 展示过弹框了，这里收集一下结果
                val enabled = NotificationManagerCompat.from(fragment.requireContext()).areNotificationsEnabled()
                Analytics.track(
                    EventConstants.EVENT_NOTIFICATION_APPLICATION,
                    "result" to enabled.toString()
                )
                isGoSys = false
            }

            // 不做合规相关的，才可以直接申请通知权限（要换成BuildConfig里面取）
            if (metaKV.appKV.needShowNotificationPermission) {
                Timber.tag(TAG).d("needShow needShowNotificationPermission")
                needShowCallback.invoke(true)
            } else {
                Timber.tag(TAG).d("needShow go checkNotificationPermission")
                checkNotificationPermission(fragment, needShowCallback)
            }
        } else {
            needShowCallback.invoke(false)
        }
    }

    override fun showByDialogManager(fragment: Fragment, onDismissCallback: (Boolean) -> Unit) {
        Timber.tag(TAG).d("showByDialogManager needShowNotificationPermission:${metaKV.appKV.needShowNotificationPermission}")
        if (metaKV.appKV.needShowNotificationPermission) {
            showNotificationPermission(fragment, onDismissCallback)
        } else {
            showAppNotificationDialog(fragment, onDismissCallback)
        }
    }

    override fun exeDismiss() {

    }

    private fun showNotificationPermission(fragment: Fragment, onDismissCallback: (Boolean) -> Unit) {
        Analytics.track(EventConstants.EVENT_NOTIFICATION_APPLICATION_SHOW)
        if (fragment.isDetached || !fragment.isAdded || fragment.view == null) {
            Timber.tag(TAG).d("showNotificationPermission fragment is invalid:${fragment}")
            onDismissCallback.invoke(true)
            return
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Timber.tag(TAG).d("showNotificationPermission SDK_INT >= 33 , start request permission")
            PermissionRequest.with(fragment.requireActivity())
                .permissions(Permission.POST_NOTIFICATION)
                .granted {
                    Timber.tag(TAG).d("showNotificationPermission SDK_INT > 33 , request permission result: granted")
                    if (fragment != null && fragment.isAdded && !fragment.isDetached && fragment.context != null && NotificationManagerCompat.from(fragment.requireContext()).areNotificationsEnabled()) {
                        Analytics.track(
                            EventConstants.EVENT_NOTIFICATION_APPLICATION,
                            "result" to "true"
                        )
                    }
                    onDismissCallback.invoke(true)
                }
                .denied {
                    Timber.tag(TAG).d("showNotificationPermission SDK_INT > 33 , request permission result: denied")
                    Analytics.track(
                        EventConstants.EVENT_NOTIFICATION_APPLICATION,
                        "result" to "false"
                    )
                    onDismissCallback.invoke(true)
                }
                .request()
            metaKV.appKV.needShowNotificationPermission = false
            //更新上次申请的时间和版本
            updateAppNotification(fragment)
        } else {
            Timber.tag(TAG).d("showNotificationPermission SDK_INT < 33 , launchWhenResumed")
            fragment.viewLifecycleOwner.lifecycleScope.launchWhenResumed {
                Timber.tag(TAG).d("showNotificationPermission SDK_INT < 33 , launchWhenResumed inner")
                if (!withContext(Dispatchers.IO) { NotificationManagerCompat.from(fragment.requireContext()).areNotificationsEnabled() }) {
                    Timber.tag(TAG).d("showNotificationPermission SDK_INT < 33 , launchWhenResumed, permission not enabled")
                    if (!fragment.isAdded || fragment.isDetached) {
                        onDismissCallback.invoke(true)
                        return@launchWhenResumed
                    }
                    ListDialog()
                        .title(fragment.getString(R.string.notification_permission_title))
                        .list(
                            mutableListOf(
                                SimpleListData(
                                    fragment.getString(R.string.allow),
                                    R.drawable.selector_button_enable,
                                    R.color.black
                                ),
                                SimpleListData(fragment.getString(R.string.dialog_cancel))
                            )
                        )
                        .clickCallback {
                            when (it?.text ?: "") {
                                fragment.getString(R.string.allow) -> {
                                    isGoSys = true
                                    onDismissCallback.invoke(false)
                                    PermissionRequest.goNotification(fragment.requireActivity())
                                }

                                else -> {
                                    Analytics.track(
                                        EventConstants.EVENT_NOTIFICATION_APPLICATION,
                                        "result" to "false"
                                    )
                                    onDismissCallback.invoke(true)
                                }
                            }
                        }
                        .show(fragment.childFragmentManager, "notification")

                    metaKV.appKV.needShowNotificationPermission = false
                    //更新上次申请的时间和版本
                    updateAppNotification(fragment)
                } else {
                    Timber.tag(TAG).d("showNotificationPermission SDK_INT < 33 , launchWhenResumed, permission enabled")
                    // 已有权限
                    onDismissCallback.invoke(true)
                }
            }
        }
    }

    private fun updateAppNotification(fragment: Fragment) {
        if (PandoraToggle.isSystemNotification) {
            fragment.viewLifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
                val record = NotificationPermissionManager.getAppNotificationRecord()
                Timber.d("checkAppNotification$record")
                NotificationPermissionManager.updateAppNotification(record, 1)
            }
        }
    }

    /**
     * app权限弹窗
     */
    private fun checkNotificationPermission(fragment: Fragment, needShowCallback: (Boolean) -> Unit) {
        Timber.tag(TAG).d("checkNotificationPermission checkAppNotification${metaKV.appKV.needShowNotificationPermission}")
        if (!metaKV.appKV.needShowNotificationPermission) {
            //否则判断是否需要app申请通知弹窗
            fragment.viewLifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
                val needShow = NotificationPermissionManager.appNeedPermission(fragment.requireContext())
                Timber.tag(TAG).d("lifecycleScope NotificationPermissionManager.appNeedPermission:$needShow")
                if (needShow) {
                    needShowCallback.invoke(true)
                } else {
                    needShowCallback.invoke(false)
                }
            }
        } else {
            needShowCallback.invoke(false)
        }
    }

    /**
     * 提示弹窗
     */
    private fun showAppNotificationDialog(fragment: Fragment, onDismissCallback: (Boolean) -> Unit) {
        Analytics.track(EventConstants.EVENT_APP_FIRST_PAGE_DIALOG_SHOW)
        NotificationPermissionManager.showPermissionDialog(
            fragment,
            title = fragment.getString(R.string.notification_app_title),
            content = fragment.getString(R.string.notification_app_content_one),
            confirmText = fragment.getString(R.string.notification_app_sure),
            cancelText = fragment.getString(R.string.notification_app_cancel),
            dismissCallBack = {
                onDismissCallback.invoke(true)
            },
            type = NotificationPermissionManager.TYPE_APP_NOTIFICATION
        ) {
            if (it) {
                Analytics.track(
                    EventConstants.EVENT_APP_FIRST_PAGE_DIALOG_CLICK,
                    "result" to "0"
                )
            } else {
                Analytics.track(
                    EventConstants.EVENT_APP_FIRST_PAGE_DIALOG_CLICK,
                    "result" to "1"
                )
            }
        }
        Timber.d("checkAppNotification_show")

    }

}