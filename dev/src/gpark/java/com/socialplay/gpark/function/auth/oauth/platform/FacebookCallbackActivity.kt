package com.socialplay.gpark.function.auth.oauth.platform

import android.content.Intent
import android.os.Bundle
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.databinding.ActivityOauthCallbackBinding
import com.socialplay.gpark.function.auth.oauth.OAuthManager
import com.socialplay.gpark.function.overseabridge.bridge.IFaceBookSdkBridge
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.property.viewBinding
import org.koin.core.context.GlobalContext
import timber.log.Timber


/**
 * @author: ning.wang
 * @date: 2021-09-27 4:04 下午
 * @desc:
 */
class FacebookCallbackActivity : BaseActivity() {
    companion object {
        private const val TAG = "LeoWn_FacebookCallback"
        const val EXTRA_SOURCE = "source"
        const val EXTRA_LOGIN_TYPE = "loginType"
    }

    private val facebookAuth: IFaceBookSdkBridge.IAuthBridge by lazy { GlobalContext.get().get<IFaceBookSdkBridge>().createAuth() }
    override val binding by viewBinding(ActivityOauthCallbackBinding::inflate)


    /**
     * [com.socialplay.gpark.data.model.LoginSource]
     */
    private var sourceFrom: String? = null
    /**
     * [com.socialplay.gpark.data.model.LoginType]
     */
    private var loginTypeFrom: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        sourceFrom = intent.getStringExtra(EXTRA_SOURCE)
        loginTypeFrom = intent.getStringExtra(EXTRA_LOGIN_TYPE)
        if (!GlobalContext.get().get<IFaceBookSdkBridge>().isEnable()){
            OAuthManager.callbacks.dispatchOnMainThread {
                this.onFailed(LoginWay.Facebook, getString(R.string.not_support_cap), 0)
            }
            finish()
            return
        }
        login()
    }

    private fun login() {
        if (facebookAuth.isExpired() == false) {
            logout()
            login()
            return
        }

        facebookAuth.login(this, object : IFaceBookSdkBridge.IAuthResultCallback {
            override fun onSuccess(token: String) {
                if (token.isEmpty()) {
                    OAuthManager.callbacks.dispatchOnMainThread {
                        Timber.tag(TAG).e("facebook login has error. error: token null")
                        this.onFailed(LoginWay.Facebook, getString(R.string.login_fail), 0)
                    }
                    return
                }
                Timber.tag(TAG).d("facebook login has success. token : $token")
                finish()
            }

            override fun onCancel() {
                OAuthManager.callbacks.dispatchOnMainThread {
                    Timber.tag(TAG).d("facebook login has cancel")
                    this.onCancel(LoginWay.Facebook)
                }
                finish()
            }

            override fun onError(error: Exception) {
                OAuthManager.callbacks.dispatchOnMainThread {
                    Timber.tag(TAG).e("facebook login has error. error: $error")
                    this.onFailed(LoginWay.Facebook, getString(R.string.login_fail), 0)
                }
                finish()
            }

            override fun onGetProfileSucceed(token: String, userId: String, genderInt: Int, birthday: String, name: String, picture: String) {
                OAuthManager.callbacks.dispatchOnMainThread {
                    this.onSuccess(
                        OAuthResponse(
                            LoginWay.Facebook,
                            token,
                            userId,
                            genderInt,
                            birthday,
                            name,
                            ""
                        ).apply {
                            source = sourceFrom
                            this.loginType = loginTypeFrom
                        }
                    )
                }
            }
        })
    }

    fun logout() {
        GlobalContext.get().get<IFaceBookSdkBridge>().logOut()
    }

    override fun onDestroy() {
        super.onDestroy()
        facebookAuth.onDestroy()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        facebookAuth.onActivityResult(requestCode, resultCode, data)
    }
}