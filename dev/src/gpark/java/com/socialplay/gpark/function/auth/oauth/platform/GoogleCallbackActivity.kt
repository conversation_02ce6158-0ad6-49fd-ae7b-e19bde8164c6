package com.socialplay.gpark.function.auth.oauth.platform

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContract
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.databinding.ActivityOauthCallbackBinding
import com.socialplay.gpark.function.auth.oauth.OAuthManager
import com.socialplay.gpark.function.overseabridge.bridge.IGoogleSdkBridge
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.property.viewBinding
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * @author: ning.wang
 * @date: 2021-09-27 10:47 上午
 * @desc:
 */
class GoogleCallbackActivity : BaseActivity() {

    companion object {
        private const val TAG = "GoogleCallbackActivity"
        const val EXTRA_IS_LOGIN = "isLogin"
        const val EXTRA_SOURCE = "source"
        const val EXTRA_LOGIN_TYPE = "loginType"
    }

    override val binding by viewBinding(ActivityOauthCallbackBinding::inflate)

    private var activityResultLauncher: ActivityResultLauncher<Intent>? = null

    /**
     * [com.socialplay.gpark.data.model.LoginSource]
     */
    private var sourceFrom: String? = null
    /**
     * [com.socialplay.gpark.data.model.LoginType]
     */
    private var loginTypeFrom: String? = null
    private val googleSdkBridge: IGoogleSdkBridge by lazy { GlobalContext.get().get() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val isLogin = intent.getBooleanExtra(EXTRA_IS_LOGIN, true)
        sourceFrom = intent.getStringExtra(EXTRA_SOURCE)
        loginTypeFrom = intent.getStringExtra(EXTRA_LOGIN_TYPE)

        if (!googleSdkBridge.isEnable()){
            OAuthManager.callbacks.dispatchOnMainThread {
                this.onFailed(LoginWay.Google, getString(R.string.not_support_cap), 0, "SDK disabled")
            }
            finish()
            return
        }

        if (!isLogin) {
            logout {}
            return
        }

        activityResultLauncher = registerForActivityResult(GoogleActivityResultContract()) {
            onActivityResult(it)
        }

        if (googleSdkBridge.isExpired(this)) {
            log("Google has account. account：isExpired")
            // 避免重复登入
            logout(true) {
                login()
            }
            return
        }
        login()
    }

    fun login() {


        val googleSignIntent = googleSdkBridge.getSignInIntent(this)
        if (activityResultLauncher == null) {
            finish()
            return
        }

        activityResultLauncher?.launch(googleSignIntent)
    }

    fun logout(isLogin: Boolean = false, callback: () -> Unit) {
        googleSdkBridge.logout(this, object : IGoogleSdkBridge.IResultCallback {
            override fun onLogoutSucceed() {
                if (!isLogin) finish()
                log("Google logout has success.")
                callback.invoke()
            }

            override fun onFailed(exception: Exception) {
                if (!isLogin) finish()
                log("Google logout has failure. error：$exception")
                callback.invoke()
            }

            override fun onCancel() {
                if (!isLogin) finish()
                log("Google logout has cancel.")
            }

            override fun onComplete() {
                if (!isLogin) finish()
                log("Google logout has complete.")
            }

        })
    }

    private fun onActivityResult(data: Intent?) {

        googleSdkBridge.getSignedInAccountFromIntent(data, object : IGoogleSdkBridge.IResultCallback {
            override fun onSignInSuccess(token: String, userId: String, genderInt: Int, birthday: String, name: String, picture: String) {
                OAuthManager.callbacks.dispatchOnMainThread {
                    if (token.isEmpty()) {
                        log("Google login has failure. error：${token}")
                        this.onFailed(LoginWay.Google, getString(R.string.login_fail), 0, "Empty token.")
                        return@dispatchOnMainThread
                    }
                    this.onSuccess(
                        OAuthResponse(
                            LoginWay.Google,
                            token,
                            userId,
                            genderInt,
                            birthday,
                            name,
                            picture
                        ).apply {
                            source = sourceFrom
                            this.loginType = loginTypeFrom
                        }
                    )
                    log("Google login has success. token：${token}")
                }
                finish()
            }

            override fun onFailed(exception: Exception) {
                finish()
                OAuthManager.callbacks.dispatchOnMainThread {
                    log("Google login has failure. error：${exception}")
                    this.onFailed(LoginWay.Google, getString(R.string.login_fail), 0, exception.javaClass.simpleName)
                }
            }

            override fun onCancel() {
                finish()
                OAuthManager.callbacks.dispatchOnMainThread {
                    log("Google login has cancel.")
                    this.onCancel(LoginWay.Google)
                }
            }

            override fun onComplete() {
                finish()
                log("Google login has complete.")
            }
        })
    }

    class GoogleActivityResultContract : ActivityResultContract<Intent, Intent?>() {
        override fun createIntent(context: Context, input: Intent): Intent {
            return input
        }

        override fun parseResult(resultCode: Int, data: Intent?): Intent? {
            return data
        }

    }

    private fun log(str: String) {
        Timber.tag(TAG).d(str)
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0)
    }

    override fun onDestroy() {
        super.onDestroy()
        activityResultLauncher?.unregister()
        activityResultLauncher = null
    }

}