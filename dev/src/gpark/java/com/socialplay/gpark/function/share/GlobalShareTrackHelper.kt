package com.socialplay.gpark.function.share

import com.socialplay.gpark.data.model.share.SharePlatform

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/22
 *     desc   :
 * </pre>
 */
object GlobalShareTrackHelper {

    fun trackResultParam(
        platform: String?,
        map: HashMap<String, Any>,
        extra: String?
    ) {
        when (platform) {
            SharePlatform.PLATFORM_YOUTUBE,
            SharePlatform.PLATFORM_TIKTOK,
            SharePlatform.PLATFORM_SNAPCHAT -> {
                if (extra != null) {
                    map["extra"] = platform
                }
            }
        }
    }

    fun trackAvatarShareResult(platform: String): <PERSON><PERSON>an {
        return when (platform) {
            SharePlatform.PLATFORM_LINK -> {
                true
            }

            SharePlatform.PLATFORM_SNAPCHAT -> {
                true
            }

            else -> false
        }
    }
}