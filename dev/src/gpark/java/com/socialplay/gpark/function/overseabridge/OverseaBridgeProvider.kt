package com.socialplay.gpark.function.overseabridge


class OverseaBridgeProvider(private val factory: IOverseaBridgeProvider) : IOverseaBridgeProvider by factory {

    companion object {

        val instance: OverseaBridgeProvider by lazy {
            val factory = findFactory()
            OverseaBridgeProvider(factory)
        }

        private fun findFactory(): IOverseaBridgeProvider {
//            val packageName = StartupContext.get().application.packageName
            // 后面有时间可以考虑用其他实现做
            return Class.forName("com.socialplay.gpark.overseabridge.OverseaBridgeProviderImpl").newInstance() as IOverseaBridgeProvider
        }
    }
}