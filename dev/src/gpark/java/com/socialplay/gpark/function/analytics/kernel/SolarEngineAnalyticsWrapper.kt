package com.socialplay.gpark.function.analytics.kernel

import com.meta.pandora.data.entity.Event
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.overseabridge.bridge.ISolarEngineBridge
import org.json.JSONObject
import org.koin.core.context.GlobalContext

/**
 * Desc: SolarEngine 发送埋点
 */
object SolarEngineAnalyticsWrapper {

    private val whiteList: Set<String> = setOf(
        "c_app_time",
        "c_tab_click",
        "c_app_detail",
        "c_click_download",
        "c_event_launch_game_success",
        "c_event_play_game_status",
        "c_play_game",
        "c_event_show_page",
        "c_login_succeed",
        "c_mw_login_room_manager_ready",
        "c_mw_start_assign_room_over2",
        "c_mw_start_link_ds_over",
        "c_mw_start_game_open",

        "c_af_playtime15",
        "c_af_playtime20",
        "c_af_playtime25",
        "c_af_playtime30",
        "c_af_playtime40",
        "c_af_playtime50",

        "c_af_playtime1",
        "c_af_playtime2",
        "c_af_playtime3",
        "c_af_playtime7",

        "c_af_played_game_1",
        "c_af_played_game_2",
        "c_af_played_game_3",
        "c_af_played_game_4",
        "c_af_played_game_5",
        "c_af_played_game_more_than_5",
        "c_af_age_9_18",

        "c_se_api_request_time_le80",
        "c_se_api_request_time_le100",
        "c_se_api_request_time_le120",
        "c_se_api_request_time_le140",
        "c_se_api_request_time_le160",
        "c_se_api_request_time_le180",
        "nickname_page_next_click",
        EventConstants.UGC_TEMPLATE_PAGE_SHOW.kind,
        EventConstants.EVENT_UGC_CREATE_EDIT_START.kind
    )

    private val solarEngineBridge: ISolarEngineBridge by lazy { GlobalContext.get().get() }

    fun track(event: Event, params: Map<String, Any>? = null) {
        if (whiteList.contains(event.kind)) {
            solarEngineBridge.logEvent(
                event.kind,
                JSONObject(),
                JSONObject(params ?: mutableMapOf<String, Any>())
            )
        }
    }

}