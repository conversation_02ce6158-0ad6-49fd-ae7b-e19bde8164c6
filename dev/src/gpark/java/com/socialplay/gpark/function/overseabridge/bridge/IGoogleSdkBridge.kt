package com.socialplay.gpark.function.overseabridge.bridge

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import com.socialplay.gpark.data.model.GooglePayResultData
import com.socialplay.gpark.data.model.GoogleProductResult
import com.socialplay.gpark.data.model.Product
import com.socialplay.gpark.data.model.pay.PayParams
import com.socialplay.gpark.data.model.pay.SubsData
import java.lang.Exception

interface IGoogleSdkBridge {

    val billingClientBridge: IBillingClientBridge

    fun isEnable(): Boolean {
        return true
    }
    suspend fun getGoogleId(context: Context): String?
    fun isExpired(context: Context): Boolean
    fun logout(activity: Activity, callback: IResultCallback)
    fun getSignInIntent(activity: Activity): Intent
    fun getSignedInAccountFromIntent(data: Intent?, callback: IResultCallback)
    fun showInAppReview(fragment: Fragment, showSuccessCallback: (Boolean) -> Unit)

    interface IResultCallback {
        fun onSignInSuccess(token: String, userId: String, genderInt: Int, birthday: String, name: String, picture: String) {}
        fun onLogoutSucceed() {}
        fun onFailed(exception: Exception)
        fun onCancel()
        fun onComplete()

    }

    interface IBillingClientBridge {

        val productLiveData: LiveData<MutableList<Product>>
        val subsProductLiveData: LiveData<MutableList<Product>>

        fun init(application: Application, isHistory: Boolean = false)

        fun setPurchasesUpdatedListener(onSuccess: (googlePayResultData: GooglePayResultData) -> Unit,
                                        onFail: (String,Int) -> Unit,
                                        onCancel: (String) -> Unit,
                                        onConsumeGood: (googlePayResultData: GooglePayResultData) -> Unit,
                                        onPayDialogShow: (Boolean) -> Unit)

        fun consumeAsync(purchaseToken: String)
        suspend fun acknowledgePurchase(purchaseToken: String)

        fun checkConnected()
        fun queryProductDetailsAsync(productList: List<SubsData>, type: String, callBack: (Pair<String?, MutableList<Product>>) -> Unit?)
        fun clearProducts()
        fun getRequestOrderBody(
            productID: String,
            count: Int,
            payTunnel: Int,
            payChannel: Int,
            sceneCode: Int,
            productType: String,
            callback: (Pair<Boolean, GoogleProductResult>?) -> Unit
        )

        fun startPay(activity: Activity, payParams: PayParams)
        fun queryPurchasesAsync()
    }
}