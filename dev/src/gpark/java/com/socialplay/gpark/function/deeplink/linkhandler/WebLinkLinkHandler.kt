package com.socialplay.gpark.function.deeplink.linkhandler

import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.LinkHandleResult
import com.socialplay.gpark.function.deeplink.LinkHandler
import com.socialplay.gpark.function.deeplink.LinkHandlerChain
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.util.isHttp

class WebLinkLinkHandler : LinkHandler {
    override fun handle(chain: <PERSON><PERSON>andler<PERSON>hain, data: LinkData): LinkHandleResult {
        val url = data.uri.getQueryParameter(MetaDeepLink.PARAM_URL)
            ?: return LinkHandleResult.Failed("no url")
        if (url.isHttp()) {
            MetaRouter.Web.navigate(data.navHost, url = url)
        } else {
            return LinkHandleResult.Failed("url invalid")
        }
        return LinkHandleResult.Success
    }
}