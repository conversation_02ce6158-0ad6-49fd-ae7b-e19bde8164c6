package com.socialplay.gpark.di

import com.socialplay.gpark.function.overseabridge.OverseaBridgeProvider
import org.koin.dsl.module

val overseaBridge = module {
    single { OverseaBridgeProvider.instance.provideFaceBookSdkBridge() }
    single { OverseaBridgeProvider.instance.provideTiktokSdkBridge() }
    single { OverseaBridgeProvider.instance.provideGoogleSdkBridge() }
    single { OverseaBridgeProvider.instance.provideFirebaseSdkBridge() }
    single { OverseaBridgeProvider.instance.provideAdSdkBridge() }
    single { OverseaBridgeProvider.instance.provideFcmSdkBridge() }
    single { OverseaBridgeProvider.instance.provideUpdateBridge() }
    single { OverseaBridgeProvider.instance.provideSolarEngineBridge() }
    single { OverseaBridgeProvider.instance.provideSnapchatSdkBridge() }

}