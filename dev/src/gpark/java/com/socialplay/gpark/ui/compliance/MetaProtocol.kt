package com.socialplay.gpark.ui.compliance

import androidx.fragment.app.FragmentActivity
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.kv.MetaKV
import org.koin.core.context.GlobalContext

/**
 * @author: ning.wang
 * @date: 2021-05-31 11:14 上午
 * @desc: 合规弹窗入口
 */
object MetaProtocol {

    // 是否已经同意用户协议
    var agreedProtocol = true
    /**
     * 检查是否需要展示合规弹窗
     *
     * @return
     */
    fun needLegal(): Boolean {
        return BuildConfig.IS_NEED_LEGAL
    }

    fun showProtocolDialog(activity: FragmentActivity, callback: () -> Unit) {

    }
}