package com.socialplay.gpark.ui.developer

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.function.overseabridge.bridge.IFaceBookSdkBridge
import com.socialplay.gpark.function.overseabridge.bridge.IFcmSdkBridge
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.toast
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

object DemoWrapper {

    const val GET_FCM_TOKEN = -5
    fun clickTestFacebook(requireContext: Context) {
        GlobalContext.get().get<IFaceBookSdkBridge>().debugLogEvent(requireContext, "event_hh")
    }

    fun getActionList(): MutableMap<String, Int> {
        return mutableMapOf(
            "Get FCM Token" to GET_FCM_TOKEN,
        )
    }

    fun handleAction(name: String, navId: Int, viewLifecycleOwner: LifecycleOwner): Boolean {
        when (navId) {
            GET_FCM_TOKEN -> {
                viewLifecycleOwner.lifecycleScope.launch {
                    try {
                        val token = GlobalContext.get().get<IFcmSdkBridge>().getRegistrationToken()
                        val msg = "FCMToken:$token"
                        Timber.d(msg)
                        ToastUtil.showShort(msg)
                    } catch (e: Exception) {
                        ToastUtil.showShort(e.message ?: "")
                    }
                }
                return true
            }

            else -> {
                return false
            }
        }
        return false
    }

    fun testJavaCrash() {
        throw Exception("this is an exception for test purpose.")
    }

    fun testNativeCrash() {

    }

    fun testANRCrash() {
        while (true) {}
    }
}