package com.socialplay.gpark.ui.login

import android.text.SpannableStringBuilder
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.model.LOGIN_TYPE_VISITOR
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.Source
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.util.SpanClick
import com.socialplay.gpark.util.extension.getColorByRes
import java.util.Locale

object LoginSpecialWrapper {

    val visitorWay = LOGIN_TYPE_VISITOR

    fun getAgeRestrictionIconRes(): Int {
        return R.drawable.icon_logo
    }

    fun showAgeRestrictionDialog(requireActivity: FragmentActivity, fragment: Fragment) {

    }

    fun showProtocolDialogBottomFragment(activity: FragmentActivity, fragment: Fragment, function: () -> Unit) {
        function.invoke()
    }

    fun getAgreementStringBuilder(fragment: Fragment, h5PageConfig: H5PageConfigInteractor): SpannableStringBuilder {
        val lang = Locale.getDefault().language
        val color = fragment.getColorByRes(R.color.secondary_color_1)
        val spannable =
//            if (lang.equals(MetaLanguageItem.JAPANESE.locale.language) || lang.equals(MetaLanguageItem.KOREAN.locale.language)) {
//            com.socialplay.gpark.util.SpannableHelper.Builder()
//                .text(fragment.getString(R.string.intl_i_read_agree_user_agreement))
//                .textAppearance(fragment.requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
//                .click(SpanClick {
//                    Analytics.track(EventConstants.EVENT_USER_AGREEMENT_CLICK) {
//                        put("source", Source.SOURCE_LOGIN)
//                    }
//                    val item = h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.USER_AGREEMENT)
////                        MetaRouter.Web.navigate(this, item)
//                    jumpWeb(fragment.requireActivity(), item.url)
//                })
//                .text(fragment.getString(R.string.intl_i_read_agree_and))
//                .text(fragment.getString(R.string.intl_i_read_agree_privacy_policy))
//                .textAppearance(fragment.requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
//                .click(SpanClick {
//                    Analytics.track(EventConstants.EVENT_PRIVACY_AGREEMENT_CLICK) {
//                        put("source", Source.SOURCE_LOGIN)
//                    }
//
//                    val item = h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.PRIVACY_AGREEMENT)
////                        MetaRouter.Web.navigate(this, item)
//                    jumpWeb(fragment.requireActivity(), item.url)
//                })
//                .text(fragment.getString(R.string.intl_i_read_agree))
//                .build()
//        } else {
            com.socialplay.gpark.util.SpannableHelper.Builder()
                .text(fragment.getString(R.string.i_have_read))
                .text(fragment.getString(R.string.i_agree))
//                .textAppearance(fragment.requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
                .text(fragment.getString(R.string.intl_i_read_agree_user_agreement))
//                .textAppearance(fragment.requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
                .click(SpanClick {
                    Analytics.track(EventConstants.EVENT_USER_AGREEMENT_CLICK) {
                        put("source", Source.SOURCE_LOGIN)
                    }
                    val item = h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.USER_AGREEMENT)
//                        MetaRouter.Web.navigate(this, item)
                    jumpWeb(fragment, item.url)
                })
                .color(color)
                .text(fragment.getString(R.string.intl_i_read_agree_and))
//                .textAppearance(fragment.requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
                .text(fragment.getString(R.string.intl_i_read_agree_privacy_policy))
//                .textAppearance(fragment.requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
                .click(SpanClick {
                    Analytics.track(EventConstants.EVENT_PRIVACY_AGREEMENT_CLICK) {
                        put("source", Source.SOURCE_LOGIN)
                    }

                    val item = h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.PRIVACY_AGREEMENT)
//                        MetaRouter.Web.navigate(this, item)
                    jumpWeb(fragment, item.url)
                })
                .color(color)
                .build()
//        }
        return spannable
    }


    private fun jumpWeb(fragment: Fragment, url: String) {
        MetaRouter.Web.navigate(
            fragment,
            title = null,
            url = url,
            showTitle = false
        )
    }

    fun continueLogin(fragment: Fragment, loginWay: LoginWay, source: String, continueInfo: ContinueAccountInfo): Boolean {
        when (loginWay) {
            LoginWay.GparkId -> {
                MetaRouter.Login.login(
                    fragment,
                    loginSource = source,
                    onlyLogin = true,
                    continueAccountInfo = continueInfo,
                    successToMain = true
                )
                return true
            }

            else -> {
                return false
            }
        }
    }
}