package com.socialplay.gpark.ui.account

import android.app.Activity
import android.app.Application
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.function.auth.oauth.IOAuthCallback
import com.socialplay.gpark.function.auth.oauth.OAuthManager
import com.socialplay.gpark.util.extension.LifecycleCallback
import com.socialplay.gpark.util.extension.replaceChinese
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2022/6/29 4:18 下午
 * @describe:
 */
class ConnectedAccountsViewModel(val accountInteractor: AccountInteractor) : ViewModel(), IOAuthCallback {

    companion object {
        // The account has already been registered and cannot be bound again
        private const val ACCOUNT_BIND_ALREADY_ERROR_CODE = 409
    }

    private var accountObserver: Observer<MetaUserInfo?>

    private val _accountLiveData: MutableLiveData<MetaUserInfo?> = MutableLiveData()
    val accountLivedata get() = _accountLiveData

    data class LinkResult(
        val way: LoginWay,
        val success: Boolean,
        // true绑定，false解绑
        val link: Boolean,
        val toastMsg: String?
    )

    // 登录方式、是否成功、toast
    val bindCallback: LifecycleCallback<(LinkResult) -> Unit> = LifecycleCallback()


    init {
        accountObserver = Observer<MetaUserInfo?> { _accountLiveData.postValue(it) }
        accountInteractor.accountLiveData.observeForever(accountObserver)
    }

    fun oauthFacebook(activity: Activity) {
        OAuthManager.oauth(LoginWay.Facebook, activity, LoginSource.BindThird.source, LoginType.BindThird, oAuthCallback = this)
    }

    fun oauthGoogle(activity: Activity) {
        OAuthManager.oauth(LoginWay.Google, activity, LoginSource.BindThird.source, LoginType.BindThird, oAuthCallback = this)
    }

    fun unBindFacebook() = viewModelScope.launch {
        accountInteractor.facebookUnBind().collect {
            val app = GlobalContext.get().get() as Application
            val msg = if (it.succeeded && it.data == true) app.getString(R.string.account_unbind_success) else it.message.replaceChinese(app.getString(R.string.account_unbind_fail))
            bindCallback.dispatchOnMainThread {
                invoke(LinkResult(LoginWay.Facebook, it.succeeded && it.data == true, false, msg))
            }
        }
    }

    fun unBindGoogle() = viewModelScope.launch {
        accountInteractor.googleUnBind().collect {
            val app = GlobalContext.get().get() as Application
            val msg = if (it.succeeded && it.data == true) app.getString(R.string.account_unbind_success) else it.message.replaceChinese(app.getString(R.string.account_unbind_fail))
            bindCallback.dispatchOnMainThread {
                invoke(LinkResult(LoginWay.Google, it.succeeded && it.data == true, false, msg))
            }
        }
    }

    /**
     * 是否只有一种方式登录
     */
    fun isSingleWay(): Boolean {
        val userInfo = accountInteractor.accountLiveData.value
        var count = 0
        if (userInfo?.bindFacebook == true) {
            count++
        }
        if (userInfo?.bindGoogle == true) {
            count++
        }
        return (count <= 1)
    }

    private fun facebookBind(response: OAuthResponse) = viewModelScope.launch {
        accountInteractor.facebookBind(response).collect {
            val app = GlobalContext.get().get() as Application
            val msg = if (it.succeeded) app.getString(R.string.account_bind_success) else it.message.replaceChinese(app.getString(R.string.account_bind_error))
            bindCallback.dispatchOnMainThread {
                invoke(LinkResult(LoginWay.Facebook, it.succeeded && it.data == true, true, msg))
            }
        }
    }

    private fun googleBind(response: OAuthResponse) = viewModelScope.launch {
        accountInteractor.googleBind(response).collect {
            val app = GlobalContext.get().get() as Application
            val msg = if (it.succeeded) app.getString(R.string.account_bind_success) else it.message.replaceChinese(app.getString(R.string.account_bind_error))
            bindCallback.dispatchOnMainThread {
                invoke(LinkResult(LoginWay.Google, it.succeeded && it.data == true, true, msg))
            }
        }
    }

    override fun onSuccess(response: OAuthResponse) {
        if (response.token.isBlank()) return
        when (response.oauthThirdWay) {
            LoginWay.Facebook -> facebookBind(response)
            LoginWay.Google -> googleBind(response)
            else -> {}
        }
    }

    override fun onFailed(oauthThirdWay: LoginWay, msg: String?, code: Int, reason: String?) {
        bindCallback.dispatchOnMainThread {
            invoke(LinkResult(oauthThirdWay, success = false, link = true, toastMsg = msg))
        }
    }

    override fun onCancel(oauthThirdWay: LoginWay) {
        bindCallback.dispatchOnMainThread {
            invoke(LinkResult(oauthThirdWay, success = false, link = true, toastMsg = ""))
        }
    }

    override fun onCleared() {
        super.onCleared()
        accountInteractor.accountLiveData.removeObserver(accountObserver)
    }

}