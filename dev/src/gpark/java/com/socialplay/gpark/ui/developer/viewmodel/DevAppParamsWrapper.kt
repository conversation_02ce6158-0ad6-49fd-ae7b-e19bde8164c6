package com.socialplay.gpark.ui.developer.viewmodel

import android.content.Context
import com.socialplay.gpark.data.interactor.DeviceInteractorWrapper
import com.socialplay.gpark.data.interactor.GoogleInteractor
import com.socialplay.gpark.data.interactor.SolarEngineInteractor
import com.socialplay.gpark.util.PackageUtil
import org.koin.core.context.GlobalContext

object DevAppParamsWrapper {
    private val googleInteractor by lazy { GlobalContext.get().get<GoogleInteractor>() }
    private val solarEngineInteractor by lazy { GlobalContext.get().get<SolarEngineInteractor>() }
    fun getAppParams(context: Context): MutableList<Pair<String, Any>> {
        return mutableListOf(
            "firebaseId" to DeviceInteractorWrapper.firebaseId,
            "googleAdId" to DeviceInteractorWrapper.googleAdId,
            "user_pseudo_id" to DeviceInteractorWrapper.user_pseudo_id,
            "Install referer" to "${googleInteractor.playInstallReferer.value}",
            "SolarEngine conversion data" to "${solarEngineInteractor.conversionData.value}",
            "SolarEngine distinct id" to "${solarEngineInteractor.getDistinctId()}",
            "isSignedByGoogle" to "${PackageUtil.isSignedByGoogle(context)}",
        )
    }
}