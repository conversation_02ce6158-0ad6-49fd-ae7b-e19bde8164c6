package com.socialplay.gpark.ui.account

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.databinding.FragmentConnectedAccountsBinding
import com.socialplay.gpark.databinding.LayoutCustomSettingLinkAccoountBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.base.BaseFragment
import com.socialplay.gpark.ui.view.preference.PreferenceItemStyle
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.extension.getDrawableByRes
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber


class ConnectedAccountsFragment : BaseFragment<FragmentConnectedAccountsBinding>() {

    private val viewModel by viewModel<ConnectedAccountsViewModel>()

    private val accountInteractor: AccountInteractor by inject()
    private val args by navArgs<ConnectedAccountsFragmentArgs>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentConnectedAccountsBinding? {
        return FragmentConnectedAccountsBinding.inflate(inflater, container, false)
    }

    override fun getFragmentName() = PageNameConstants.FRAGMENT_NAME_CONNECTED_ACCOUNTS

    override fun init() {
        initView()
        initData()
    }

    private fun initData() {
        viewModel.bindCallback.observe(viewLifecycleOwner) {
            Analytics.track(EventConstants.EVENT_SECURITY_CONNECTED_RESULT) {
                put(EventConstants.KEY_LOGIN_SOURCE, it.way.way)
                put(EventConstants.KEY_LOGIN_RESULT, if (it.success) "1" else "2")
            }
            binding.vLoading.gone()
            it.toastMsg?.let { toast(it) }
        }
        viewModel.accountLivedata.observe(viewLifecycleOwner) {
            it?.let { fillData(it) }
        }
    }

    private fun initView() {
        binding.apply {
            tblTitleBar.setOnBackClickedListener {
                findNavController().popBackStack()
            }

            pivFacebook.setOnAntiViolenceClickListener {
                bindOrUnBindFacebook()
            }
            pivGoogle.setOnAntiViolenceClickListener {
                bindOrUnBindGoogle()
            }
        }
    }

    override fun loadFirstData() {
        fillData(accountInteractor.accountLiveData.value)
    }

    private fun fillData(userInfo: MetaUserInfo?) {
        Timber.tag("ConnectedAccounts").d("fillData $userInfo")
        if (userInfo == null) return

        updateFacebookBind(userInfo)
        updateGoogleBind(userInfo)
    }

    private fun updateFacebookBind(userInfo: MetaUserInfo) {
        binding.pivFacebook.updateStyleParams<PreferenceItemStyle.Custom> {
            val customView = LayoutCustomSettingLinkAccoountBinding.bind(view)

            Glide.with(this@ConnectedAccountsFragment)
                .load(userInfo.facebookPortrait)
                .placeholder(R.drawable.icon_login_facebook)
                .circleCrop()
                .into(customView.ivIcon)

            customView.tvTitle.text = getString(R.string.login_with_facebook)

            if (userInfo.bindFacebook) {
                customView.tvOption.apply {
                    text = getString(R.string.connected_accounts_op_disconnect)
                    background = getDrawableByRes(R.drawable.bg_connected_accounts_disconnect)
                    setTextColor(Color.parseColor("#17191C"))
                }
                customView.tvNickname.apply {
                    visible(true)
                    text = userInfo.facebookNickname
                }
            } else {
                customView.tvOption.apply {
                    text = getString(R.string.connected_accounts_op_connect)
                    background = getDrawableByRes(R.drawable.bg_connected_accounts_connect)
                    setTextColor(Color.parseColor("#ffffff"))
                }
                customView.tvNickname.visible(false)
            }
        }
    }

    private fun updateGoogleBind(userInfo: MetaUserInfo) {
        binding.pivGoogle.updateStyleParams<PreferenceItemStyle.Custom> {
            val customView = LayoutCustomSettingLinkAccoountBinding.bind(view)

            Glide.with(this@ConnectedAccountsFragment)
                .load(userInfo.googlePortrait)
                .placeholder(R.drawable.icon_login_google)
                .circleCrop()
                .into(customView.ivIcon)

            customView.tvTitle.text = getString(R.string.login_with_google)

            if (userInfo.bindGoogle) {
                customView.tvOption.apply {
                    text = getString(R.string.connected_accounts_op_disconnect)
                    background = getDrawableByRes(R.drawable.bg_connected_accounts_disconnect)
                    setTextColor(Color.parseColor("#17191C"))
                }
                customView.tvNickname.apply {
                    visible(true)
                    text = userInfo.googleNickname
                }
            } else {
                customView.tvOption.apply {
                    text = getString(R.string.connected_accounts_op_connect)
                    background = getDrawableByRes(R.drawable.bg_connected_accounts_connect)
                    setTextColor(Color.parseColor("#ffffff"))
                }
                customView.tvNickname.visible(false)
            }
        }
    }

    private fun bindOrUnBindFacebook() {
        val isFacebookBind = accountInteractor.accountLiveData.value?.bindFacebook ?: false
        if (isFacebookBind) {
            Analytics.track(EventConstants.EVENT_UNBIND_CLICK) {
                put("way", "facebook")
            }
        } else {
            Analytics.track(EventConstants.EVENT_BIND_CLICK) {
                put("way", "facebook")
            }
        }
        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.loading_net_error)
            return
        }
        if (!isFacebookBind) {
            showLoading(getString(R.string.binding))
            viewModel.oauthFacebook(requireActivity())
            return
        }
//        val isSingleWay = viewModel.isSingleWay()
//        if (isSingleWay) {
//            binding.vLoading.gone()
//            toast(getString(R.string.account_unbind_signle_error))
//            return
//        }
        showLoading(getString(R.string.unbinding))
        viewModel.unBindFacebook()
    }

    private fun bindOrUnBindGoogle() {
        val isGoogleBind = accountInteractor.accountLiveData.value?.bindGoogle ?: false
        if (isGoogleBind) {
            Analytics.track(EventConstants.EVENT_UNBIND_CLICK) {
                put("way", "google")
            }
        } else {
            Analytics.track(EventConstants.EVENT_BIND_CLICK) {
                put("way", "google")
            }
        }
        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.loading_net_error)
            return
        }
        if (!isGoogleBind) {
            showLoading(getString(R.string.binding))
            viewModel.oauthGoogle(requireActivity())
            return
        }
//        val isSingleWay = viewModel.isSingleWay()
//        if (isSingleWay) {
//            binding.vLoading.gone()
//            toast(getString(R.string.account_unbind_signle_error))
//            return
//        }
        showLoading(getString(R.string.unbinding))
        viewModel.unBindGoogle()
    }


    private fun showLoading(msg: String) {
        binding.vLoading.showLoading(loadingTips = msg)
        binding.vLoading.visible()
    }

    override fun onResume() {
        super.onResume()
        Analytics.track(EventConstants.EVENT_LOGIN_SHOW_PAGE) {
            put(EventConstants.KEY_LOGIN_PAGE_NAME, getFragmentName())
            put(EventConstants.KEY_LOGIN_SOURCE, args.source ?: LoginPageSource.Unknown.source)
            args.gameId?.let {
                put(EventConstants.KEY_LOGIN_GAME_CODE, it)
            }
        }
    }
}