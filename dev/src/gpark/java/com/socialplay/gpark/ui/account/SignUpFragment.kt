package com.socialplay.gpark.ui.account

import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.TextPaint
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import com.google.android.material.textfield.TextInputEditText
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.mgs.ViolateMessage
import com.socialplay.gpark.databinding.FragmentSignUpBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.Source
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.dialog.LoadingDialogFragment
import com.socialplay.gpark.ui.login.LoginViewModel
import com.socialplay.gpark.ui.login.LoginViewModelState
import com.socialplay.gpark.ui.view.InterceptClickEventLinkMovementMethod
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.addTextChangedListener
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.doOnFocusChange
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.replaceChinese
import com.socialplay.gpark.util.extension.setFontFamily
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.Locale
import com.airbnb.mvrx.fragmentViewModel

/**
 * 注册页面
 */
class SignUpFragment : BaseFragment<FragmentSignUpBinding>(R.layout.fragment_sign_up) {

    private val viewModel by viewModel<SignUpViewModel>()
    private val loginViewModel: LoginViewModel by fragmentViewModel()
    private val args by navArgs<SignUpFragmentArgs>()

    private val h5PageConfig by inject<H5PageConfigInteractor>()

    private var loadingDialogFragment: LoadingDialogFragment? = null

    private var showAccountError = false
    private var showPasswordError = false

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentSignUpBinding? {
        return FragmentSignUpBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initView()
        initData()
    }

    private fun initView() {
        Analytics.track(EventConstants.SIGN_UP_PAGE_SHOW)
        binding.apply {
            viewLifecycleOwner.lifecycleScope.launch {
                delay(10)
                rlContentArea.fullScroll(View.FOCUS_DOWN)
            }

            val agreementContent = getAgreementStringBuilder()
            tvAgreement.text = agreementContent
            tvAgreement.movementMethod = InterceptClickEventLinkMovementMethod(tvAgreement)
            tvAgreement.isClickable = false
            tvAgreement.isContextClickable = false

            requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
                navigateUp()
            }

            vGoogle.setOnAntiViolenceClickListener {
                InputUtil.hideKeyboard(it)
                loginViewModel.auth(
                    LoginWay.getDefaultSignWay(),
                    requireActivity(),
                    args.source ?: LoginSource.Unknown.source,
                    LoginType.CreateAccount
                )
            }

            tvLogin.setOnAntiViolenceClickListener {
                InputUtil.hideKeyboard(it)

                viewModel.signUp(requireContext(), LoginType.CreateAccount)
            }
            ivClearName.setOnAntiViolenceClickListener {
                binding.etAccount.setText("")
            }
            ivClearPassword.setOnAntiViolenceClickListener {
                binding.etPassword.setText("")
            }

            ivPasswordVisibility.setOnClickListener {
                viewModel.togglePasswordVisibility()
            }

            etAccount.addTextChangedListener(viewLifecycleOwner) {
                viewModel.postAccountValue(it?.toString()?.trim())
                setFont(etAccount)
            }

            etPassword.addTextChangedListener(viewLifecycleOwner) {
                viewModel.postPasswordValue(it?.toString()?.trim())
                setFont(etPassword)
            }
            etAccount.doOnFocusChange(viewLifecycleOwner) { _, focus ->
                binding.tvAccountFormatErrTip.visible(showAccountError || focus)
                binding.tvPasswordFormatErrTip.visible(showPasswordError || !focus)
            }
            etPassword.doOnFocusChange(viewLifecycleOwner) { _, focus ->
                binding.tvAccountFormatErrTip.visible(showAccountError || !focus)
                binding.tvPasswordFormatErrTip.visible(showPasswordError || focus)
            }
            tbl.setOnBackAntiViolenceClickedListener {
                navigateUp()
            }

            visibleList(
                binding.vGoogle,
                binding.tvGoogle,
                binding.ivGoogle,
                binding.vLineOrL,
                binding.tvOr,
                binding.vLineOrR,
                binding.ivLoginAccountTip,
                visible = true
            )
        }
    }

    private fun setFont(editText: TextInputEditText) {
        if (editText.text?.toString()?.isBlank() == true) {
            editText.setFontFamily(R.font.poppins_regular_400)
        } else {
            editText.setFontFamily(R.font.poppins_semi_bold_600)
        }
    }

    private fun initData() {
        viewModel.accountCheckResultFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            showAccountError = it?.isValid == false
            if (showAccountError) {
                binding.tvAccountFormatErrTip.visible()
                binding.tvAccountFormatErrTip.text =
                    it?.description ?: getString(R.string.signup_account_input_error)
                binding.tvAccountFormatErrTip.setTextColorByRes(R.color.color_F55C45)
            } else {
                binding.tvAccountFormatErrTip.setText(R.string.signup_account_input_error)
                binding.tvAccountFormatErrTip.setTextColorByRes(R.color.color_666666 )
            }
        }

        viewModel.passwordCheckResultFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            showPasswordError = it?.isValid == false
            if (showPasswordError) {
                binding.tvPasswordFormatErrTip.visible()
                binding.tvPasswordFormatErrTip.text =
                    it?.description ?: getString(R.string.signup_password_input_error)
                binding.tvPasswordFormatErrTip.setTextColorByRes(R.color.color_F55C45)
            } else {
                binding.tvPasswordFormatErrTip.setText(R.string.signup_password_input_error)
                binding.tvPasswordFormatErrTip.setTextColorByRes(R.color.color_666666)
            }
        }

        viewModel.passwordVisibilityFlow.distinctUntilChanged()
            .collectWithLifecycleOwner(viewLifecycleOwner) {
                if (it) {
                    binding.ivPasswordVisibility.setImageResource(R.drawable.icon_login_visible_password)
                    binding.etPassword.transformationMethod =
                        HideReturnsTransformationMethod.getInstance()
                } else {
                    binding.ivPasswordVisibility.setImageResource(R.drawable.icon_login_hiden_password)
                    binding.etPassword.transformationMethod =
                        PasswordTransformationMethod.getInstance()
                }
                binding.etPassword.setSelection(binding.etPassword.length())
            }

        viewModel.accountFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            binding.ivClearName.visible(!it.isNullOrEmpty(), true)
            if (!it.isNullOrBlank()) {
                binding.inputAccount.setHint(R.string.text_account)
            } else {
                binding.inputAccount.setHint(R.string.enter_account)
            }
        }

        viewModel.passwordFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            val isEmpty = it.isNullOrEmpty()
            binding.ivPasswordVisibility.visible(!isEmpty, true)
            binding.ivClearPassword.visible(!isEmpty, true)
            if (!it.isNullOrBlank()) {
                binding.inputPassword.setHint(R.string.text_password)
            } else {
                binding.inputPassword.setHint(R.string.enter_password)
            }
        }

        viewModel.accountFlow.combine(viewModel.passwordFlow) { account, password ->
            account.isNullOrBlank() || password.isNullOrBlank()
        }.collectWithLifecycleOwner(viewLifecycleOwner) {
            binding.tvLogin.enableWithAlpha(!it)
        }

        viewModel.signUpStatusFlow.collectWithLifecycleOwner(viewLifecycleOwner) {
            when (it) {
                LoginState.Loading -> {
                    showLoading()
                }

                is LoginState.Succeeded -> {
                    Analytics.track(EventConstants.SIGN_UP_SIGN_UP_CLICK)
                    dismissLoading()
                    MetaRouter.Startup.createAvatar(this, false)
                }

                is LoginState.Failed -> {
                    dismissLoading()
                    val msg = it.message
                    toast(msg.replaceChinese(getString(R.string.sign_up_failed)))
                }

                else -> {
                    dismissLoading()
                }
            }
        }

        loginViewModel.onEach(
            LoginViewModelState::signStatus
        ) {
            when (it) {
                is LoginState.Loading -> {
                    showLoading()
                }

                is LoginState.Succeeded -> {
                    dismissLoading()
                    if (it.userInfo?.firstBindGoogle == true) {
                        MetaRouter.Startup.createAvatar(this, false)
                    } else {
                        MetaRouter.Main.tabHome(this, requireContext())
                    }
                }

                is LoginState.Failed -> {
                    dismissLoading()
                    val msg = it.message
                    if (msg.isNotBlank()) {
                        toast(msg)
                    }
                    if (it.violateMessage != null) {
                        showViolateDialogByInfo(it.violateMessage)
                    }
                }

                is LoginState.UserCanceled -> {
                    dismissLoading()
                }

                else -> {}
            }
        }
    }

    private fun showViolateDialogByInfo(violateMessage: ViolateMessage) {
        violateMessage.toArgs()?.let { MetaRouter.Report.violateRulesDialog(this, it) }
    }

    private fun getAgreementStringBuilder(): Spannable {
        val lang = Locale.getDefault().language
        val spannable =
//            if (lang.equals(MetaLanguageItem.JAPANESE.locale.language) || lang.equals(
//                MetaLanguageItem.KOREAN.locale.language
//            )
//        ) {
//            SpannableHelper.Builder()
//                .text(getString(R.string.intl_i_read_agree_user_agreement))
//                .textAppearance(requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
//                .click(SpanClick {
//                    Analytics.track(EventConstants.EVENT_USER_AGREEMENT_CLICK) {
//                        put("source", Source.SOURCE_SIGNUP)
//                    }
//                    val item =
//                        h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.USER_AGREEMENT)
//                    MetaRouter.Web.navigate(this, item)
//                })
//                .text(getString(R.string.intl_i_read_agree_and))
//                .text(getString(R.string.intl_i_read_agree_privacy_policy))
//                .textAppearance(requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
//                .click(SpanClick {
//                    Analytics.track(EventConstants.EVENT_PRIVACY_AGREEMENT_CLICK) {
//                        put("source", Source.SOURCE_SIGNUP)
//                    }
//
//                    val item =
//                        h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.PRIVACY_AGREEMENT)
//                    MetaRouter.Web.navigate(this, item)
//
//                })
//                .text(getString(R.string.intl_i_read_agree))
//                .build()
//        } else {
            SpannableHelper.Builder()
                .text(getString(R.string.i_have_read))
                .text(getString(R.string.i_agree))
                .textAppearance(requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
                .text(getString(R.string.intl_i_read_agree_user_agreement))
                .textAppearance(requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
                .click(SpanClick {
                    Analytics.track(EventConstants.EVENT_USER_AGREEMENT_CLICK) {
                        put("source", Source.SOURCE_SIGNUP)
                    }
                    val item =
                        h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.USER_AGREEMENT)
                    MetaRouter.Web.navigate(this, item)
                })
                .text(getString(R.string.intl_i_read_agree_and))
                .textAppearance(requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
                .text(getString(R.string.intl_i_read_agree_privacy_policy))
                .textAppearance(requireContext(), R.style.MetaTextView_S12_PoppinsMedium500)
                .click(SpanClick {
                    Analytics.track(EventConstants.EVENT_PRIVACY_AGREEMENT_CLICK) {
                        put("source", Source.SOURCE_SIGNUP)
                    }

                    val item =
                        h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.PRIVACY_AGREEMENT)
                    MetaRouter.Web.navigate(this, item)

                })
                .build()
//        }
        return spannable
    }

    inner class SpanClick(val call: () -> Unit) : ClickableSpan() {
        override fun onClick(widget: View) {
            call.invoke()
        }

        override fun updateDrawState(ds: TextPaint) {
            ds.isUnderlineText = false
            ds.color = Color.parseColor("#53535E")
        }
    }

    private fun showLoading() {
        loadingDialogFragment = MetaRouter.Dialog.loading(
            childFragmentManager,
            msg = getString(R.string.loading)
        )
    }

    private fun dismissLoading() {
        loadingDialogFragment?.dismissAllowingStateLoss()
        loadingDialogFragment = null
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        loginViewModel.init(
            args.source ?: LoginSource.Unknown.source,
            false,
            LoginViewModel.LOCATION_SIGN_UP
        )
    }

    override fun onPause() {
        InputUtil.hideKeyboard(binding.root)
        super.onPause()
    }

    override fun onDestroyView() {
        dismissLoading()
        super.onDestroyView()
    }

    override fun getPageName(): String {
        return PageNameConstants.FRAGMENT_NAME_SIGN_UP
    }
}