package com.socialplay.gpark.ui.account

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.user.Gender
import com.socialplay.gpark.data.model.user.ValueCheckedResult
import com.socialplay.gpark.util.AccountUtil
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch


class SignUpViewModel(
    private val accountInteractor: AccountInteractor,
    private val repository: IMetaRepository
) : ViewModel() {

    private val _accountFlow = MutableStateFlow<String?>(null)
    val accountFlow: Flow<String?> = _accountFlow
    private val _passwordFlow = MutableStateFlow<String?>(null)
    val passwordFlow: Flow<String?> = _passwordFlow

    private val _accountCheckResultFlow: MutableStateFlow<ValueCheckedResult?> = MutableStateFlow(null)
    val accountCheckResultFlow: Flow<ValueCheckedResult?> = _accountCheckResultFlow
    private val _passwordCheckResultFlow: MutableStateFlow<ValueCheckedResult?> = MutableStateFlow(null)
    val passwordCheckResultFlow: Flow<ValueCheckedResult?> = _passwordCheckResultFlow

    private val _passwordVisibilityFlow = MutableStateFlow(false)
    val passwordVisibilityFlow: Flow<Boolean> = _passwordVisibilityFlow
    private val _signUpStatusFlow = MutableSharedFlow<LoginState<*>>()
    val signUpStatusFlow: SharedFlow<LoginState<*>> = _signUpStatusFlow

    fun postAccountValue(value: String?) {
        _accountFlow.value = value
        _accountCheckResultFlow.value = null
    }

    fun postPasswordValue(password: String?) {
        _passwordFlow.value = password
        _passwordCheckResultFlow.value = null
    }

    fun togglePasswordVisibility() {
        _passwordVisibilityFlow.value = !_passwordVisibilityFlow.value
    }

    fun signUp(context: Context, loginType: LoginType) = viewModelScope.launch {
        if (!checkValidity(context)) return@launch
        _signUpStatusFlow.emit(LoginState.Loading)
        _signUpStatusFlow.emitAll(
            accountInteractor.accountSignup(
                _accountFlow.value!!,
                _passwordFlow.value!!,
                loginType
            )
        )
    }

    private fun checkValidity(context: Context): Boolean {
        var result = true
        val account = _accountFlow.value
        val password = _passwordFlow.value
        if (account.isNullOrBlank()) {
            _accountCheckResultFlow.value = ValueCheckedResult(false, context.getString(R.string.cannot_empty).format(context.getString(R.string.text_account)))
            result = false
        } else if (!AccountUtil.validateAccount(account)) {
            _accountCheckResultFlow.value = ValueCheckedResult(false, context.getString(R.string.signup_account_input_error))
            result = false
        }
        if (password.isNullOrBlank()) {
            _passwordCheckResultFlow.value = ValueCheckedResult(false, context.getString(R.string.cannot_empty).format(context.getString(R.string.text_password)))
            result = false
        } else if (!AccountUtil.validatePassword(password)) {
            _passwordCheckResultFlow.value = ValueCheckedResult(false, context.getString(R.string.signup_password_input_error))
            result = false
        }

        return result
    }

}