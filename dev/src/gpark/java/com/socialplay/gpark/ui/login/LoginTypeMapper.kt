package com.socialplay.gpark.ui.login

import androidx.compose.ui.text.toLowerCase
import com.socialplay.gpark.R
import com.socialplay.gpark.util.getStringByGlobal
import java.util.Locale

/**
 * <pre>
 * author : qijijie
 * e-mail : <EMAIL>
 * time   : 2025/08/21
 * desc   : 通过后端下发的loginType字段来映射本地的登录类型字符串，表格文档：https://meta.feishu.cn/wiki/BvFGw9orriABClkfd8uczMumnCc
 * </pre>
 */
object LoginTypeMapper {
    private const val GOOGLE = "google"
    private const val FACEBOOK = "facebook"
    private const val APPLE = "apple"
    private const val ACCOUNT = "account"
    private const val EMAIL = "email"
    private const val P12 = "p12"
    private const val CREATOR_QR_LOGIN = "creator_qr_login"
    private const val GPARK_ID = "gpark_id"
    private const val VISITOR = "visitor"

    fun getLoginType(loginType: String?): String {
        return when (loginType) {
            GOOGLE -> getStringByGlobal(R.string.account_last_login_google)
            FACEBOOK -> getStringByGlobal(R.string.account_last_login_facebook)
            APPLE -> getStringByGlobal(R.string.account_last_login_apple)
            ACCOUNT -> getStringByGlobal(R.string.account_last_login_account)
            EMAIL -> getStringByGlobal(R.string.account_last_login_email)
            P12 -> getStringByGlobal(R.string.account_last_login_p12)
            CREATOR_QR_LOGIN -> getStringByGlobal(R.string.account_last_login_qr)
            GPARK_ID -> getStringByGlobal(R.string.account_last_login_gpark)
            VISITOR -> getStringByGlobal(R.string.account_last_login_visitor)
            else -> getStringByGlobal(R.string.account_last_login_account)
        }
    }
}