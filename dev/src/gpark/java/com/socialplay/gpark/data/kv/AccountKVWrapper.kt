package com.socialplay.gpark.data.kv

import com.socialplay.gpark.util.property.MMKVScope
import com.tencent.mmkv.MMKV
import timber.log.Timber

class AccountKVWrapper(override val mmkv: MMKV, val metaAppMmkv: MMKV) : MMKVScope {
    companion object {
        const val FIREBASE_TOKEN = "firebase_token"
        const val FIREBASE_ID_TOKEN = "firebase_id_token"
        const val IS_NEW_USER_WS = "is_new_user_ws"
    }

    var firebaseIdToken: String?
        set(value) {
            mmkv.putString(FIREBASE_ID_TOKEN, value)
        }
        get() = mmkv.getString(FIREBASE_ID_TOKEN, null)

    var is_new_user_ws: String?
        set(value) {
            mmkv.putString(IS_NEW_USER_WS, value)
        }
        get() = mmkv.getString(IS_NEW_USER_WS, null)

    var customToken: String?
        set(value) {
            Timber.d("tokenInterceptor set token : $value")
            mmkv.putString(FIREBASE_TOKEN, value)
        }
        get() {
            val userToken = mmkv.getString(FIREBASE_TOKEN, null)
            if (!userToken.isNullOrBlank()) {
                return userToken
            }
            return metaAppMmkv.getString(FIREBASE_TOKEN, null)
        }

    fun clear() {
        customToken = ""
        firebaseIdToken = ""
    }
}