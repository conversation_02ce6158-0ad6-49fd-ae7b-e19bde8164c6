package com.socialplay.gpark.data.repository

import androidx.paging.ExperimentalPagingApi
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.api.MetaApiWrapper
import com.socialplay.gpark.data.api.MetaDTokenApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.interactor.DeviceInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.local.AppDatabase
import com.socialplay.gpark.data.local.SimpleDiskLruCache
import com.socialplay.gpark.data.mapper.MetaMapper
import com.socialplay.gpark.data.model.FireBaseIdToken
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.OAuthResponse
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

@OptIn(ExperimentalPagingApi::class)
class MetaRepositoryWrapper(
    private val metaApi: MetaApiWrapper,
    private val dtokenMetaApi: MetaDTokenApi,
    private val metaKV: MetaKV,
    private val db: AppDatabase,
    private val cache: SimpleDiskLruCache,
    private val deviceInteractor: DeviceInteractor,
    private val metaMapper: MetaMapper
) : MetaRepository(metaApi, dtokenMetaApi, metaKV, db, cache, deviceInteractor, metaMapper), IMetaRepositoryWrapper {
    private var authRepository = AuthRepository(metaApi)
    private var userRepository = UserRepositoryWrapper(metaApi, dtokenMetaApi, metaKV, authRepository, db.shareRecordDao)
    override suspend fun loginByGoogle(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> = userRepository.loginByGoogle(response, onlyLogin, loginType)

    override suspend fun loginByFacebook(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> =
        userRepository.loginByFacebook(response, onlyLogin, loginType)


    override suspend fun bindFacebook(response: OAuthResponse): Flow<DataResult<Boolean>> = userRepository.bindByFacebook(response)

    override suspend fun bindGoogle(response: OAuthResponse): Flow<DataResult<Boolean>> = userRepository.bindByGoogle(response)

    override suspend fun unBindByFacebook(): Flow<DataResult<Boolean>> = userRepository.unBindByFacebook()

    override suspend fun unBindByGoogle(): Flow<DataResult<Boolean>> = userRepository.unBindByGoogle()

    override fun updateCustomToken(customToken: String?) = userRepository.updateCustomToken(customToken)
    override fun fetchCustomToken() = userRepository.fetchCustomToken()

    override fun hasFirebaseTokenExpired(token: FireBaseIdToken?): Boolean = authRepository.hasFirebaseTokenExpired(token)

    // 获取忘记密码的短信验证码
    override suspend fun getForgetPwdPhoneCode(phoneNumber: String): Flow<DataResult<Boolean>> = flow {
        emit(DataResult.Success(true))
    }

    // 获取短信验证码
    override suspend fun getPhoneSmsCode(phoneNumber: String, sendType: String): Flow<DataResult<Boolean>> = flow {
        emit(DataResult.Success(true))
    }

    // 校验验证码
    override suspend fun verifyPhoneCode(phoneNumber: String, phoneCode: String, sendType: String): Flow<DataResult<Boolean>> = flow {
        emit(DataResult.Success(true))
    }

    override fun getUpdateInfo(): Flow<DataResult<UpdateInfo>> = userRepository.getUpdateInfo()

    override suspend fun isNewUser(): Flow<DataResult<Boolean>> = flow {
        val dataResult = DataSource.getDataResultForApi {
            metaApi.isNewUser(
                mapOf<String, String>(
                    "datetime" to "",
                )
            )
        }
        emit(dataResult)
    }

    override suspend fun gparkIdLogin(
        gparkId: String,
        password: String,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> = userRepository.gparkIdLogin(gparkId, password, loginType)
}