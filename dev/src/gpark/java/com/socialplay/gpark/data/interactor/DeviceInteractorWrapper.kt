package com.socialplay.gpark.data.interactor

import android.content.Context
import com.socialplay.gpark.data.kv.MetaKV
import org.koin.core.context.GlobalContext

object DeviceInteractorWrapper {
    fun ids(): List<String> {
        return listOf( googleAdId, firebaseId)
    }

    private val googleInteractor by GlobalContext.get().inject<GoogleInteractor>()


    val firebaseId: String
        get() = googleInteractor.firebaseId.value ?: ""

    val user_pseudo_id: String
        get() = googleInteractor.user_pseudo_id

    val googleAdId: String
        get() = googleInteractor.googleAdId

    /**
     * GPark 没有 oaId
     */
    fun generateOaId(context: Context, metaKV: MetaKV, onComplete: (result: String) -> Unit) { }
}