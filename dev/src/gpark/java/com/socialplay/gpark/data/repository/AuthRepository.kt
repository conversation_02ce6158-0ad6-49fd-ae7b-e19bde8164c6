package com.socialplay.gpark.data.repository

import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.model.FireBaseIdToken
import com.socialplay.gpark.data.model.LoginFromType
import com.socialplay.gpark.data.model.auth.QRCodeAuthScanResult
import com.socialplay.gpark.function.overseabridge.bridge.IFirebaseSdkBridge
import kotlinx.coroutines.withTimeoutOrNull
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * @author: ning.wang
 * @date: 2021-09-16 3:40 下午
 * @desc:
 */
class AuthRepository(private val metaApi: MetaApi) {

    private val firebaseBridge: IFirebaseSdkBridge by lazy { GlobalContext.get().get() }
    suspend fun signInWithCredential(loginFromType: LoginFromType?, token: String){
        firebaseBridge.auth.awaitSignInWithCredential(loginFromType, token)
    }

    suspend fun linkWithCredential(loginFromType: LoginFromType, token: String){
        firebaseBridge.auth.linkWithCredential(loginFromType, token)
    }

    suspend fun unLinkWithCredential(loginFromType: LoginFromType) {
        firebaseBridge.auth.unLinkWithCredential(loginFromType)
    }

    suspend fun getIdToken(customToken: String?, withTimeout: Boolean): FireBaseIdToken? {
        return if (withTimeout) {
            // 设置一个超时时间
            withTimeoutOrNull(20000) {
                firebaseBridge.auth.getIdToken(customToken)
            }
        } else {
            firebaseBridge.auth.getIdToken(customToken)
        }
    }

    fun hasFirebaseTokenExpired(token: FireBaseIdToken?): Boolean {
        return token?.let {
            val diffTime = System.currentTimeMillis() - token.expirationTimestamp
            val expired = diffTime <= 0
            Timber.d(
                "hasTokenExpired: current time=[%s] token timestamp=[%s] diff=[%s]ms expired=%s",
                System.currentTimeMillis(),
                token.expirationTimestamp,
                diffTime,
                expired
            )
            Timber.tag("Test-IdToken").d("hasFirebaseTokenExpired current:${System.currentTimeMillis()} tokenTimestamp:${token.expirationTimestamp} diffTime:$diffTime expired:$expired")
            expired
        } ?: true
    }

    fun logout() {
        firebaseBridge.auth.signOut()
    }

    suspend fun requestScanQRCode(url: String): DataResult<QRCodeAuthScanResult> {
        return DataSource.getDataResultForApi { metaApi.requestScanQRCode(url) }
    }

    suspend fun confirmLogin(url: String): DataResult<Any> {
        return DataSource.getDataResultForApi { metaApi.confirmLogin(url) }
    }
}