package com.socialplay.gpark.data.model

data class GparkProductData(val productCode: String)


data class GoogleProductResult(
    val body: BodyRequestOrder?,
    val code: Int,
    val message: String?
)

/*
originalJson 的数据格式如下:
{
    "packageName": "com.socialplay.gpark",
    "productId": "gp_online_49",
    "purchaseTime": *************,
    "purchaseState": 0,
    "purchaseToken": "adiinjlehgfiijfhcaiapjko.AO-J1OwjjJHTZegdEKBgFtoylyymNtIqPKA1yy1Gnz9PpJ92X9EzK-9ZZr8Ylvu5WHGns5xv6GirzEmJ6FkE_wWp0BGTOqP6YQ",
    "obfuscatedAccountId": "d2532fb89c694093ad2822b93c44c3a6",
    "obfuscatedProfileId": "2025052302311300002218290190078",
    "quantity": 1,
    "acknowledged": false,
    "orderId": "GPA.**************-26695"
}
*/
data class GooglePayResultData(
    val originalJson: String,
    val orderId: String,
    val purchaseToken: String,
    val needUpdate: Boolean,//是否需要通知游戏或者web订单结果
    val productType:String,
    val googleOrderId: String,
    val uuid: String? = null,
    val consumeAgain: Boolean? = false // 是否是重新消耗订单
)
