package com.socialplay.gpark.data.repository

import com.socialplay.gpark.R
import com.socialplay.gpark.data.api.MetaApiWrapper
import com.socialplay.gpark.data.api.MetaDTokenApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.local.ShareRecordDao
import com.socialplay.gpark.data.model.FireBaseIdToken
import com.socialplay.gpark.data.model.LOGIN_TYPE_GPARK_ID
import com.socialplay.gpark.data.model.LoginFromType
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.data.model.mgs.ViolateMessage
import com.socialplay.gpark.data.model.user.AuthInfoApiResult
import com.socialplay.gpark.data.model.user.LOGIN_REQUEST_TYPE_ALL
import com.socialplay.gpark.data.model.user.LOGIN_REQUEST_TYPE_ONLY_LOGIN
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.data.model.user.ThirdBindRequest
import com.socialplay.gpark.data.model.user.ThirdLoginRequest
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.http.CheckTokenInterceptor
import com.socialplay.gpark.function.overseabridge.bridge.IUpdateBride
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.Md5Util
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

class UserRepositoryWrapper(
    private val metaApi: MetaApiWrapper,
    private val detokenMetaApi: MetaDTokenApi,
    private val metaKV: MetaKV,
    private val authRepository: AuthRepository,
    private val shareRecordDao: ShareRecordDao
) : UserRepository(metaApi, detokenMetaApi, metaKV, authRepository, shareRecordDao) {

    fun fetchCustomToken(): String? {
        return GsonUtil.gsonSafeParse<FireBaseIdToken>(metaKV.accountWrapper.firebaseIdToken)?.token
    }

    fun updateCustomToken(customToken: String?) {
        GlobalScope.launch(Dispatchers.IO) {
            Timber.tag(CheckTokenInterceptor.TAG).d("fetchFirebaseIdToken start")
            val idToken = kotlin.runCatching { authRepository.getIdToken(customToken, true) }.getOrNull()
            Timber.tag(CheckTokenInterceptor.TAG).d("fetchFirebaseIdToken customToken:$customToken, idToken:$idToken")
            metaKV.accountWrapper.firebaseIdToken = GsonUtil.gson.toJson(idToken)
        }
    }

    suspend fun loginByGoogle(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> {
        val request = ThirdLoginRequest(
            if (onlyLogin) LOGIN_REQUEST_TYPE_ONLY_LOGIN else LOGIN_REQUEST_TYPE_ALL,
            LoginWay.Google.way,
            response.userid,
            response.token,
            response.birthday,
            response.gender,
            response.name,
            response.picture,
            commonParamsProvider.simCountryCode
        )
        val dataResult = DataSource.getDataResultForApi { metaApi.loginByThird(request) }
        return processAuthLogin(dataResult, loginType, LoginWay.Google, response.token)
    }

    suspend fun loginByFacebook(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> {
        val request = ThirdLoginRequest(
            if (onlyLogin) LOGIN_REQUEST_TYPE_ONLY_LOGIN else LOGIN_REQUEST_TYPE_ALL,
            LoginWay.Facebook.way,
            response.userid,
            response.token,
            response.birthday,
            response.gender,
            response.name,
            response.picture,
            commonParamsProvider.simCountryCode
        )
        val dataResult = DataSource.getDataResultForApi { metaApi.loginByThird(request) }
        return processAuthLogin(dataResult, loginType, LoginWay.Facebook, response.token)
    }

    private fun signInWithCredential(loginFromType: LoginFromType?, token: String) = GlobalScope.launch(Dispatchers.IO) {
        authRepository.signInWithCredential(loginFromType, token)
    }

    suspend fun bindByFacebook(response: OAuthResponse): Flow<DataResult<Boolean>> = flow {
        val request = ThirdBindRequest(
            "facebook",
            response.userid,
            response.token,
            response.birthday,
            response.gender,
            response.name,
            response.picture
        )
        val dataResult = DataSource.getDataResultForApi { metaApi.bindByThird(request) }
        if (dataResult.succeeded) {
            authRepository.linkWithCredential(LoginFromType.FaceBook, response.token)
        }
        emit(dataResult)
    }

    suspend fun bindByGoogle(response: OAuthResponse): Flow<DataResult<Boolean>> = flow {
        val request = ThirdBindRequest(
            "google",
            response.userid,
            response.token,
            response.birthday,
            response.gender,
            response.name,
            response.picture
        )
        val dataResult = DataSource.getDataResultForApi { metaApi.bindByThird(request) }
        if (dataResult.succeeded) {
            kotlin.runCatching {
                authRepository.linkWithCredential(LoginFromType.Google, response.token)
                emit(DataSource.getDataResult { true })
            }.getOrElse {
                emit(DataSource.getDataResult { false })
            }
            return@flow
        }
        emit(dataResult)
    }

    suspend fun unBindByFacebook(): Flow<DataResult<Boolean>> = flow {
        val map = mapOf("bindType" to "facebook")
        val dataResult = DataSource.getDataResultForApi { metaApi.unBindByThird(map) }
        authRepository.unLinkWithCredential(LoginFromType.FaceBook)
        emit(dataResult)
    }

    suspend fun unBindByGoogle(): Flow<DataResult<Boolean>> = flow {
        val map = mapOf("bindType" to "google")
        val dataResult = DataSource.getDataResultForApi { metaApi.unBindByThird(map) }
        authRepository.unLinkWithCredential(LoginFromType.Google)
        emit(dataResult)
    }

    fun getUpdateInfo(): Flow<DataResult<UpdateInfo>> = flow {
        val updateAvailability = GlobalContext.get().get<IUpdateBride>().updateAvailability(GlobalContext.get().get())
        Timber.i("UpdateInfoCheck initUpdate getUpdateInfo updateAvailability：$updateAvailability")
        if (updateAvailability) {
            val dataResult = DataSource.getDataResultForApi { metaApi.getUpdateInfo() }
            emit(dataResult)
        } else {
            emit(DataSource.getDataResult { null })
        }
    }

    override suspend fun gparkIdLogin(
        id: String,
        password: String,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> {
        val request = ThirdLoginRequest(
            requestType = LOGIN_REQUEST_TYPE_ONLY_LOGIN,
            loginKey = id,
            loginToken = Md5Util.str2MD5(password),
            loginType = LOGIN_TYPE_GPARK_ID,
            simCountryCode =   commonParamsProvider.simCountryCode
        )
        val dataResult = DataSource.getDataResultForApi {
            metaApi.loginByThird(request)
        }
        return processAuthLogin(dataResult, loginType, LoginWay.GparkId, "")
    }
}