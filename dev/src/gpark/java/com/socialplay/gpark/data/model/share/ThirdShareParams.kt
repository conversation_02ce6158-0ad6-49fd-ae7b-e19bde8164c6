package com.socialplay.gpark.data.model.share

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/23
 *     desc   :
 * </pre>
 */
@Parcelize
class ThirdShareParams(
    val reqId: String,
    val platform: String,
    val mode: String,
    val path: String?,
    val paths: List<String>?,
    val sticker: ShareData.Sticker?,
    val gameId: String?,
    val title: String?,
    val content: String?,
    val tags: List<String>?,
    val from: Int
) : Parcelable {

    companion object {
        const val FROM_RECORD_END_DIALOG = 1
        const val FROM_MY_RECORD = 2
    }

    val isFromRecording get() = from == FROM_RECORD_END_DIALOG || from == FROM_MY_RECORD
    val isFromRecordingEnd get() = from == FROM_RECORD_END_DIALOG
}