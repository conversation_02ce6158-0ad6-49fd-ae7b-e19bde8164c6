package com.socialplay.gpark.data.interactor

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.function.pandora.PandoraToggle
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * create by: bin on 2021/6/6
 */


class UpdateAppInteractor(private val repository: IMetaRepositoryWrapper) {

    private val deviceInteractor: DeviceInteractor by lazy { GlobalContext.get().get<DeviceInteractor>() }
    private val accountInteractor: AccountInteractor = GlobalContext.get().get<AccountInteractor>()
    private val coroutineScope: CoroutineScope = MainScope()

    // Empty
    private val _updateInfoLiveData: MutableLiveData<UpdateInfo?> by lazy { MutableLiveData<UpdateInfo?>() }
    val updateInfoLiveData: LiveData<UpdateInfo?> = _updateInfoLiveData

    init {
        // 添加设备ID初始化完成的回调
        deviceInteractor.addAndroidIdInitCallback {
            Timber.d("UpdateInfoCheck UpdateAppInteractor DeviceId initialized, refreshing update info")
            // 设备ID初始化完成后请求更新信息
            // 这里还需要等登录信息，不然也获取不到
            accountInteractor.getUserInfoFromCache()?.let {
                Timber.d("UpdateInfoCheck UpdateAppInteractor getUserInfoFromCache success, userInfo：${it?.uuid}")
                checkAndRequest()
            } ?: run {
                Timber.d("UpdateInfoCheck UpdateAppInteractor getUserInfoFromCache failed,  waiting for login complete")
                accountInteractor.accountLiveData.asFlow().onEach {
                    Timber.d("UpdateInfoCheck UpdateAppInteractor accountLiveData observer triggered, userInfo:$it")
                    it?.let { user ->
                        checkAndRequest()
                    }
                }.launchIn(coroutineScope)
            }
        }
    }

    private fun checkAndRequest() {
        val updateOpen = PandoraToggle.isUpdateOpen
        Timber.d("UpdateInfoCheck UpdateAppInteractor checkAndRequest start updateOpen:$updateOpen")
        if (updateOpen) {
            try {
                coroutineScope.launch(Dispatchers.IO) {
                    repository.getUpdateInfo().collect {
                        Timber.d("UpdateInfoCheck UpdateAppInteractor checkAndRequest  updateInfo:${it}")
                        if (it.succeeded) {
                            _updateInfoLiveData.postValue(it.data)
                        } else {
                            _updateInfoLiveData.postValue(null)
                        }
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "UpdateInfoCheck UpdateAppInteractor checkAndRequest  Error during update process")
                _updateInfoLiveData.postValue(null)
            }
        } else {
            _updateInfoLiveData.postValue(null)
        }
    }
}