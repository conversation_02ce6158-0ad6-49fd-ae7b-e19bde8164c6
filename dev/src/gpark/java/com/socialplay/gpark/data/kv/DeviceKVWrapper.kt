package com.socialplay.gpark.data.kv

import com.tencent.mmkv.MMKV

class DeviceKVWrapper(private val mmKV: MMKV) {
    companion object {
        private const val DEVICE_SOLAR_ENGINE_CONVERSION_DATA_KEY = "key_device_solar_engine_conversion_data"
    }

    var solarEngineConversionData: String?
        set(value) {
            mmKV.putString(DEVICE_SOLAR_ENGINE_CONVERSION_DATA_KEY, value)
        }
        get() {
            return mmKV.getString(DEVICE_SOLAR_ENGINE_CONVERSION_DATA_KEY, null)
        }
}