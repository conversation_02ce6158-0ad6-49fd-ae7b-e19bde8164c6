package com.socialplay.gpark.data.api

import com.socialplay.gpark.data.base.ApiResult
import com.socialplay.gpark.data.model.UpdateInfo
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

interface MetaApiWrapper : Meta<PERSON><PERSON> {
    @GET("/version/v3/validate")
    suspend fun getUpdateInfo(): ApiResult<UpdateInfo>

    /**
     * 获取是否是新用户
     */
    @POST("/user/v2/isNew")
    suspend fun isNewUser(@Body body: Map<String, String>): ApiResult<Boolean>
}