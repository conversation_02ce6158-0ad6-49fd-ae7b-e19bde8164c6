package com.socialplay.gpark.data.model.startup

import android.content.Context
import com.socialplay.gpark.R
import com.socialplay.gpark.function.cdnview.CacheCdnImageTask
import org.koin.core.context.GlobalContext

object GuideInfoHelper {
    fun defaultList(): List<GuideInfo> {
        val context = GlobalContext.get().get<Context>()
        return listOf(
            GuideInfo(
                context.getString(R.string.local_guide_default_title_map),
                context.getString(R.string.local_guide_default_desc_map),
                CacheCdnImageTask.CDN_GUIDE_DEFAULT_BANNER_MAP,
                GuideInfo.TYPE_MAPS
            ),
            GuideInfo(
                context.getString(R.string.local_guide_default_title_shot),
                context.getString(R.string.local_guide_default_desc_shot),
                CacheCdnImageTask.CDN_GUIDE_DEFAULT_BANNER_SHOT,
                GuideInfo.TYPE_SHOT
            ),
            GuideInfo(
                context.getString(R.string.local_guide_default_title_car),
                context.getString(R.string.local_guide_default_desc_car),
                CacheCdnImageTask.CDN_GUIDE_DEFAULT_BANNER_CAR,
                GuideInfo.TYPE_CAR
            ),
            GuideInfo(
                context.getString(R.string.local_guide_default_title_module),
                context.getString(R.string.local_guide_default_desc_module),
                CacheCdnImageTask.CDN_GUIDE_DEFAULT_BANNER_MODULE,
                GuideInfo.TYPE_MODULES
            )
        )
    }
}