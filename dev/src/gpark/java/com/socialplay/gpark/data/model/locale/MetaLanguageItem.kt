package com.socialplay.gpark.data.model.locale

import android.content.Context
import android.os.Parcelable
import androidx.annotation.StringRes
import com.socialplay.gpark.R
import com.socialplay.gpark.function.locale.MetaLanguages
import kotlinx.parcelize.Parcelize
import java.util.Locale

interface IComplexIntlFeature {
    /**
     * 发帖删除资源动画，是否正向
     * @see [direction] 方向 com.socialplay.gpark.ui.post.v2.PublishPostFragment.direction
     */
    fun postDeleteForward(): Boolean
}

/**
 * @param isSystem 是否跟随系统
 */
data class LanguageOption(
    val language: MetaLanguageItem,
    val isSystem: Boolean,
)

/**
 * @param allPlatformsLangCode 全平台公用语言code，当跟随系统时，此值不准确
 * @param followSystem 是否跟随系统
 */
@Parcelize
data class LanguageSettingConfig(
    val allPlatformsLangCode: Int,
    val followSystem: Boolean,
) : Parcelable {
    companion object {
        fun getFollowSystemConfig(context: Context): LanguageSettingConfig {
            return LanguageSettingConfig(
                allPlatformsLangCode = MetaLanguages.getFollowSystemLanguage(context).allPlatformsLangCode,
                followSystem = true
            )
        }
    }
}

/**
 * @param allPlatformsLangCode 全平台公用语言code：客户端、web、后端、mw
 * @param locale 语言
 * @param noTransNameStringRes 不翻译的字符串资源
 * @param transNameStringRes 翻译的字符串资源
 * @param matchLanguage 是否匹配语言
 *
 * 机型不同，locale格式也不同：华为（zh_SG_#Hant）表达更多样，小米（zh_TW）基础设置
 */
data class MetaLanguageItem(
    val allPlatformsLangCode: Int,
    val locale: Locale,
    @StringRes val noTransNameStringRes: Int,
    val dynamicImpl: IComplexIntlFeature,
    val matchLanguage: (languageTag: String) -> Boolean
) {
    init {
        MetaLanguages.putMap(this)
    }

    companion object {
        val ENGLISH = MetaLanguageItem(
            1,
            Locale.ENGLISH,
            R.string.no_trans_english,
            object : IComplexIntlFeature {
                override fun postDeleteForward(): Boolean = false
            },
        ) {
            return@MetaLanguageItem it.contains("en")
        }

//        val JAPANESE = MetaLanguageItem(
//            9,
//            Locale.JAPANESE,
//            R.string.no_trans_japanese,
//            object : IComplexIntlFeature {
//                override fun postDeleteForward(): Boolean = false
//            }
//        ) {
//            return@MetaLanguageItem it.contains("ja")
//        }
//
//        val KOREAN = MetaLanguageItem(
//            10,
//            Locale.KOREAN,
//            R.string.no_trans_korean,
//            object : IComplexIntlFeature {
//                override fun postDeleteForward(): Boolean = true
//            },
//        ) {
//            return@MetaLanguageItem it.contains("ko")
//        }

//        val TRADITIONAL_CHINESE = MetaLanguageItem(
//            3,
//            Locale.TRADITIONAL_CHINESE,
//            R.string.no_trans_traditional_chinese,
//            object : IComplexIntlFeature {
//                override fun postDeleteForward(): Boolean = false
//            }
//        ) { languageTag ->
//            val tags = setOf("zh-TW", "zh-HK", "zh-MO", "zh-Hant")
//            return@MetaLanguageItem tags.any { languageTag.contains(it) }
//        }
//
//        val SIMPLIFIED_CHINESE = MetaLanguageItem(
//            2,
//            Locale.SIMPLIFIED_CHINESE,
//            R.string.no_trans_simplified_chinese,
//            object : IComplexIntlFeature {
//                override fun postDeleteForward(): Boolean = false
//            }
//        ) { languageTag ->
//            val tags = setOf("zh-CN", "zh-Hans")
//            return@MetaLanguageItem tags.any { languageTag.contains(it) }
//        }
    }
}