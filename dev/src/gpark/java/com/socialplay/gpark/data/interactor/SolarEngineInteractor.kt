package com.socialplay.gpark.data.interactor

import android.app.Application
import android.os.SystemClock
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.se.SeKeyMapper
import com.socialplay.gpark.data.model.se.SolarEngineConversionData
import com.socialplay.gpark.data.model.se.SolarEngineConversionFailureData
import com.socialplay.gpark.data.model.se.SolarEngineConversionSuccessData
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.overseabridge.bridge.ISolarEngineBridge
import com.socialplay.gpark.function.overseabridge.bridge.SolarEngineConfigBridge
import com.socialplay.gpark.util.GsonUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import timber.log.Timber

class SolarEngineInteractor(
    private val metaKv: MetaKV,
    private val solarEngineBridge: ISolarEngineBridge
) {

    //需要上报的SE字段
    private val collectEventKeys = arrayOf(
        "adgroup_id",
        "adgroup_name",
        "adplan_id",
        "adplan_name",
        "site_id",
        "site_name",
        "adcreative_id",
        "adcreative_name",
        "channel_name",
    )

    private val se2PandoraKeyMap = mapOf(
        "adgroup_id" to "se_adgroup_id",
        "adgroup_name" to "se_adgroup_name",
        "adplan_id" to "se_adplan_id",
        "adplan_name" to "se_adplan_name",
        "site_id" to "se_site_id",
        "site_name" to "se_site_name",
        "adcreative_id" to "se_adcreative_id",
        "adcreative_name" to "se_adcreative_name",
        "channel_name" to "se_status",
    )


    private val keyExchangeMap = listOf(
        // Unity 渠道的对adplan和adgroup进行交换
        SeKeyMapper({
            it["channel_name"] == "Unity Ads"
        }, {
            when (it){
                "adplan_id" -> "adgroup_id"
                "adgroup_id" -> "adplan_id"

                "adplan_name" -> "adgroup_name"
                "adgroup_name" -> "adplan_name"

                else -> it
            }
        })
    )

    private val _conversionData: MutableLiveData<SolarEngineConversionData?> = MutableLiveData()
    val conversionData: LiveData<SolarEngineConversionData?> = _conversionData

    val collectEventsConversionData: LiveData<Map<String, String>> =
        conversionData.map { conversionData ->
            if (conversionData is SolarEngineConversionSuccessData) {
                return@map collectEventKeys.associate {
                    val keyExchanger = keyExchangeMap.firstOrNull { it.isMatch(conversionData.data) }

                    val key = keyExchanger?.keyMapper?.invoke(it) ?: it

                    val mappingKey = se2PandoraKeyMap[it]
                    if(mappingKey != null){
                        mappingKey to (conversionData.data[key]?.toString() ?: "")
                    }else{
                        it to (conversionData.data[key]?.toString() ?: "")
                    }
                }
            }
            return@map emptyMap<String, String>()
        }

    fun preInit(app: Application) {
        solarEngineBridge.preInit(app, app.getString(R.string.solar_engine_app_key))
    }

    fun init(app: Application) {
        val cachedConversionData = readConversionDataFromCache()
        Timber.i("Read solarengine conversion from cache $cachedConversionData")

        //有缓存就直接读取缓存了
        if (cachedConversionData != null) {
            _conversionData.value = cachedConversionData
        }

        val mainScope = MainScope()

        val config = SolarEngineConfigBridge(
            logEnabled = BuildConfig.DEBUG,
            isDebugModel = BuildConfig.DEBUG,

            enable2GReporting = true,
            isGDPRArea = false,
            adPersonalizationEnabled = true,
            adUserDataEnabled = true,
            coppaEnabled = false,
            kidsAppEnabled = false,
            attributionListener = object : ISolarEngineBridge.OnAttributionListener{
                override fun onAttributionSuccess(attribution: JSONObject?) {
                    mainScope.launch {
                        onConversionDataSuccess(attribution)
                    }
                }

                override fun onAttributionFail(errorCode: Int) {
                    mainScope.launch {
                        onConversionDataFail(errorCode)
                    }
                }
            }
        )

        val startTime = SystemClock.elapsedRealtime()

        solarEngineBridge.initialize(
            app,
            app.getString(R.string.solar_engine_app_key),
            config,
            object : ISolarEngineBridge.IInitializationCallbackBridge {
                override fun onInitializationCompleted(code: Int) {
                    Timber.d("SolarEngine initialize complete code:${code} distinctId:${solarEngineBridge.getDistinctId()}")

                    Analytics.track(EventConstants.SE_START_FINISH) {
                        val time = SystemClock.elapsedRealtime() - startTime
                        put("time", time)
                        put("se_time", time)
                        put("code", code)
                    }

                    if (code == 0) {
                        mainScope.launch {
                            val attribution = solarEngineBridge.getAttribution()
                            if(attribution != null){
                                onConversionDataSuccess(attribution)
                            }
                        }
                    }
                }
            })
    }

    fun getDistinctId(): String? {
        return solarEngineBridge.getDistinctId()
    }

    private suspend fun onConversionDataFail(code: Int) {

        Analytics.track(EventConstants.EVENT_SE_CONVERSION_DATA_EXCEPTION) {
            put("code", code)
        }

        _conversionData.value = SolarEngineConversionFailureData("Initialize error code:${code}")
    }

    private suspend fun onConversionDataSuccess(attribution: JSONObject?) {
        Timber.d("SolarEngine retrieved attribution attribution:${attribution}")
        if (attribution != null) {
            val attributionMap = mutableMapOf<String, Any>()
            attribution.keys().forEach {
                val value = attribution.get(it)
                attributionMap[it] = value
            }

            val conversionData = SolarEngineConversionSuccessData(attributionMap)
            _conversionData.value = conversionData


            Analytics.track(EventConstants.EVENT_SE_CONVERSION_DATA_RECEIVED) {
                var index = 0
                for (entry in attributionMap) {
                    put("af_config_${index++}", "${entry.key}:${entry.value}")
                }
            }

            withContext(Dispatchers.IO){
                metaKv.deviceWrapper.solarEngineConversionData = GsonUtil.safeToJson(conversionData)
            }

        } else {
            _conversionData.value = SolarEngineConversionFailureData("Attribution data is null")
        }
    }


    private fun readConversionDataFromCache(): SolarEngineConversionSuccessData? {
        return metaKv.deviceWrapper.solarEngineConversionData?.let {
            GsonUtil.gsonSafeParseCollection(it)
        }
    }
}