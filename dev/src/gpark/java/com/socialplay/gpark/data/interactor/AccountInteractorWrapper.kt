package com.socialplay.gpark.data.interactor

import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.LoginStatusEvent
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.getLoginTypeByValue
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.ui.login.LoginItemData
import com.socialplay.gpark.ui.login.LoginViewModel.Companion.STATUS_CANCEL
import com.socialplay.gpark.ui.login.LoginViewModel.Companion.STATUS_FAIL
import com.socialplay.gpark.ui.login.LoginViewModel.Companion.STATUS_SUCCESS
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber

class AccountInteractor(metaRepository: IMetaRepositoryWrapper, metaKV: MetaKV) : BaseAccountInteractor(metaRepository, metaKV) {

    override fun isBindAccount(): Boolean {
        val userInfo = _accountLiveData.value ?: return false
        return userInfo.bindFacebook || userInfo.bindGoogle || (userInfo.thirdBindInfo?.parentEmail != null && userInfo.bindEmail?.isNullOrEmpty() == false) || (!userInfo.account.isNullOrEmpty())
    }

    fun fetchLoginOthers(): List<LoginItemData> {
        return listOf(
//            LoginItemData(LoginWay.Facebook, R.drawable.icon_login_facebook, R.string.login_with_facebook),
            LoginItemData(LoginWay.Google, R.drawable.icon_login_google, R.string.login_with_google),
        )
    }

    suspend fun facebookLogin(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.loginByFacebook(response, onlyLogin, loginType).collect {
            if (it::class.java == LoginState.Succeeded::class.java) {
                postLoginStatusAndUserInfo((it as LoginState.Succeeded).userInfo, LoginStatusEvent.LOGIN_SUCCESS)
            }
            emit(it)
        }
    }

    suspend fun googleLogin(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.loginByGoogle(response, onlyLogin, loginType).collect {
            if (it.succeeded) {
                postLoginStatusAndUserInfo((it as LoginState.Succeeded).userInfo, LoginStatusEvent.LOGIN_SUCCESS)
            }
            emit(it)
        }
    }

    suspend fun googleBind(response: OAuthResponse): Flow<DataResult<Boolean>> = flow {
        metaRepository.bindGoogle(response).collect {
            if (it.succeeded && it.data == true) {
                getUserInfoFromCache()?.apply {
                    bindGoogle = true
                    getMetaUserInfoFromNet(true)
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
            emit(it)
        }
    }

    suspend fun googleUnBind(): Flow<DataResult<Boolean>> = flow {
        metaRepository.unBindByGoogle().collect {
            if (it.succeeded && it.data == true) {
                getUserInfoFromCache()?.apply {
                    bindGoogle = false
                    googleNickname = ""
                    googlePortrait = ""
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
            emit(it)
        }
    }

    suspend fun facebookBind(response: OAuthResponse): Flow<DataResult<Boolean>> = flow {
        metaRepository.bindFacebook(response).collect {
            if (it.succeeded && it.data == true) {
                getUserInfoFromCache()?.apply {
                    bindFacebook = true
                    getMetaUserInfoFromNet(true)
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
            emit(it)
        }
    }

    suspend fun facebookUnBind(): Flow<DataResult<Boolean>> = flow {
        metaRepository.unBindByFacebook().collect {
            if (it.succeeded && it.data == true) {
                getUserInfoFromCache()?.apply {
                    bindFacebook = false
                    facebookNickname = ""
                    facebookPortrait = ""
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
            emit(it)
        }
    }

    suspend fun loginByAuthResponse(response: OAuthResponse, onlyLogin: Boolean, location: String): Flow<LoginState<MetaUserInfo>> = flow {
        Timber.i("loginByAuthResponse response ${response}, response.source= ${response.source}")
        when (response.oauthThirdWay) {
            LoginWay.Facebook -> {
                facebookLogin(response, onlyLogin, getLoginTypeByValue(response.loginType)).collect {
                    emit(it)
                }
            }

            LoginWay.Google -> {
                Analytics.track(
                    EventConstants.GET_GOOGLE_INFORMATION,
                    "state" to STATUS_SUCCESS,
                    "location" to location
                )
                googleLogin(response, false, getLoginTypeByValue(response.loginType)).collect {
                    if (it.succeeded) {
                        Analytics.track(
                            EventConstants.SIGN_UP_GOOGLE_CLICK,
                            "location" to location
                        )
                    } else if (it is LoginState.Failed) {
                        Analytics.track(
                            EventConstants.SIGN_UP_GOOGLE_REQUEST_API_FAIL,
                            "reason" to it.message,
                            "code" to it.code
                        )
                    }
                    emit(it)
                }
            }

            else -> {}
        }
    }

    fun loginAuthFailed(oauthThirdWay: LoginWay, msg: String?, code: Int, reason: String?, location: String) {
        if (oauthThirdWay == LoginWay.Google) {
            Analytics.track(
                EventConstants.GET_GOOGLE_INFORMATION,
                "state" to STATUS_FAIL,
                "location" to location,
                "reason" to reason.orEmpty()
            )
        }
    }

    fun loginAuthCancel(oauthThirdWay: LoginWay, location: String) {
        if (oauthThirdWay == LoginWay.Google) {
            Analytics.track(
                EventConstants.GET_GOOGLE_INFORMATION,
                "state" to STATUS_CANCEL,
                "location" to location
            )
        }
    }

    fun updateCustomToken(customToken: String?) {
        metaRepository.updateCustomToken(customToken)
    }

}