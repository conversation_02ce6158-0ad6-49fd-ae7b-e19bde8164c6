package com.socialplay.gpark.data.model.se

interface SolarEngineConversionData {
    val isSuccess: Boolean
}

data class SolarEngineConversionSuccessData(
    val data: Map<String, Any>
) : SolarEngineConversionData {
    override val isSuccess: Boolean get() = true
}

data class SolarEngineConversionFailureData(val reason: String) : SolarEngineConversionData {
    override val isSuccess: Boolean get() = false
}
