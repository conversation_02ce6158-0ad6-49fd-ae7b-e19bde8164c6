package com.socialplay.gpark.data.model


/**
 * 登录方式
 * [埋点文档] https://meta.feishu.cn/wiki/L4vFwdeNQiK46DkJ4DRcnE0nnLg
 */
enum class LoginWay(val way: String) {
    Account("account"),
    Facebook("facebook"),
    Google("google"),
    Tourist("tourist"),
    QrCode("qrcode"),
    GparkId("gparkid");

    fun toLoginFromType(): LoginFromType? {
        return when (this) {
            LoginWay.Google -> LoginFromType.Google
            LoginWay.Facebook -> LoginFromType.FaceBook
            else -> null
        }
    }


    companion object {
        fun getDefaultSignWay(): LoginWay {
            return Google
        }

        fun getLoginType(typeStr: String?): LoginWay? {
            return when (typeStr) {
                Account.way -> Account
                Facebook.way -> Facebook
                Google.way -> Google
                Tourist.way -> Tourist
                QrCode.way -> QrCode
                GparkId.way,
                LOGIN_TYPE_GPARK_ID -> GparkId
                else -> null
            }
        }
    }
}

enum class LoginFromType {
    Google, FaceBook
}

const val LOGIN_TYPE_VISITOR = "visitor"
const val LOGIN_TYPE_GPARK_ID = "gpark_id"

object LoginTypes {
    fun checkValid(loginType: String?, loginKey: String? = null): Boolean {
        return when (loginType) {
            LoginWay.Account.way, LOGIN_TYPE_GPARK_ID -> {
                !loginKey.isNullOrEmpty()
            }

            LoginWay.Facebook.way, LoginWay.Google.way, LOGIN_TYPE_VISITOR -> {
                true
            }

            else -> {
                false
            }
        }
    }

    fun checkIsAccountLogin(loginType: String?, loginKey: String? = null): Boolean {
        return (loginType == LoginWay.Account.way || loginType == LOGIN_TYPE_GPARK_ID) && !loginKey.isNullOrEmpty()
    }

}