package com.socialplay.gpark.data

import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.model.FireBaseIdToken
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.OAuthResponse
import kotlinx.coroutines.flow.Flow

interface IMetaRepositoryWrapper : IMetaRepository {

    suspend fun loginByGoogle(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>>
    suspend fun loginByFacebook(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>>
    suspend fun bindGoogle(response: OAuthResponse): Flow<DataResult<Boolean>>
    suspend fun bindFacebook(response: OAuthResponse): Flow<DataResult<Boolean>>
    suspend fun unBindByGoogle(): Flow<DataResult<Boolean>>
    suspend fun unBindByFacebook(): Flow<DataResult<Boolean>>

    fun updateCustomToken(customToken: String?)
    fun fetchCustomToken(): String?
    fun hasFirebaseTokenExpired(token: FireBaseIdToken?): Boolean
    // 获取忘记密码的短信验证码
    suspend fun getForgetPwdPhoneCode(phoneNumber: String): Flow<DataResult<Boolean>>
    // 根据类型获取短信验证码
    suspend fun getPhoneSmsCode(phoneNumber: String, sendType: String): Flow<DataResult<Boolean>>
    // 校验短信验证码
    suspend fun verifyPhoneCode(phoneNumber: String, phoneCode: String, sendType: String): Flow<DataResult<Boolean>>

    fun getUpdateInfo(): Flow<DataResult<UpdateInfo>>
    suspend fun isNewUser(): Flow<DataResult<Boolean>>
}