package com.socialplay.gpark.data.model.find_id

import com.socialplay.gpark.R

/**
 * <pre>
 * author : qijijie
 * e-mail : <EMAIL>
 * time   : 2025/08/13
 * desc   :
 * </pre>
 */
data class SupportMethod(val platform: Int?, val title: String?, val content: String?) {
    companion object {
        const val GPARK_EMAIL = 0
        const val PARTY_EMAIL = 1

        const val PARTY_WECHAT = 2
        const val PARTY_QQ = 3
        const val PARTY_RED_NOTE = 4

        const val GPARK_DISCORD = 5
        const val GPARK_YOUTUBE = 6
        const val GPARK_TIKTOK = 7

        fun getPlatformResId(platform: Int): Int? {
            return when(platform) {
                GPARK_EMAIL -> R.drawable.ic_share_friend
                PARTY_EMAIL -> R.drawable.ic_share_friend

                GPARK_DISCORD -> R.drawable.ic_share_discord
                GPARK_YOUTUBE -> R.drawable.ic_share_youtube
                GPARK_TIKTOK -> R.drawable.ic_share_tiktok

                else -> null
            }
        }
    }
}