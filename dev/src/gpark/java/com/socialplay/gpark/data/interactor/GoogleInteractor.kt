package com.socialplay.gpark.data.interactor

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.function.overseabridge.bridge.IFirebaseSdkBridge
import com.socialplay.gpark.function.overseabridge.bridge.IGoogleSdkBridge
import com.socialplay.gpark.util.PlayRefererUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * xingxiu.hou
 * 2021/9/16
 */
class GoogleInteractor(private val context: Context, private val metaKV: MetaKV) {

    private val firebaseSdkBridge by lazy { GlobalContext.get().get<IFirebaseSdkBridge>() }
    private var _googleAdId: String? = null

    private var _playInstallReferer: MutableLiveData<Map<String,String>> = MutableLiveData(emptyMap())

    val googleAdId: String
        get() = _googleAdId ?: ""

    val firebaseId: LiveData<String?>
        get() = firebaseSdkBridge.firebaseIdLiveData

    val user_pseudo_id: String
        get() = firebaseSdkBridge.appInstanceIdLiveData.value ?: ""

    val playInstallReferer: LiveData<Map<String,String>> = _playInstallReferer

    suspend fun initialize(scope: CoroutineScope) {
        getFirebaseId()
        _playInstallReferer.postValue(PlayRefererUtil.splitReferer(getInstallReferer()))

        withContext(scope.coroutineContext) {
            Timber.d("GOOGLE ID ::   Thread ${Thread.currentThread().name}")
            getGoogleAdId()
            getPseudoId()
        }
    }

    private fun getFirebaseId() {
        Timber.d("GOOGLE ID ::   _firebaseId : generateFirebaseId")
        firebaseSdkBridge.getFirebaseId(context)
    }

    private suspend fun getGoogleAdId() {
        Timber.d("GOOGLE ID ::   _googleAdId : generateGoogleAdId")
        withContext(Dispatchers.IO) {
            _googleAdId = GlobalContext.get().get<IGoogleSdkBridge>().getGoogleId(context)
            _googleAdId?.let { adId ->
                //自 2021 年底开始，如果用户选择停用针对用户兴趣投放的广告或广告个性化功能，系统便不会提供广告标识符。您收到的会是一串零，而不是该标识符
                val canUse = adId.trim().any { it != '0' && it != '-' }
                if (!canUse) _googleAdId = null
            }
            Timber.d("GOOGLE ID ::   _googleAdId : $_googleAdId")
        }
    }

    private suspend fun getPseudoId() {
        firebaseSdkBridge.getAppInstanceId(context)
    }

    /**
     * 获取安装引荐来源
     */
    private suspend fun getInstallReferer(): String? {
        var installReferer = metaKV.device.playInstallReferer
        if (installReferer.isNullOrEmpty()) {
            installReferer = PlayRefererUtil.getReferer(context)?.installReferrer
            //获取成功才保存
            installReferer?.let { metaKV.device.playInstallReferer = it }
        }
        Timber.d("zhuwei Install referer fetched %s", installReferer)
        return installReferer
    }
}