package com.socialplay.gpark.app.initialize

import com.socialplay.gpark.data.api.MetaApi
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.function.overseabridge.bridge.IFcmSdkBridge
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext

object PushInit {

    val scope = CoroutineScope(Dispatchers.IO)

    suspend fun init() {
        runCatching {
            val token = withContext(Dispatchers.IO) {
                GlobalContext.get().get<IFcmSdkBridge>().getRegistrationToken()
            }
            GlobalContext.get().get<AccountInteractor>().accountLiveData.observeForever {
                it ?: return@observeForever
                scope.launch {
                    kotlin.runCatching {
                        GlobalContext.get().get<MetaApi>().uploadPushToken(hashMapOf("pushKey" to token, "pushType" to "firebase"))
                    }.getOrElse {
                        it.printStackTrace()
                    }
                }
            }
        }.getOrElse {
            it.printStackTrace()
        }
    }

}