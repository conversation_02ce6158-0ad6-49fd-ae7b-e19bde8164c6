package com.socialplay.gpark

import android.content.Context
import com.socialplay.gpark.data.interactor.GoogleInteractor
import com.socialplay.gpark.data.interactor.SolarEngineInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.di.overseaBridge
import com.socialplay.gpark.function.analytics.NewUserCheckAnalytics
import com.socialplay.gpark.function.notification.NotificationDialog
import com.socialplay.gpark.function.overseabridge.bridge.IAdSdkBridge
import com.socialplay.gpark.function.overseabridge.bridge.IFaceBookSdkBridge
import com.socialplay.gpark.function.overseabridge.bridge.ISnapchatSdkBridge
import com.socialplay.gpark.function.overseabridge.bridge.ITiktokSdkBridge
import com.socialplay.gpark.function.startup.StartupProcessType.AWO
import com.socialplay.gpark.function.startup.StartupProcessType.H
import com.socialplay.gpark.function.startup.StartupProcessType.M
import com.socialplay.gpark.function.startup.core.plus
import com.socialplay.gpark.function.startup.core.project.Project
import com.socialplay.gpark.function.startup.dsl.task
import com.socialplay.gpark.function.startup.dsl.taskAsync
import com.socialplay.gpark.ui.account.ConnectedAccountsViewModel
import com.socialplay.gpark.ui.account.SignUpViewModel
import com.socialplay.gpark.ui.dialog.DialogManagerInfo
import com.socialplay.gpark.ui.dialog.DialogScene
import com.socialplay.gpark.ui.dialog.DialogShowCount
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.main.UpdateDialog
import kotlinx.coroutines.Dispatchers
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.core.context.GlobalContext
import org.koin.core.module.Module
import org.koin.dsl.module

/**
 * 用于区分国内海外不同的初始化需求
 */
object AppStartupInit {
    fun koinModules(): List<Module> {
        // TODO 等功能迁移完之后才能动工这里
        return listOf(
            module {
                fun provideGoogleInteractor(context: Context, metaKV: MetaKV) = GoogleInteractor(context, metaKV)

                single { provideGoogleInteractor(androidContext(), get()) }
                single { SolarEngineInteractor(get(), get()) }
                single { NewUserCheckAnalytics(get(), get()) }
            },
            overseaBridge,
            module {
                viewModel { ConnectedAccountsViewModel(get()) }
                viewModel { SignUpViewModel(get(), get()) }
            }
        )
    }

    fun onCreateAnalyticsBefore(project: Project) {
        project.solarEnginePreInit()
        project.solarEngineInit()
    }

    fun onCreateAnalyticsAfter(project: Project) {
        // 此处Firebase没初始化好的话，使用koin注入的MetaRepository会崩，使用的时候记得检查
        project.google()
    }

    fun onCreate(project: Project) {
        // 等功能迁移完之后才能动工这里
        project.facebookSdkInit()
        project.tiktokSdkInit()
        project.snapchatSdkInit()
        project.metaAdInit()
        project.newUserInit()
    }

    fun onAttachContext(project: Project) {
        // TODO 等功能迁移完之后才能动工这里
    }

    fun Project.newUserInit() = taskAsync("newUserInit", H, Dispatchers.IO) {
        GlobalContext.get().get<NewUserCheckAnalytics>().initialize(this)
    }

    fun Project.solarEnginePreInit() = task("solarEnginePreInit", H) {
        GlobalContext.get().get<SolarEngineInteractor>().preInit(application)
    }

    fun Project.solarEngineInit() = task("solarEngineInit", H) {
        GlobalContext.get().get<SolarEngineInteractor>().init(application)
    }

    fun Project.google() = taskAsync("google", AWO, Dispatchers.IO) {
        GlobalContext.get().get<GoogleInteractor>().initialize(this)
    }

    fun Project.facebookSdkInit() = task("facebookSdkInit", H) {
        GlobalContext.get().get<IFaceBookSdkBridge>().init(application)
    }

    fun Project.tiktokSdkInit() = task("tiktokSdkInit", H + M) {
        GlobalContext.get().get<ITiktokSdkBridge>().init()
    }

    fun Project.snapchatSdkInit() = task("snapchatSdkInit", H + M) {
        GlobalContext.get().get<ISnapchatSdkBridge>().init()
    }

    fun Project.metaAdInit() = task("metaAdInit", H) {
        GlobalContext.get().get<IAdSdkBridge>().preInit(application, BuildConfig.LOG_DEBUG)
    }

    suspend fun dialogManager() {
        // 更新弹框
        DialogShowManager.registerDialog(UpdateDialog(), DialogManagerInfo(1, listOf(DialogScene.MAIN_PAGE, DialogScene.GAME_DETAIL_PAGE), showCount = DialogShowCount.MULTI))
        // 通知权限弹框
        DialogShowManager.registerDialog(NotificationDialog(), DialogManagerInfo(950, listOf(DialogScene.GUIDE_LOGIN_PAGE)))
    }
}