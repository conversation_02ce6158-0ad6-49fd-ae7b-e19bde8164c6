package com.socialplay.gpark.data.model.realname

import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Process
import com.bin.cpbus.CpEventBus
import com.meta.lib.mwbiz.MWBiz
import com.meta.lib.mwbiz.MWBizBridge
import com.socialplay.gpark.data.model.event.GameStateNoteEvent
import com.socialplay.gpark.function.analytics.observer.GameCrashHostObserve
import com.socialplay.gpark.function.deeplink.MetaDeepLink.KEY_COMMON_BACK
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.ui.realname.AntiAdditionLifecycle

object MWLifecycleWrapper {
    fun list(app: Application): List<AntiAdditionLifecycle> {
        return listOf(
            AntiAdditionLifecycle(app) {
                backToMainAndKillGame(app)
            }
        )
    }

    fun backToMainAndKillGame(context: Context, intentBlock: (Intent.() -> Unit)? = null) {
        val gamePkg = MWBizBridge.currentGamePkg()
        val gameId = MWBizBridge.currentGameId()
        CpEventBus.post(
            GameStateNoteEvent(
                "ActivityPaused",
                "",
                Process.myPid(),
                gamePkg,
                gameId,
                true
            )
        )
        GameCrashHostObserve.recordGameOnPause(gamePkg, gameId, true)
        // 要回到原来的界面，这里就不多此一举去跳转了，主要是担心原来的进程没了
//        val intent =
//            Intent(context, MainActivity::class.java).apply {
//                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//                intentBlock?.invoke(this)
//            }
//        intent.putExtra(KEY_COMMON_BACK, true)
//        context.startActivity(intent)
        MWBiz.exit()
    }
}