package com.socialplay.gpark.data

import com.meta.box.data.model.realname.RealNameCheckResult
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.model.AdAnalyticQueryBody
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.Recharge
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.data.model.pay.PayChannelList
import com.socialplay.gpark.data.model.pay.PayResultEntity
import com.socialplay.gpark.data.model.pay.TakeOrderInfo
import com.socialplay.gpark.data.model.pay.TakeOrderParams
import com.socialplay.gpark.data.model.pay.UserBalance
import com.socialplay.gpark.data.model.pay.mobile.MobilePointsParam
import com.socialplay.gpark.data.model.realname.RealNameAutoInfo
import com.socialplay.gpark.data.model.realname.RealNameConfig
import com.socialplay.gpark.data.model.realname.RealNameSurplusGameTime
import com.socialplay.gpark.data.model.user.AuthInfoApiResult
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.OAuthResponse
import kotlinx.coroutines.flow.Flow
import java.util.ArrayList

interface IMetaRepositoryWrapper : IMetaRepository {

    // 游客登录
    suspend fun postGuestLogin(): Flow<DataResult<AuthInfoApiResult>>

    // qq登录
    suspend fun loginByQQ(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>>

    // 微信登录
    suspend fun loginByWX(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>>

    // 抖音登录
    suspend fun loginByDouYin(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>>

    // 快手登录
    suspend fun loginByKwai(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>>

    // 233乐园登录
    suspend fun loginByLeYuan(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>>

    // 手机号登录
    suspend fun loginByPhone(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>>

    // 获取验证码
    suspend fun getLoginPhoneCode(phoneNumber: String): Flow<DataResult<Boolean>>

    // 获取忘记密码的短信验证码
    suspend fun getForgetPwdPhoneCode(phoneNumber: String): Flow<DataResult<Boolean>>
    // 根据类型获取短信验证码
    suspend fun getPhoneSmsCode(phoneNumber: String, sendType: String): Flow<DataResult<Boolean>>
    // 校验短信验证码
    suspend fun verifyPhoneCode(phoneNumber: String, phoneCode: String, sendType: String): Flow<DataResult<Boolean>>

    // 本机一键登录
    suspend fun loginByOneKey(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>>

    // 账号密码登录
    override suspend fun accountLogin(account: String, password: String, loginType: LoginType): Flow<LoginState<MetaUserInfo>>

    fun updateCustomToken(customToken: String?)
    fun fetchCustomToken(): String?
    fun getRecharges(): Flow<DataResult<ArrayList<Recharge>>>

    // mw支付 Ark币兑换商品
    suspend fun partyMWPay(hashMap: Map<String, Any?>):Flow<DataResult<*>>

    /**
     * 支付相关
     */
    suspend fun getPayChannels(selfPkgName: String, scenceCode: String, gamePkgName: String, gameId: String): Flow<DataResult<PayChannelList>>

    //免广告充值下单
    fun privilegePlaceOrder(payChannel: Int, takeOrderInfo: TakeOrderInfo): Flow<DataResult<PayResultEntity>>

    fun cancelOrder(orderId: String): Flow<DataResult<Boolean>>

    suspend fun rechargingLoopParty(orderId: String): DataResult<Boolean>

    suspend fun takeOrderV3(takeOrderParams: TakeOrderParams, payChannel: Int, mobilePointsParam: MobilePointsParam?): Flow<DataResult<PayResultEntity?>>

    /* ====================================== 实名认证 start ====================================== */
    suspend fun realNameCheck(name: String, cardNo: String): DataResult<RealNameCheckResult>

    suspend fun realNameSave(name: String, cardNo: String, pkgName: String, gameId: String): DataResult<Any>

    suspend fun realNameClear(name: String, cardNo: String): Flow<DataResult<Any>>

    suspend fun realNameDetail(): DataResult<RealNameAutoInfo>

    suspend fun getRealNameSurplusGameTimeV3(gameId: Long, gamePkg: String): DataResult<RealNameSurplusGameTime>

    suspend fun realNameConfig(gameId: String? = null): Flow<DataResult<RealNameConfig>>
    /* ====================================== 实名认证 end ====================================== */

    // 应用升级
    fun getUpdateInfo(): Flow<DataResult<UpdateInfo>>

    // 广告埋点上报
    fun reportAdAnalytic(body: AdAnalyticQueryBody): Flow<DataResult<Boolean>>
}