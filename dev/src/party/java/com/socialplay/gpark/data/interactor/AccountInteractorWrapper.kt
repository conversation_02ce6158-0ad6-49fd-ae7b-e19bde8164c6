package com.socialplay.gpark.data.interactor

import android.content.Context
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.ApiResultCodeException
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.LoginStatusEvent
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.getLoginTypeByValue
import com.socialplay.gpark.data.model.user.AuthInfoApiResult
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.ui.login.LoginItemData
import com.socialplay.gpark.util.ifNullOrEmpty
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import org.koin.core.context.GlobalContext
import timber.log.Timber

class AccountInteractor(metaRepository: IMetaRepositoryWrapper, metaKV: MetaKV) : BaseAccountInteractor(metaRepository, metaKV) {

    override fun isBindAccount(): Boolean {
        val userInfo = _accountLiveData.value ?: return false
        return (userInfo.thirdBindInfo?.parentEmail != null && userInfo.bindEmail?.isNullOrEmpty() == false) || (!userInfo.account.isNullOrEmpty())
    }

    fun fetchLoginOthers(): List<LoginItemData> {
        return listOf(
            LoginItemData(LoginWay.Wechat, R.drawable.icon_wechat, R.string.login_with_facebook),
            LoginItemData(LoginWay.QQ, R.drawable.icon_qq, R.string.qq_name),
            LoginItemData(LoginWay.Leyuan, R.drawable.icon_233, R.string.leyuan_name),
        )
    }


    /**
     * 游客登录：无循环重试逻辑，有回调
     */
    @Throws
    fun loginByGuest(): Flow<AuthInfoApiResult> = flow {
        metaRepository.postGuestLogin().collect {
            if (it.succeeded && it.data?.userInfo != null) {
                postLoginStatusAndUserInfo(
                    it.data?.userInfo,
                    LoginStatusEvent.LOGIN_SUCCESS
                )
                emit(it.data!!)
            } else {
                throw ApiResultCodeException(
                    it.code ?: 0,
                    it.message.ifNullOrEmpty { GlobalContext.get().get<Context>().getString(R.string.login_fail) },
                    AuthInfoApiResult::class
                )
            }
        }
    }

    /**
     * qq登录
     */
    suspend fun loginByQQ(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.loginByQQ(response, onlyLogin, loginType).collect {
            if (it.succeeded) {
                postLoginStatusAndUserInfo((it as LoginState.Succeeded).userInfo, LoginStatusEvent.LOGIN_SUCCESS)
            }
            emit(it)
        }
    }


    /**
     * 微信登录
     */
    suspend fun loginByWX(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.loginByWX(response, onlyLogin, loginType).collect {
            if (it.succeeded) {
                postLoginStatusAndUserInfo((it as LoginState.Succeeded).userInfo, LoginStatusEvent.LOGIN_SUCCESS)
            }
            emit(it)
        }
    }


    /**
     * 抖音登录
     */
    suspend fun loginByDouYin(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.loginByDouYin(response, onlyLogin, loginType).collect {
            if (it.succeeded) {
                postLoginStatusAndUserInfo((it as LoginState.Succeeded).userInfo, LoginStatusEvent.LOGIN_SUCCESS)
            }
            emit(it)
        }
    }


    /**
     * 抖音登录
     */
    suspend fun loginByKwai(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.loginByKwai(response, onlyLogin, loginType).collect {
            if (it.succeeded) {
                postLoginStatusAndUserInfo((it as LoginState.Succeeded).userInfo, LoginStatusEvent.LOGIN_SUCCESS)
            }
            emit(it)
        }
    }

    /**
     * 233乐园登录
     */
    suspend fun loginByLeYuan(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.loginByLeYuan(response, onlyLogin, loginType).collect {
            if (it.succeeded) {
                postLoginStatusAndUserInfo((it as LoginState.Succeeded).userInfo, LoginStatusEvent.LOGIN_SUCCESS)
            }
            emit(it)
        }
    }

    /**
     * 手机号登录
     */
    suspend fun loginByPhone(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> = flow {
        metaRepository.loginByPhone(response, onlyLogin, loginType).collect {
            if (it.succeeded) {
                postLoginStatusAndUserInfo((it as LoginState.Succeeded).userInfo, LoginStatusEvent.LOGIN_SUCCESS)
            }
            emit(it)
        }
    }

    suspend fun loginByAuthResponse(response: OAuthResponse, onlyLogin: Boolean, location: String = ""): Flow<LoginState<MetaUserInfo>> = flow {
        Timber.i("loginByAuthResponse response ${response}, response.source= ${response.source}")
        val loginTypeByValue = getLoginTypeByValue(response.loginType)
        when (response.oauthThirdWay) {
            LoginWay.Account -> {
                // 账号登录，现在走的公共库的单独接口
            }

            LoginWay.Phone -> {
                loginByPhone(response, onlyLogin, loginTypeByValue).collect {
                    emit(it)
                }
            }

            LoginWay.Wechat -> {
                loginByWX(response, onlyLogin, loginTypeByValue).collect {
                    emit(it)
                }
            }

            LoginWay.QQ -> {
                loginByQQ(response, onlyLogin, loginTypeByValue).collect {
                    emit(it)
                }
            }

            LoginWay.Leyuan -> {
                loginByLeYuan(response, onlyLogin, loginTypeByValue).collect {
                    emit(it)
                }
            }

            LoginWay.Tourist -> {
                // 游客登录，现在没有这个逻辑
            }

            LoginWay.QrCode -> {
                // 扫码登录，现在单独走的processAuthLogin逻辑
            }

            else -> {
                // 其他登录方式
                emit(LoginState.Failed(GlobalContext.get().get<Context>().getString(R.string.unsupport_login_way), 0, null))
            }
        }
    }

    fun loginAuthFailed(oauthThirdWay: LoginWay, msg: String?, code: Int, reason: String?, location: String) {
//        if (oauthThirdWay == LoginWay.Google) {
//            Analytics.track(
//                EventConstants.GET_GOOGLE_INFORMATION,
//                "state" to STATUS_FAIL,
//                "location" to location,
//                "reason" to reason.orEmpty()
//            )
//        }
    }

    fun loginAuthCancel(oauthThirdWay: LoginWay, location: String) {
//        if (oauthThirdWay == LoginWay.Google) {
//            Analytics.track(
//                EventConstants.GET_GOOGLE_INFORMATION,
//                "state" to STATUS_CANCEL,
//                "location" to location
//            )
//        }
    }

    fun updateCustomToken(customToken: String?) {
        metaRepository.updateCustomToken(customToken)
    }


    /**
     * 更新用户信息和实名信息
     *
     * @param age
     */
    fun updateRealNameUserInfo(age: Int, realNameSource: Int) {
        Timber.d("updateRealNameUserInfo.age=$age")
        if (age > 0) {
            getUserInfoFromMmKv()?.apply {
                var changed = false
                if (!this.bindIdCard) {
                    changed = true
                    this.bindIdCard = true
                }
                if (this.age != age) {
                    changed = true
                    this.age = age
                }
                if (this.realNameSource != realNameSource) {
                    changed = true
                    this.realNameSource = realNameSource
                }
                if (changed) {
                    postLoginStatusAndUserInfo(this, LoginStatusEvent.UPDATE)
                }
            }
        }
        metaKV.realName.mustUpdateRealName()
    }
}