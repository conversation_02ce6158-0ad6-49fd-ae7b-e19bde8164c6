package com.socialplay.gpark.data.model.pay


/**
 * author : yi.zhang
 * e-mail : <EMAIL>
 * time   : 2021/04/14
 * desc   : 下单成功
 */
data class TakeOrderResult(
    //订单编号
    var orderCode: String? = null,

    //订单能支付的超时时长（单位：秒）
    var expirePayDuration: String? = null,

    //优惠券实际优惠的金额（单位：分）
    var couponAmount: Int = 0,

    //订单待支付金额（单位：分【人民币】）订单实际需要支付金额；支付金额=订单总金额-优惠券金额； 如没有优惠，待支付金额 == 订单总金额
    var payAmount: Int = 0,

    //商品编码
    var productCode: String? = null,

    //商品名称
    var productName: String? = null,

    //商品购买数量
    var count: Int = 1,
    val subOrders: List<SubOrder>? = null,
    val sceneCode: String? = null
)

data class SubOrder(val orderCode: String, val orderType: String)

