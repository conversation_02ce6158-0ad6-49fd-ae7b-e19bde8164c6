package com.socialplay.gpark.data.model.pay


/**
 * author : yi.zhang
 * e-mail : <EMAIL>
 * time   : 2021/04/15
 * desc   : 游戏支付结果回调
 */
data class PayResultEntity(
    var orderCode: String? = null,
    var channel: String? = null,
    var appid: String? = null,
    var merchantId: String? = null,
    var qrCodeUrl: String? = null,
    var qqPayInfo: QqPayInfoBean? = null,
    var wxPayInfo: WxPayInfoBean? = null,
    var aliPayInfo: AliPayInfoBean? = null,
    var payType: Int? = null,
    var payAmount: Int? = null,
    var mobilePhone: String? = null,
    var sceneCode: String? = null,
    var subOrders: List<SubOrder>? = null
)

data class QqPayInfoBean(
    var prepayId: String? = null,
    var nonceStr: String? = null,
    var timestamp: String? = null,
    var sign: String? = null,
    var signType: String? = null,
    var pubAcc: String? = null,
    var pubAccHint: String? = null
)

data class WxPayInfoBean(
    var prepayId: String? = null,
    var signType: String? = null,
    var sign: String? = null,
    var timestamp: String? = null,
    var packageStr: String? = null,
    var nonceStr: String? = null,
    var payUrl: String? = null,
    var refer: String? = null
)

data class AliPayInfoBean(
    var aliPayParam: String? = null
)
