package com.socialplay.gpark.data.model

import com.meta.pandora.data.entity.Event
import com.socialplay.gpark.function.analytics.EventDesc

object PartyEventConstants {
    @EventDesc("点击登录按钮")
    val EVENT_LOGIN_CLICK = Event("event_login_click")

    @EventDesc("点击上次登录按钮")
    val EVENT_LAST_LOGIN_CLICK = Event("event_last_login_click")

    @EventDesc("登录结果")
    val EVENT_LOGIN_RESULT = Event("event_login_result")

    @EventDesc("设置密码点击")
    val EVENT_PASSWORD_SETUP = Event("event_password_setup")

    @EventDesc("设置密码结果")
    val EVENT_PASSWORD_SETUP_RESULT = Event("event_password_setup_result")

    @EventDesc("注销账号点击")
    val EVENT_ACCOUNT_LOGOUT = Event("event_account_logout")

    @EventDesc("注销账号结果")
    val EVENT_ACCOUNT_LOGOUT_RESULT = Event("event_account_logout_result")

    @EventDesc("是否同意个人协议")
    val EVENT_CONTRACT_ACCESS = Event("event_contract_access")

    @EventDesc("账号页-点击忘记密码")
    val EVENT_LOGIN_PWD_FORGET_CLICK = Event("event_login_pwd_forget_click")

    @EventDesc("找回密码页-点击客服")
    val EVENT_LOGIN_PWD_SERVICE = Event("event_login_pwd_service")

    @EventDesc("找回密码页-点击未绑定手机号")
    val EVENT_LOGIN_PWD_NUMBER = Event("event_login_pwd_number")

    //引导
    @EventDesc("用户方向选择")
    val EVENT_DIRECTION_CONFIRM = Event("event_direction_confirm")

    @EventDesc("角色创建确认")
    val EVENT_ROLE_CONFIRM = Event("event_role_confirm")

    @EventDesc("是否展示超级推荐位游戏")
    val EVENT_SUPERGAME_SHOW = Event("event_supergame_show")

    @EventDesc("是否打开超级推荐位游戏")
    val EVENT_SUPERGAME_PLAY = Event("event_supergame_play")

    //Fake广告
    @EventDesc("广告-广告展示成功fake")
    val EVENT_AD_SHOW_SUCCESS_FAKE = Event("event_ad_show_success_fake")

    //底部栏+号

    //地图
    @EventDesc("点击底栏+号")
    val EVENT_CREATE_CLICK = Event("event_create_click")

    @EventDesc("点击创作方向")
    val EVENT_CREATE_DIRECTION = Event("event_create_direction")

    @EventDesc("点击地图不同tab")
    val EVENT_WORKSHOP_TAB = Event("event_workshop_tab")

    @EventDesc("飞轮位点击")
    val EVENT_OPERATION_POSITION_CLICK = Event("event_operation_position_click")

    @EventDesc("ugc建造页-点击创建模板")
    val UGC_CREATE_TEMPLATE_CLICK = Event("ugc_create_template_click")

    @EventDesc("ugc建造页-点击直接进入草稿")
    val UGC_CREATE_EDIT_CLICK = Event("ugc_create_edit_click")

    @EventDesc("ugc建造页-点击右上角三个点")
    val UGC_CREATE_SET_CLICK = Event("ugc_create_set_click")

    @EventDesc("ugc建造页-模板右上角点击复制")
    val UGC_CREATE_SET_COPY_CLICK = Event("ugc_create_set_copy_click")

    @EventDesc("ugc建造页-模板右上角点击删除")
    val UGC_CREATE_SET_DELETE_CLICK = Event("ugc_create_set_delete_click")

    @EventDesc("ugc建造页-删除弹窗曝光")
    val UGC_CREATE_DELETE_SHOW = Event("ugc_create_delete_show")

    @EventDesc("ugc建造页-删除弹窗点击确认")
    val UGC_CREATE_DELETE_CONFIRM_CLICK = Event("ugc_create_delete_confirm_click")

    @EventDesc("备份按钮曝光")
    val UGC_BACKUP_EXPOSURE = Event("ugc_backup_exposure")

    @EventDesc("备份按钮点击")
    val UGC_BACKUP_CLICK = Event("ugc_backup_click")

    @EventDesc("备份详情页曝光")
    val BACKUP_PROFILE_EXPOSURE = Event("backup_profile_exposure")

    @EventDesc("备份详情页点击")
    val BACKUP_PROFILE_CLICK = Event("backup_profile_click")

    @EventDesc("二级确认界面")
    val SECONDARY_BACKUP_PROFILE = Event("secondary_backup_profile")

    //素材、拍剧
    @EventDesc("选择创作方向")
    val EVENT_MATERIAL_CLICK = Event("event_material_click")

    @EventDesc("成功跳转角编UGC服装")
    val EVENT_UGC_CLOTHES_JUMP = Event("event_ugc_clothes_jump")

    @EventDesc("view页点击moments")
    val VIEW_CLICK_MOMENTS = Event("view_click_moments")

    @EventDesc("moments模板页展现")
    val EVENT_MOMENTS_TEMPLATE_SHOW = Event("event_moments_template_show")

    @EventDesc("moments模板页点击模板")
    val EVENT_MOMENTS_TEMPLATE_CLICK = Event("event_moments_template_click")

    //个人主页
    @EventDesc("页面展示时长")
    val APP_PAGE_TIME = Event("app_page_time")

    @EventDesc("退出登录")
    val EVENT_ACCOUNT_LOG_OFF = Event("event_account_log_off")

    @EventDesc("客态-关注")
    val EVENT_CUSTOMER_FOLLOW_CLICK = Event("event_customer_follow_click")

    @EventDesc("客态-取消关注点击")
    val EVENT_CUSTOMER_UNFOLLOW_CLICK = Event("event_customer_unfollow_click")

    @EventDesc("客态-取消关注结果")
    val EVENT_CUSTOMER_UNFOLLOW_RESULT = Event("event_customer_unfollow_result")

    @EventDesc("客态-加好友申请")
    val EVENT_CUSTOMER_ADD_FRIENDS_CLICK = Event("event_customer_add_friends_click")

    @EventDesc("客态-加好友申请结果")
    val EVENT_CUSTOMER_ADD_FRIENDS_RESULT = Event("event_customer_add_friends_result")

    @EventDesc("客态-发消息点击")
    val EVENT_CUSTOMER_IM_CLICK = Event("event_customer_im_click")

    @EventDesc("实名认证弹窗展示")
    val EVENT_REALNAME_SHOW = Event("event_realname_show")

    @EventDesc("实名认证_关闭按钮点击")
    val EVENT_REALNAME_CLOSE_CLICK = Event("event_realname_close_click")

    @EventDesc("实名认证结果")
    val EVENT_REALNAME_RESULT = Event("event_realname_result")

    @EventDesc("实名拦截弹窗展示")
    val EVENT_REALNAME_INTERCEPT = Event("event_realname_intercept")

    @EventDesc("点击角编换装")
    val EVENT_AVATAR_CLICK = Event("event_avatar_click")

    @EventDesc("点击建造")
    val EVENT_CREATION_CLICK = Event("event_creation_click")

    @EventDesc("点击进入派对")
    val EVENT_PARTY_CLICK = Event("event_party_click")

    @EventDesc("点击进入工坊")
    val EVENT_WORKSHOP_CLICK = Event("event_workshop_click")

    @EventDesc("点击选择模板")
    val EVENT_CREATION_TEMPLATE_CLICK = Event("event_creation_template_click")

    @EventDesc("点击进入广场")
    val EVENT_SQUARE_CLICK = Event("event_square_click")

    @EventDesc("版本更新弹窗出现")
    val EVENT_RENEW_SHOW = Event("event_renew_show")

    @EventDesc("版本更新点击")
    val EVENT_RENEW_CLICK = Event("event_renew_click")

    @EventDesc("版本更新退出")
    val EVENT_RENEW_CLOSE = Event("event_renew_close")

    @EventDesc("截屏分享")
    val EVENT_SCREENSHOT_SHARE = Event("event_screenshot_share")

    @EventDesc("推荐游戏icon曝光")
    val FEED_ITEM_SHOW = Event("feed_item_show")

    @EventDesc("推荐游戏icon点击")
    val FEED_ITEM_CLICK = Event("feed_item_click")

    @EventDesc("游戏icon点击")
    val ITEM_CLICK = Event("item_click")

    @EventDesc("游戏详情页展示")
    val APP_DETAIL = Event("app_detail")

    @EventDesc("点击开始游戏按钮")
    val EVENT_CLICK_DOWNLOAD = Event("click_download")

    @EventDesc("登录确认页切换账号点击")
    val EVENT_LOGIN_SWITCH_ACCOUNT = Event("login_switch_account")

    @EventDesc("登录确认页账号管理页面展示")
    val EVENT_LOGIN_ACCOUNT_MANAGEMENT_SHOW= Event("login_account_management_show")

    @EventDesc("点击开始游戏按钮")
    val EVENT_LOGIN_ACCOUNT_MANAGEMENT_CLICK = Event("login_account_management_click")

    @EventDesc("角色创建确认")
    val EVENT_CREATE_ROLE_CONFIRM = Event("event_create_role_confirm")

    @EventDesc("昵称确认")
    val EVENT_NICKNAME_CONFIRM = Event("event_nickname_confirm")

    @EventDesc("新手引导展示结果")
    val EVENT_BEGINNER_GUIDE_SHOW_RESULT = Event("event_beginner_guide_show_result")

    @EventDesc("新手引导展示")
    val EVENT_BEGINNER_GUIDE_SHOW = Event("event_beginner_guide_show")

    @EventDesc("新手引导点击")
    val EVENT_BEGINNER_GUIDE_CLICK = Event("event_beginner_guide_click")

    @EventDesc("首页刷新按钮点击")
    val EVENT_HOME_REFRESH_CLICK = Event("event_refresh_click")

    @EventDesc("拍短剧按钮点击")
    val EVENT_SHORTPLAYS_CLICK = Event("event_shortplays_click")

    @EventDesc("个人主页展示")
    val EVENT_HOMEPAGE_LAUNCH = Event("event_homepage_launch")

    @EventDesc("个人主页-点击按钮跳转")
    val EVENT_HOMEPAGE_CROSS = Event("event_homepage_cross")

    @EventDesc("个人主页浏览作品库")
    val EVENT_SELF_HOMEPAGE_BROWSE = Event("event_self_homepage_browse")

    @EventDesc("个人主页修改昵称")
    val EVENT_SELF_HOMEPAGE_NICKNAME = Event("event_self_homepage_nickname")

    @EventDesc("实名认证通过提醒弹窗")
    val EVENT_REALNAME_REMINDPASS_SHOW = Event("event_realname_remindpass_show")

    @EventDesc("实名认证通过提醒弹窗点击")
    val EVENT_REALNAME_REMINDPASS_CLICK = Event("event_realname_remindpass_click")

    @EventDesc("游戏房间展示")
    val EVENT_ROOM_SHOW = Event("event_room_show")

    @EventDesc("游戏房间点击加入")
    val EVENT_ROOM_CLICK_JOIN = Event("event_room_click_join")

    @EventDesc("评论页面展示")
    val EVENT_COMMENT_PAGE_SHOW = Event("event_comment_page_show")

    @EventDesc("评论页面排序按钮点击")
    val EVENT_COMMENT_PAGE_SORT_CLICK = Event("event_comment_page_sort_click")

    @EventDesc("评论页面编写icon点击")
    val EVENT_COMMENT_WRITE_CLICK = Event("event_comment_write_click")

    @EventDesc("评论页面编写弹窗展示")
    val EVENT_COMMENT_WRITE_POPUP_SHOW = Event("event_comment_write_popup_show")

    @EventDesc("评论页面编写弹窗点击发布评论")
    val EVENT_COMMENT_WRITE_POPUP_PUBLISH = Event("event_comment_write_popup_publish")

    @EventDesc("评论页面编写弹窗关闭")
    val EVENT_COMMENT_WRITE_POPUP_PUBLISH_CLOSE = Event("event_comment_write_popup_publish_close")

    @EventDesc("评论页面编写弹窗点击发布评论结果")
    val EVENT_COMMENT_WRITE_POPUP_PUBLISH_RESULT = Event("event_comment_write_popup_publish_result")

    @EventDesc("【我也要建造地图】按钮点击")
    val EVENT_DETAIL_CREATION_CLICK = Event("event_detail_creation_click")

    @EventDesc("广场关闭提示弹窗展示")
    val EVENT_SQUARE_CLOSE_PROMPT_SHOW = Event("event_square_close_prompt_show")

    @EventDesc("广场关闭提示弹窗点击关闭")
    val EVENT_SQUARE_CLOSE_PROMPT_CLOSE = Event("event_square_close_prompt_close")

    @EventDesc("兴趣弹窗页展示")
    val EVENT_INTEREST_POPUP_SHOW = Event("event_interest_popup_show")

    @EventDesc("兴趣选项选中")
    val EVENT_INTEREST_POPUP_MARK_CLICK = Event("event_interest_popup_mark_click")

    @EventDesc("提交兴趣喜好")
    val EVENT_INTEREST_POPUP_CONFIRM = Event("event_interest_popup_confirm")

    @EventDesc("点击复制gameid")
    val EVENT_GAMEID_COPY = Event("event_gameid_copy")

    @EventDesc("点击关注作者")
    val EVENT_AUTHOR_FOLLOW = Event("event_author_follow")

    @EventDesc("给游戏点赞")
    val EVENT_GAME_LIKE = Event("event_game_like")

    @EventDesc("点击右上角反馈")
    val EVENT_FEEDBACK_CLICK = Event("event_feedback_click")

    @EventDesc("提交反馈")
    val EVENT_FEEDBACK_CLICK_SUBMIT = Event("event_feedback_click_submit")

    @EventDesc("点赞评论")
    val EVENT_COMMENT_LIKE = Event("event_comment_like")

    @EventDesc("评论发布成功上报")
    val EVENT_COMMENT_PUBLISH_SUCCESS = Event("event_comment_publish_success")

    @EventDesc("派对币直充页面曝光")
    val EVENT_PARTY_RECHARGE_SHOW = Event("event_party_recharge_show")

    @EventDesc("派对币充值疑问项曝光")
    val EVENT_PARTY_RECHARGE_TIPS_SHOW = Event("event_party_recharge_tips_show")

    @EventDesc("派对充值收银台曝光")
    val EVENT_PARTY_PAY_SHOW = Event("event_party_pay_show")

    @EventDesc("派对充值收银台点击支付")
    val EVENT_PARTY_PAY_CLICK = Event("event_party_pay_click")

    @EventDesc("跳转第三方支付")
    val EVENT_PARTY_JUMP_THE_THIRD = Event("event_party_jump_the_third")

    @EventDesc("顶栏tab点击")
    val EVENT_HOME_TAB_CLICK = Event("event_home_tab_click")

    @EventDesc("客户端场景触发系统截图")
    val EVENT_SCREEN_SHARE = Event("event_screen_share")

    @EventDesc("购买派对币支付成功")
    val EVENT_PARTY_PAY_SUCCESS = Event("event_party_pay_success")

    @EventDesc("派对币充值失败")
    val EVENT_PARTY_PAY_FAIL = Event("event_party_pay_fail")

    @EventDesc("消耗派对币点击")
    val EVENT_PARTY_PAY_COIN_CLICK = Event("event_party_pay_coin_click",)

    @EventDesc("消耗派对币曝光")
    val EVENT_PARTY_PAY_COIN_SHOW = Event("event_party_pay_coin_show",)

    @EventDesc("消耗派对币结果")
    val EVENT_PARTY_PAY_COIN_SUCCESS = Event("event_party_pay_coin_success",)

    @EventDesc("派对币充值到账成功")
    val EVENT_PARTY_RECHARGE_SUCCESS = Event("event_party_recharge_success",)

    @EventDesc("收银台页面曝光")
    val EVENT_PARTY_CLIENT_PAY_SHOW = Event("event_client_pay_show",)

    @EventDesc("派对充值收银台点击支付")
    val EVENT_PARTY_CLIENT_PAY_CLICK = Event("event_client_pay_click",)

    @EventDesc("跳转第三方支付")
    val EVENT_PARTY_CLIENT_PAY_JUMP_THIRD = Event("event_client_jump_the_third",)
}