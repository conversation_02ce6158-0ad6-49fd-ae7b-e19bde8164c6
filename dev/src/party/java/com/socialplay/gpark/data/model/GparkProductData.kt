package com.socialplay.gpark.data.model

data class GparkProductData(val productCode: String)


data class GoogleProductResult(
    val body: BodyRequestOrder?,
    val code: Int,
    val message: String?
)
data class GooglePayResultData(
    val originalJson: String,
    val orderId: String,
    val purchaseToken: String,
    val needUpdate: Boolean,//是否需要通知游戏或者web订单结果
    val productType:String,
    val googleOrderId: String,
    val uuid: String? = null,
    val consumeAgain: Boolean? = false // 是否是重新消耗订单
)
