package com.socialplay.gpark.data.interactor

import android.content.Context
import com.bin.android.oaid.IGetter
import com.bin.android.oaid.OAID
import com.socialplay.gpark.data.kv.MetaKV
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

object DeviceInteractorWrapper {
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    fun ids(): List<String> {
        return listOf()
    }

//    val firebaseId: String
//        get() = googleInteractor.firebaseId.value ?: ""
//
//    val user_pseudo_id: String
//        get() = googleInteractor.user_pseudo_id
//
//    val googleAdId: String
//        get() = googleInteractor.googleAdId

    private val isGenerateOaIdInvoked = AtomicBoolean(false)

    fun generateOaId(context: Context, metaKV: MetaKV, onComplete: (result: String) -> Unit) {
        if (!isGenerateOaIdInvoked.compareAndSet(false, true)) {
            return
        }
        scope.launch {
            var i = 0
            var flag = true
            while (i < 10 && flag) {
                i++
                val getter = object : IGetter {
                    override fun onComplete(result: String) {
                        if (result.isNotEmpty()) {
                            flag = false
                            onComplete(result)
                        }
                    }

                    override fun onError(error: Exception) {
                        // 获取oaid失败, 一些手机不支持oaid
                    }
                }
                try {
                    OAID.get(context, getter)
                } catch (err: Error) {
                    Timber.w(err)
                }
                delay(2000)
            }
        }
    }
}