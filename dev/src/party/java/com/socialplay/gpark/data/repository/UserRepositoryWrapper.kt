package com.socialplay.gpark.data.repository

import com.socialplay.gpark.data.model.realname.RealNameCheckEncryptBody
import com.meta.box.data.model.realname.RealNameCheckResult
import com.socialplay.gpark.data.api.MetaApiWrapper
import com.socialplay.gpark.data.api.MetaDTokenApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.local.ShareRecordDao
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.realname.RealNameAutoInfo
import com.socialplay.gpark.data.model.realname.RealNameCardNoName
import com.socialplay.gpark.data.model.realname.RealNameConfig
import com.socialplay.gpark.data.model.realname.RealNameSurplusGameTime
import com.socialplay.gpark.data.model.user.AccountLoginRequest
import com.socialplay.gpark.data.model.user.AccountLoginRequest.Companion.SMS_TYPE_FORGETPWD
import com.socialplay.gpark.data.model.user.AccountLoginRequest.Companion.SMS_TYPE_LOGIN
import com.socialplay.gpark.data.model.user.AuthInfoApiResult
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.data.model.user.OneKeyLoginRequest
import com.socialplay.gpark.data.model.user.VerifyPhoneCode
import com.socialplay.gpark.data.model.user.WXLoginRequest
import com.socialplay.gpark.function.http.CheckTokenInterceptor
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber

class UserRepositoryWrapper(
    private val metaApi: MetaApiWrapper, private val detokenMetaApi: MetaDTokenApi, private val metaKV: MetaKV, private val authRepository: AuthRepository, private val shareRecordDao: ShareRecordDao
) : UserRepository(metaApi, detokenMetaApi, metaKV, authRepository, shareRecordDao) {

    fun fetchCustomToken(): String? {
        return ""
    }

    fun updateCustomToken(customToken: String?) {
//        GlobalScope.launch(Dispatchers.IO) {
//            Timber.tag(CheckTokenInterceptor.TAG).d("fetchFirebaseIdToken start")
//            val idToken = kotlin.runCatching { authRepository.getIdToken(customToken, true) }.getOrNull()
//            Timber.tag(CheckTokenInterceptor.TAG).d("fetchFirebaseIdToken customToken:$customToken, idToken:$idToken")
//            metaKV.accountWrapper.firebaseIdToken = GsonUtil.gson.toJson(idToken)
//        }
    }


    /**
     * 游客登录
     */
    suspend fun postGuestLogin(): Flow<DataResult<AuthInfoApiResult>> = flow {
        metaKV.account.accessToken = null
        val dataResult = DataSource.getDataResultForApi { metaApi.visitorLoginDToken() }
        Timber.tag(CheckTokenInterceptor.TAG).d("postGuestLogin dataResult:$dataResult")
        dataResult.data?.accessToken
        saveToken(
            "", dataResult.data?.refreshToken, dataResult.data?.accessToken, dataResult.data?.accessTokenExpire
        )
        if (dataResult.succeeded && dataResult.data?.userInfo != null) {
            accountInteractor.initTokenExpireCheck()
        }
        emit(dataResult)
    }

    /**
     * qq登录
     */
    suspend fun loginByQQ(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> {
        val openId = response.userid
        val token = response.token
        val dataResult = DataSource.getDataResultForApi {
            metaApi.loginUnite(
                AccountLoginRequest(
                    AccountLoginRequest.LOGIN_REQUEST_TYPE_ALL, response.oauthThirdWay.way, openId, token
                )
            )
        }
        return processAuthLogin(dataResult, loginType, LoginWay.QQ, response.token)
    }

    /**
     * 微信登录
     */
    suspend fun loginByWX(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> {
        val authCode = response.token
        val dataResult = DataSource.getDataResultForApi {
            metaApi.loginUnite(
                AccountLoginRequest(
                    AccountLoginRequest.LOGIN_REQUEST_TYPE_ALL, response.oauthThirdWay.way, null, authCode
                )
            )
        }
        return processAuthLogin(dataResult, loginType, LoginWay.Wechat, response.token)
    }

    /**
     * 抖音登录
     */
    suspend fun loginByDouYin(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> {
        val authCode = response.token
        val dataResult = DataSource.getDataResultForApi { metaApi.douyinLogin(WXLoginRequest(authCode)) }
        return processAuthLogin(dataResult, loginType, LoginWay.Douyin, response.token)
    }

    /**
     * 快手登录
     */
    suspend fun loginByKwai(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> {
        val authCode = response.token
        val dataResult = DataSource.getDataResultForApi { metaApi.kwaiLogin(WXLoginRequest(authCode)) }
        return processAuthLogin(dataResult, loginType, LoginWay.KuaiShou, response.token)
    }

    /**
     * 233乐园登录
     */
    suspend fun loginByLeYuan(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> {
        val authCode = response.token
        val dataResult = DataSource.getDataResultForApi {
            metaApi.loginUnite(
                AccountLoginRequest(
                    AccountLoginRequest.LOGIN_REQUEST_TYPE_ALL, response.oauthThirdWay.way, null, authCode
                )
            )
        }
        return processAuthLogin(dataResult, loginType, LoginWay.Leyuan, response.token)
    }

    /**
     * 手机号登录
     */
    suspend fun loginByPhone(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> {
        val phone = response.userid
        val phoneCode = response.token
        val dataResult = DataSource.getDataResultForApi {
            metaApi.loginUnite(
                AccountLoginRequest(
                    AccountLoginRequest.LOGIN_REQUEST_TYPE_ALL, response.oauthThirdWay.way, phone, phoneCode
                )
            )
        }
        return processAuthLogin(dataResult, loginType, LoginWay.Phone, response.token)
    }

    override suspend fun accountLogin(account: String, password: String, loginType: LoginType): Flow<LoginState<MetaUserInfo>> {
        val passwordEncrypt = passwordEncrypt(password)
        val dataResult = DataSource.getDataResultForApi {
            metaApi.loginUnite(
                AccountLoginRequest(
                    AccountLoginRequest.LOGIN_REQUEST_TYPE_ONLY_LOGIN,
                    LoginWay.Account.way,
                    account,
                    passwordEncrypt
                )
            )
        }
        return processAuthLogin(dataResult, loginType, LoginWay.Account, "")
    }

    /**
     * 本机一键登录
     */
    suspend fun loginByOneKey(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> {
        val token = response.token
        val dataResult = DataSource.getDataResultForApi {
            metaApi.loginByOneKey(OneKeyLoginRequest(token))
        }
        return processAuthLogin(dataResult, loginType, LoginWay.Onekey, response.token)
    }

    /**
     * 获取登录验证码
     */
    suspend fun getLoginPhoneCode(phoneNumber: String): Flow<DataResult<Boolean>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.getLoginPhoneCode(
                mapOf(
                    "phone" to phoneNumber,
                    "sendType" to SMS_TYPE_LOGIN
                )
            )
        })
    }

    /**
     * 获取登录验证码
     */
    suspend fun getForgetPwdPhoneCode(phoneNumber: String): Flow<DataResult<Boolean>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.getLoginPhoneCode(
                mapOf(
                    "phone" to phoneNumber,
                    "sendType" to SMS_TYPE_FORGETPWD
                )
            )
        })
    }

    /**
     * 获取验证码
     */
    fun getPhoneSmsCode(phoneNumber: String, sendType: String): Flow<DataResult<Boolean>> = flow {
        emit(DataSource.getDataResultForApi {
            metaApi.getLoginPhoneCode(
                mapOf(
                    "phone" to phoneNumber,
                    "sendType" to sendType
                )
            )
        })
    }

    /**
     * 验证换绑旧手机号
     */
    fun verifyPhoneCode(
        phoneNumber: String,
        phoneCode: String,
        sendType: String
    ): Flow<DataResult<Boolean>> =
        flow {
            val dataResult = DataSource.getDataResultForApi {
                metaApi.verifyPhoneCode(
                    VerifyPhoneCode(
                        sendType = sendType,
                        phone = phoneNumber,
                        code = phoneCode,
                    )
                )
            }
            emit(dataResult)
        }

//    /**
//     * 手机号绑定
//     *
//     * @param map
//     * @return
//     */
//    @POST("authorize/v2/third/binding")
//    suspend fun bindPhone(@Body request: ThirdBindRequest): ApiResult<Boolean>
//
//    /**
//     * 更换手机号绑定
//     *
//     * @param map
//     * @return
//     */
//    @POST("/multiplatform/auth/v1/phone/change")
//    suspend fun changeNewPhone(@Body map: Map<String, String>): ApiResult<Boolean>
//
//    /**
//     * 换绑获取原手机验证码
//     */
//    @POST("bind/v1/sms/rebind")
//    suspend fun getVerifyCode(@Body map: Map<String, String>): ApiResult<Boolean>
//
//    /**
//     * 换绑获取新手机验证码
//     */
//    @POST("bind/v1/sms/new/bind")
//    suspend fun getNewVerifyCode(@Body map: Map<String, String>): ApiResult<Boolean>
//
//    /**
//     * 换绑获取原手机验证码
//     */
//    @POST("bind/v1/rebind/phone/check")
//    suspend fun verifyCode(@Body map: Map<String, String>): ApiResult<JsonElement>

    override fun passwordReset(phoneNumber: String, smsCode: String, newPassword: String):Flow<DataResult<Boolean>>  = flow {
        val dataResult = DataSource.getDataResultForApi {
            metaApi.accountPasswordReset(
                mapOf(
                    "validatePhone" to phoneNumber,
                    "validatePhoneCode" to smsCode,
                    "newPassword" to (passwordEncrypt(newPassword) ?: newPassword),
                    "type" to AccountLoginRequest.TYPE
                )
            )
        }
        emit(dataResult)
    }

    suspend fun realNameCheck(name: String, cardNo: String): DataResult<RealNameCheckResult> {
        val rsaCardName = passwordEncrypt(name).orEmpty()
        val rsaCardNo = passwordEncrypt(cardNo).orEmpty()
        val realNameCheckBody = RealNameCheckEncryptBody(rsaCardName, rsaCardNo)
        return DataSource.getDataResultForApi { metaApi.realNameCheck(realNameCheckBody) }
    }

    suspend fun realNameSave(
        name: String,
        cardNo: String,
        pkgName: String,
        gameId: String
    ): DataResult<Any> {
        var gameLong: Long = 0;
        if (gameId.isNotEmpty()) {
            gameLong = gameId.toLong()
        }
        val eName = passwordEncrypt(name).orEmpty()
        val eCardNo = passwordEncrypt(cardNo).orEmpty()
        val realNameCardNoName = RealNameCardNoName(pkgName, gameLong, eCardNo, eName)
        return DataSource.getDataResultForApi { metaApi.realNameSave(realNameCardNoName) }
    }

    suspend fun realNameClear(name: String, cardNo: String): Flow<DataResult<Any>> = flow {
        val eName = passwordEncrypt(name).orEmpty()
        val eCardNo = passwordEncrypt(cardNo).orEmpty()
        val realNameCardNoName =
            RealNameCardNoName(cardNoEncrypt = eCardNo, realNameEncrypt = eName)
        emit(DataSource.getDataResultForApi { metaApi.realNameClear(realNameCardNoName) })
    }

    suspend fun realNameDetail(): DataResult<RealNameAutoInfo> {
        return DataSource.getDataResultForApi { metaApi.realNameDetail() }
    }

    suspend fun getRealNameSurplusGameTimeV3(
        gameId: Long,
        gamePkg: String
    ): DataResult<RealNameSurplusGameTime> {
        return DataSource.getDataResultForApi { metaApi.getRealNameSurplusGameTimeV3(hashMapOf("gameId" to "$gameId", "packageName" to gamePkg)) }
    }

    suspend fun realNameConfig(gameId: String? = null): Flow<DataResult<RealNameConfig>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.getRealNameConfig(gameId) })
    }
}