package com.socialplay.gpark.data.model

import android.os.Parcelable
import android.os.SystemClock
import com.socialplay.gpark.BuildConfig
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.io.Serializable
import kotlin.collections.HashMap


/**
 * create by: bin on 2020/12/21
 *
 * 更新实体类
 *
"id": 配置ID,
"strategyName": 更新策略名,
"targetVersion": 将要更新到的版本号,
"packageSize": "包大小",
"apkUrl": "包地址",
"apkMd5": "包MD5值",
"totalUpdCount": 更新数量限制,
"updateStrategy": "0为弱更 1强更",
"weakUpdateStrategy": "弱更策略 1弹窗 2首页 3设置页面",
"popFrequency": 弹窗频率 0为无限制,
"title": "版本更新",
"updateDescription": "最新的版本，快来体验吧",
"homePageFrequency": 主页频率 0为无限制
 */
@Parcelize
data class UpdateInfo(
    var id: Int? = null,
    var homePageFrequency: Int? = null,
    var updateApkMd5: String? = null,
    var updateApkUrl: String? = null,
    var updateApkSize: Long? = null,
    var popFrequency: Int? = null,
    var targetVersion: String? = null,
    var title: String? = null,
    var updateDescription: String? = null,
    var updateStrategy: Int? = null,// 0弱更新, 1强更
    var weakUpdateStrategy: String? = null,
    val patchInfo: UpdatePatch? = null
) : Serializable, Parcelable {

    @IgnoredOnParcel
    val uniqueTag: Long = id?.toLong() ?: SystemClock.elapsedRealtime()

    @IgnoredOnParcel
    val isForceUpdate: Boolean get() = updateStrategy == 1

    fun getUpdateEventMap(): HashMap<String, Any> {
        val hashMap = HashMap<String, Any>()
        hashMap["update_id"] = id ?: -1
        hashMap["update_target_version"] = targetVersion ?: ""
        hashMap["update_current_version"] = BuildConfig.VERSION_NAME
        hashMap["update_strong"] = updateStrategy ?: -1
        hashMap["update_weak"] = weakUpdateStrategy ?: ""
        return hashMap
    }
}

@Parcelize
data class UpdatePatch(
        val compress: String, // bzip2
        val differenceType: String, // bsdiff
        val channelInfo: String?,
        val patchMd5: String,
        val patchSize: Long,
        val patchUrl: String,
        val oldBaseApkMd5: String,
        val newBaseApkMd5: String,
): Serializable, Parcelable