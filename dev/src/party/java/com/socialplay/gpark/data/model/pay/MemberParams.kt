package com.socialplay.gpark.data.model.pay

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/10/09
 *     desc   :会员下单参数
 *
 */
data class MemberParams(
    //价格
    val amount: Int,
    //商品名称
    val productCode: String,
    //会员档位
    val grade: String,
    //支付类型,会员"0",大会员"1",乐币充值"",新大会员"2
    val memberType: String,
    val happyCoin: String,
    val payType: Int,
    val reward: String,
    var reason: String = "pay_failed",
    val sceneCode: String = AgentPayType.PAY_OWN_SENCECODE,
    var cpExtra :String,
):BasePayParams()
