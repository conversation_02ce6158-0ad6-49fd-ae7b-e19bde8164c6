package com.socialplay.gpark.data.repository

import com.socialplay.gpark.data.api.MetaApiWrapper
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.PayOrderInfo
import com.socialplay.gpark.data.model.pay.PayResultEntity
import com.socialplay.gpark.data.model.pay.TakeOrderInfo
import com.socialplay.gpark.data.model.pay.UserBalance
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/07/19
 *     desc   :会员一期
 *
 */
class UserPrivilegeRepository(private val metaApi: MetaApiWrapper) {
    fun privilegePlaceOrder(
        payChannel: Int,
        takeOrderInfo: TakeOrderInfo
    ): Flow<DataResult<PayResultEntity>> = flow {
        val createOrderResultEntity =
            DataSource.getDataResultForApi { metaApi.privilegePlaceOrderParty(takeOrderInfo) }
        if (createOrderResultEntity.succeeded) {
            // 下单成功后 进行预支付下单
            val payOrderBean = PayOrderInfo()
            payOrderBean.orderCode = createOrderResultEntity.data?.orderCode
            payOrderBean.payChannel = payChannel
            val result = DataSource.getDataResultForApi { metaApi.createOrderPayment(payOrderBean) }
            emit(result)
        } else {
            emit(
                DataResult.Error(
                    createOrderResultEntity.code ?: 0,
                    createOrderResultEntity.message ?: "",
                )
            )
        }
    }

    fun partyMWPay(hashMap: Map<String, Any?>): Flow<DataResult<*>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.partyMWPay(hashMap) })
    }
}