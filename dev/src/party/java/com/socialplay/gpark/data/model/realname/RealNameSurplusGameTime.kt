package com.socialplay.gpark.data.model.realname

/**
 * @author: ning.wang
 * @date: 2021-06-15 4:52 下午
 * @desc: 版本 v3
 *
 */
data class RealNameSurplusGameTime(
    /**
     * 弹窗类型 [Popup]
     * 必须存在
     */
    val popup: String,

    /**
     * 原因类型
     * 必须存在
     */
    val type: String,

    /**
     *剩余时长，单位毫秒
     */
    val surplusGameTime: Long,

    /**
     * 弹窗的话术
     */
    val message: String?, // 弹窗的话术
) {

    companion object {

        /**
         * [https://wiki.appshahe.com/pages/viewpage.action?pageId=107548174]
         */
        object Popup {
            /**
             * 无弹窗
             */
            const val NO = "no_popup"

            /**
             * 实名弹窗
             */
            const val REAL_NAME = "realname_popup"

            /**
             * 青少年弹窗
             */
            const val CHILD_LIMIT = "child_limit_popup"

            /**
             * 家长模式-时间限制弹窗
             */
            const val PATRIARCH_TIME = "patriarch_time_popup"

            /**
             * 家长模式-游戏限制弹窗
             */
            const val PATRIARCH_GAME = "patriarch_game_popup"
        }
    }
}
