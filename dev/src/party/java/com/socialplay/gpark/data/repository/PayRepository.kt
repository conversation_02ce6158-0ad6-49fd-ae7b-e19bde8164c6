package com.socialplay.gpark.data.repository

import android.content.Context
import com.socialplay.gpark.R
import com.socialplay.gpark.data.api.MetaApiWrapper
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.PayOrderInfo
import com.socialplay.gpark.data.model.Recharge
import com.socialplay.gpark.data.model.pay.AgentPayType
import com.socialplay.gpark.data.model.pay.PayChannelList
import com.socialplay.gpark.data.model.pay.PayResultEntity
import com.socialplay.gpark.data.model.pay.TakeOrderParams
import com.socialplay.gpark.data.model.pay.TakeOrderResult
import com.socialplay.gpark.data.model.pay.mobile.MobilePointsParam
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * Created by yaqi.liu on 2021/6/22
 */
class PayRepository(private val metaApi: MetaApiWrapper) {
    /**
     * 获取充值商品列表
     */
    fun getRecharges(): Flow<DataResult<ArrayList<Recharge>>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.getRecharges() })
    }

    suspend fun getPayChannels(
        selfPkgName: String,
        sceneCode: String,
        gamePkgName: String,
        gameId: String
    ): Flow<DataResult<PayChannelList>> = flow {
        val map = HashMap<String, String>()
        map["bundleId"] = selfPkgName
        map["sceneCode"] = sceneCode
        map["gameCode"] = gameId
        emit(DataSource.getDataResultForApi { metaApi.getPayChannels(map) })
    }

    fun cancelOrder(orderId: String): Flow<DataResult<Boolean>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.cancelOrder(orderId) })
    }

    suspend fun rechargingLoopParty(orderId: String): DataResult<Boolean> {
        val result = DataSource.getDataResultForApi { metaApi.rechargingLoop(orderId) }
        return if (result.succeeded) {
            if (result.data == true) {
                //支付成功
                result
            } else {
                //支付结果还没查询到
                DataResult.Loading
            }
        } else {
            result
        }
    }

    suspend fun takeOrderV3(
        takeOrderParams: TakeOrderParams,
        payChannel: Int,
        mobilePointsParam: MobilePointsParam?
    ) = flow {
        val createOrderResultEntity =
            DataSource.getDataResultForApi { metaApi.takeOrderV3(takeOrderParams) }
        if (createOrderResultEntity.succeeded) {
            val payAmount = createOrderResultEntity.data?.payAmount ?: -1
            var result: DataResult<PayResultEntity?>
            if (!createOrderResultEntity.data?.orderCode.isNullOrEmpty() && payAmount == 0) {
                //有订单号，且实际支付金额是0的，属于优惠券抵扣免单，需要特殊处理
                result = generateCouponDeductedFreeOrder(createOrderResultEntity.data?.orderCode)
                result.data?.subOrders = createOrderResultEntity.data?.subOrders
            } else {
                when (payChannel) {
                    AgentPayType.PAY_TYPE_HELP_PAY -> {
                        Timber.i("Processing help pay order request...")
                        result = DataSource.getDataResult {
                            val apiResult = metaApi.createHelpPayOrder(
                                createHelpPayOderInfo(createOrderResultEntity)
                            )
                            if (apiResult.isSuccessful) {
                                apiResult.data?.orderCode = createOrderResultEntity.data?.orderCode
                                apiResult.data?.payAmount = payAmount
                                apiResult.data?.channel = payChannel.toString()
                            }
                            apiResult.data
                        }
                    }

                    AgentPayType.PAY_TYPE_MOBILE_POINTS -> {
                        Timber.i("Processing mobile point order request...")
                        result = DataSource.getDataResultForApi {
                            metaApi.createOrderV3Payment(
                                createMobilePointOrderInfo(
                                    payChannel,
                                    createOrderResultEntity,
                                    mobilePointsParam
                                )
                            )
                        }
                        if (result.succeeded) {
                            result.data?.mobilePhone = mobilePointsParam?.mobilePhone
                            result.data?.sceneCode = createOrderResultEntity.data?.sceneCode
                        }
                    }

                    else -> {
                        Timber.i("Processing common order request...")
                        result = DataSource.getDataResultForApi {
                            metaApi.createOrderV3Payment(
                                createOrderInfo(
                                    payChannel,
                                    createOrderResultEntity
                                )
                            )
                        }
                    }
                }
            }
            result.data?.subOrders = createOrderResultEntity.data?.subOrders
            emit(result)
        } else {
            // 下单失败
            emit(
                DataResult.Error(
                    createOrderResultEntity.code ?: 0,
                    createOrderResultEntity.message ?: ""
                )
            )
        }
    }

    private fun createHelpPayOderInfo(createOrderResultEntity: DataResult<TakeOrderResult?>): HashMap<String, String> {
        val map = HashMap<String, String>()
        map["sceneCode"] = createOrderResultEntity.data?.sceneCode ?: ""
        map["bizOrderNo"] = createOrderResultEntity.data?.orderCode ?: ""
        return map
    }

    /**
     * 优惠券抵扣后支付0元免单处理
     */
    private fun generateCouponDeductedFreeOrder(orderCode: String?): DataResult<PayResultEntity?> {
        val payResultEntity = PayResultEntity()
        payResultEntity.payAmount = 0
        payResultEntity.orderCode = orderCode
        return DataResult.Success(payResultEntity, message = GlobalContext.get().get<Context>().getString(R.string.coupon_succeeded))
    }

    private fun createMobilePointOrderInfo(
        payChannel: Int,
        createOrderResultEntity: DataResult<TakeOrderResult?>,
        mobilePointsParam: MobilePointsParam?
    ): PayOrderInfo {
        val payOrderBean = PayOrderInfo()
        payOrderBean.orderCode = createOrderResultEntity.data?.orderCode
        payOrderBean.payChannel = payChannel
        payOrderBean.payTunnel = "1"
        //移动积分预支付是需要以下
        payOrderBean.phone = mobilePointsParam?.mobilePhone
        payOrderBean.sessionid = mobilePointsParam?.sessionId
        payOrderBean.fingerprint = mobilePointsParam?.fingerprint
        return payOrderBean
    }

    private fun createOrderInfo(
        payChannel: Int,
        createOrderResultEntity: DataResult<TakeOrderResult?>
    ): PayOrderInfo {
        val payOrderBean = PayOrderInfo()
        payOrderBean.orderCode = createOrderResultEntity.data?.orderCode
        payOrderBean.payChannel = payChannel
        payOrderBean.payTunnel = "1"
        return payOrderBean
    }
}