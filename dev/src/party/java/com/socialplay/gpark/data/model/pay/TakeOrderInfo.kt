package com.socialplay.gpark.data.model.pay

/**
 * author : yi.zhang
 * e-mail : <EMAIL>
 * time   : 2021/04/14
 * desc   :下单参数
 */
data class TakeOrderInfo(
    //订单总金额（单位：分【人民币】）
    var amount: Int = 0,
    //订单待支付金额（单位：分【人民币】）订单实际需要支付金额；支付金额=订单总金额-优惠券金额； 如没有优惠，待支付金额 == 订单总金额
    var payAmount: Int = 0,
    //商品编码
    var productCode: String? = null,
    //商品名称
    var productName: String? = null,
    //商品购买数量
    var count: Int = 1,
    //供应商订单标识
    var cpOrderId: String? = null,
    //防抖动随机码不能为空
    var nonce: String? = null,
    //联运游戏开发者（CP）的游戏维度标识符
    var appKey: String? = null,
    //提供给CP扩展透传字段（CP自定义扩展字段信息）
    var cpExtra: String? = null,
    //优惠券编码
    var couponCode: String? = null,
    //商品单价
    var productPrice: Int = 0,
    //sdk版本号
    var sdkVersion: String? = null,
    var sceneCode: String? = null,
    var gamePackage: String? = null,
    //自有平台 商品透传扩展字段
    var attachJson: String? = null,
    // 扩展字段
    var extendJson :String?=null,

     //移动积分支付时使用的sessionid,不是平台的【通付盾，风控参数,用于积分风控系统。从js中获取】
    var sessionid: String? = null,
    //移动积分支付时使用的，设备标识【通付盾，风控参数,用于积分风控系统。从js中获取】
    var fingerprint: String? = null,
    //移动积分支付时使用的，手机号
    var phone: String? = null,
    //赠送礼品
    var giveToken: String? = null,
    // 平台立减token(乐币充值)
    var platformReduceToken: String? = null,
    //乐币充值赠送权益
    var promotionToken: String? = null,
    var runtime: TakeOrderInfoRuntime? = null,
)

data class TakeOrderInfoRuntime(
    val gameId: String? = null
)
