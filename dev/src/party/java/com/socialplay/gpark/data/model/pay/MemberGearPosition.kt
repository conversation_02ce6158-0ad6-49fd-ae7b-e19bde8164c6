package com.socialplay.gpark.data.model.pay

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2023/09/14
 *     desc   :
 *
 */
data class MemberGearPosition(
    var selected: Int = 0,
    val originPrice: Int,
    val price: Int,
    val goodId: String,
    val goodName: String,
    val appKey: String,
    val sceneCode: String,
    val attachJson: String? = null,
){
    fun isSel(): Boolean {
        return selected == 1
    }
}
