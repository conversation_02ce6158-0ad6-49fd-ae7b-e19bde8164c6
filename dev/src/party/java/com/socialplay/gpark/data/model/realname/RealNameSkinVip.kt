package com.socialplay.gpark.data.model.realname

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.io.Serializable

@Parcelize
data class RealNameSkinVip(
    val title: String = "", //标题
    val imgUrl: String = "",// 图片
    val id: String = "", //特权ID
    var gameId: String = "",
    val source: String? = null // 奖励来源：实名给广告 ad、实名给皮肤 sakura_skin、修改实名 edit、游戏内实名没礼物：game、APP基础实名 normal（前两个后端返回的）
) : Serializable, Parcelable
