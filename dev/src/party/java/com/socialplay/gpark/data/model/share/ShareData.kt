package com.socialplay.gpark.data.model.share

import android.content.Context
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.meta.lib.mwbiz.MWBizBridge
import com.meta.share.util.toArrayList
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.extension.ifNullOrBlank
import kotlinx.coroutines.flow.single
import kotlinx.parcelize.Parcelize
import org.koin.core.context.GlobalContext

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/01
 *     desc   :
 * </pre>
 */
data class SharePlatform(
    val platform: String,
    val iconRes: Int,
    val titleRes: Int,
    val code: Int,
) {

    companion object {
        // 发贴子、发快手、发小红书、发抖音、发朋友圈、QQ 空间、私信好友、快手好友、抖音好友、微信好友、QQ 好友、生成图片、保存相册、复制链接、更多
        const val PLATFORM_FRIEND = "friend"
        const val PLATFORM_LINK = "link"
        const val PLATFORM_SYSTEM = "system"
        const val PLATFORM_SAVE = "save"
        const val PLATFORM_LONG_IMAGE = "longImage"
        const val PLATFORM_POST = "post"
        const val PLATFORM_COMMENT = "comment"

        const val PLATFORM_WECHAT_FRIEND = "wechat"
        const val PLATFORM_WECHAT_MOMENT = "wechatmoment"
        const val PLATFORM_QQ_FRIEND = "qq"
        const val PLATFORM_QQ_ZONE = "qzone"
        const val PLATFORM_DOUYIN_PUBLISH = "douyin"
        const val PLATFORM_DOUYIN_FRIEND = "douyinfriend"
        const val PLATFORM_KUAISHOU_PUBLISH = "kuaishou"
        const val PLATFORM_KUAISHOU_FRIEND = "kuaishoufriend"
        const val PLATFORM_XIAOHONGSHU_PUBLISH = "xiaohongshu"

        fun platformTrackParam(platform: String?): String {
            return when (platform) {
                PLATFORM_FRIEND -> "friends"
                PLATFORM_LINK -> "url"
                PLATFORM_SYSTEM -> "more"
                PLATFORM_SAVE -> "save"
                PLATFORM_POST -> "post"
                PLATFORM_COMMENT -> "comment"
                PLATFORM_WECHAT_FRIEND -> "wx"
                PLATFORM_WECHAT_MOMENT -> "pyq"
                PLATFORM_QQ_FRIEND -> "qqfriends"
                PLATFORM_QQ_ZONE -> "qq"
                PLATFORM_DOUYIN_PUBLISH -> "dy"
                PLATFORM_DOUYIN_FRIEND -> "dyfriends"
                PLATFORM_KUAISHOU_PUBLISH -> "ks"
                PLATFORM_KUAISHOU_FRIEND -> "ksfriends"
                PLATFORM_XIAOHONGSHU_PUBLISH -> "xhs"
                PLATFORM_LONG_IMAGE -> "image"
                else -> ""
            }
        }

        fun friend() = SharePlatform(
            PLATFORM_FRIEND,
            R.drawable.ic_share_friend,
            R.string.share_friend,
            1,
        )

        fun link() = SharePlatform(
            PLATFORM_LINK,
            R.drawable.ic_share_link,
            R.string.global_share_copy_link,
            2,
        )

        fun system() = SharePlatform(
            PLATFORM_SYSTEM,
            R.drawable.ic_share_system,
            R.string.global_share_system_share,
            3,
        )

        fun save() = SharePlatform(
            PLATFORM_SAVE,
            R.drawable.ic_share_download,
            R.string.share_save_album,
            4,
        )

        fun longImage() = SharePlatform(
            PLATFORM_LONG_IMAGE,
            R.drawable.ic_share_long_image,
            R.string.image,
            5,
        )

        fun post() = SharePlatform(
            PLATFORM_POST,
            R.drawable.ic_share_post,
            R.string.share_post,
            9,
        )

        fun wechatFriend() = SharePlatform(
            PLATFORM_WECHAT_FRIEND,
            R.drawable.ic_share_wechat,
            R.string.share_wechat_friends,
            10,
        )

        fun wechatMoments() = SharePlatform(
            PLATFORM_WECHAT_MOMENT,
            R.drawable.ic_share_wechat_moments,
            R.string.share_wechat_moments,
            11,
        )

        fun qqFriend() = SharePlatform(
            PLATFORM_QQ_FRIEND,
            R.drawable.ic_share_qq,
            R.string.share_qq,
            12,
        )

        fun qzone() = SharePlatform(
            PLATFORM_QQ_ZONE,
            R.drawable.ic_share_qzone,
            R.string.share_qzone,
            13,
        )

        fun douyin() = SharePlatform(
            PLATFORM_DOUYIN_PUBLISH,
            R.drawable.ic_share_douyin,
            R.string.share_douyin,
            14,
        )

        fun douyinFriend() = SharePlatform(
            PLATFORM_DOUYIN_FRIEND,
            R.drawable.ic_share_douyin_friend,
            R.string.share_douyin_friend,
            15,
        )

        fun kuaishou() = SharePlatform(
            PLATFORM_KUAISHOU_PUBLISH,
            R.drawable.ic_share_kuaishou,
            R.string.share_kuaishou,
            16,
        )

        fun kuaishouFriend() = SharePlatform(
            PLATFORM_KUAISHOU_FRIEND,
            R.drawable.ic_share_kuaishou_friend,
            R.string.share_kuaishou_friend,
            17,
        )

        fun xiaohongshu() = SharePlatform(
            PLATFORM_XIAOHONGSHU_PUBLISH,
            R.drawable.ic_share_xiaohongshu,
            R.string.share_red_note,
            18,
        )

        fun comment() = SharePlatform(
            PLATFORM_COMMENT,
            R.drawable.icon_comments,
            R.string.global_share_comments,
            19,
        )
    }
}

data class ShareConfig(
    val dy: Dy? = null,
    val ks: Ks? = null,
    val xhs: Xhs? = null,
    val wq: Wq? = null,
    @SerializedName(value = "system", alternate = ["systemShare"])
    val system: System? = null
) {

    companion object {
        fun default() = ShareConfig(
            dy = Dy("233派对！启动！", null, listOf("233派对")),
            ks = Ks("233派对！启动！", null, listOf("233派对")),
            xhs = Xhs("233派对！启动！", null, listOf("233派对")),
            wq = null,
            system = System("233派对！启动！", "")
        )

        fun replaceContent(
            input: String?,
            gameName: String? = null,
            authorName: String? = null,
            userName: String? = null,
            pvCount: Long? = null,
            postContent: String? = null
        ): String? {
            return if (input.isNullOrBlank()) {
                input
            } else {
                Regex("\\{([^{}]+?)\\}").replace(input) {
                    when (it.groupValues[1]) {
                        "gameName" -> gameName
                        "authorName" -> authorName
                        "userName" -> userName
                        "pvCount" -> (pvCount ?: 0).toString()
                        "postContent" -> postContent
                        else -> null
                    }.ifNullOrBlank { " " }
                }
            }
        }
    }

    fun titleContentByPlatform(
        platform: String,
        gameName: String? = null,
        authorName: String? = null,
        userName: String? = null,
        pvCount: Long? = null,
        postContent: String? = null
    ): Pair<String?, String?> {
        val title: String?
        val content: String?
        when (platform) {
            SharePlatform.PLATFORM_DOUYIN_PUBLISH,
            SharePlatform.PLATFORM_DOUYIN_FRIEND -> {
                title = dy?.shortTitle
                content = dy?.title
            }

            SharePlatform.PLATFORM_KUAISHOU_FRIEND -> {
                title = ks?.title
                content = ks?.content
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                title = xhs?.title
                content = xhs?.content
            }

            SharePlatform.PLATFORM_WECHAT_MOMENT,
            SharePlatform.PLATFORM_QQ_ZONE,
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            SharePlatform.PLATFORM_QQ_FRIEND -> {
                title = wq?.title
                content = wq?.content
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                title = system?.title
                content = system?.desc
            }

            else -> {
                title = null
                content = null
            }
        }

        return replaceContent(
            title,
            gameName,
            authorName,
            userName,
            pvCount,
            postContent
        ) to replaceContent(
            content,
            gameName,
            authorName,
            userName,
            pvCount,
            postContent
        )
    }

    fun titleByPlatform(platform: String): String? {
        return when (platform) {
            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                dy?.shortTitle
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                xhs?.title
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                system?.title
            }

            else -> {
                null
            }
        }
    }

    fun descByPlatform(platform: String): String? {
        return when (platform) {
            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                dy?.title
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                xhs?.content
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                system?.desc
            }

            else -> {
                null
            }
        }
    }

    fun tagsByPlatform(platform: String): List<String>? {
        return when (platform) {
            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                dy?.tags
            }

            SharePlatform.PLATFORM_KUAISHOU_PUBLISH -> {
                ks?.tags
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                xhs?.tags
            }

            else -> {
                null
            }
        }
    }

    data class Dy(
        val shortTitle: String?,
        val title: String?,
        val tags: List<String>?
    )

    data class Ks(
        val title: String?,
        val content: String?,
        val tags: List<String>?
    )

    data class Xhs(
        val title: String?,
        val content: String?,
        val tags: List<String>?
    )

    data class Wq(
        val title: String?,
        val content: String?,
        val tags: List<String>?
    )

    data class System(
        val title: String? = null,
        @SerializedName(value = "desc", alternate = ["content"])
        val desc: String? = null
    )
}

data class ShareData(
    val requestId: String,
    val platform: String,
    val mode: String,
    val images: List<String>? = null,
    val videos: List<String>? = null,
    val sticker: Sticker? = null,
    val extraTitle: String? = null,
    val extraContent: String? = null,
    val extraTags: List<String>? = null,
    val gameId: String? = null,
    val from: Int = 0
) {

    companion object {

        fun wechatFriendSingleImage(
            requestId: String,
            imagePath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            ShareHelper.MODE_SINGLE_IMAGE,
            images = listOf(imagePath),
            gameId = gameId
        )

        fun wechatFriendMultiImages(
            requestId: String,
            imagePaths: List<String>,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            ShareHelper.MODE_MULTI_IMAGES,
            images = imagePaths,
            gameId = gameId
        )

        fun wechatFriendSingleVideo(
            requestId: String,
            videoPath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            gameId = gameId
        )

        fun wechatFriendWebCard(
            requestId: String,
            sticker: Sticker,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            ShareHelper.MODE_SINGLE_WEB_CARD,
            sticker = sticker,
            gameId = gameId
        )

        fun wechatMomentsSingleImage(
            requestId: String,
            imagePath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_WECHAT_MOMENT,
            ShareHelper.MODE_SINGLE_IMAGE,
            images = listOf(imagePath),
            gameId = gameId
        )

        fun wechatMomentsSingleVideo(
            requestId: String,
            videoPath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_WECHAT_MOMENT,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            gameId = gameId
        )

        fun wechatMomentsWebCard(
            requestId: String,
            sticker: Sticker,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_WECHAT_MOMENT,
            ShareHelper.MODE_SINGLE_WEB_CARD,
            sticker = sticker,
            gameId = gameId
        )

        fun qqFriendSingleImage(
            requestId: String,
            imagePath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_QQ_FRIEND,
            ShareHelper.MODE_SINGLE_IMAGE,
            images = listOf(imagePath),
            gameId = gameId
        )

        fun qqFriendMultiImages(
            requestId: String,
            imagePaths: List<String>,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_QQ_FRIEND,
            ShareHelper.MODE_MULTI_IMAGES,
            images = imagePaths,
            gameId = gameId
        )

        fun qqFriendSingleVideo(
            requestId: String,
            videoPath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_QQ_FRIEND,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            gameId = gameId
        )

        fun qqFriendWebCard(
            requestId: String,
            sticker: Sticker,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_QQ_FRIEND,
            ShareHelper.MODE_SINGLE_WEB_CARD,
            sticker = sticker,
            gameId = gameId
        )

        fun qqZoneSingleImage(
            requestId: String,
            imagePath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_QQ_ZONE,
            ShareHelper.MODE_SINGLE_IMAGE,
            images = listOf(imagePath),
            gameId = gameId
        )

        fun qqZoneMultiImages(
            requestId: String,
            imagePaths: List<String>,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_QQ_ZONE,
            ShareHelper.MODE_MULTI_IMAGES,
            images = imagePaths,
            gameId = gameId
        )

        fun qqZoneSingleVideo(
            requestId: String,
            videoPath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_QQ_ZONE,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            gameId = gameId
        )

        fun qqZoneWebCard(
            requestId: String,
            sticker: Sticker,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_QQ_ZONE,
            ShareHelper.MODE_SINGLE_WEB_CARD,
            sticker = sticker,
            gameId = gameId
        )

        fun douyinPublishSingleImage(
            requestId: String,
            imagePath: String,
            config: ShareConfig? = null,
            shortTitle: String? = null,
            title: String? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_DOUYIN_PUBLISH,
            ShareHelper.MODE_SINGLE_IMAGE,
            images = listOf(imagePath),
            extraTitle = shortTitle ?: config?.dy?.shortTitle,
            extraContent = title ?: config?.dy?.title,
            extraTags = tags ?: config?.dy?.tags,
            gameId = gameId
        )

        fun douyinPublishMultiImages(
            requestId: String,
            imagePaths: List<String>,
            config: ShareConfig? = null,
            shortTitle: String? = null,
            title: String? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_DOUYIN_PUBLISH,
            ShareHelper.MODE_MULTI_IMAGES,
            images = imagePaths,
            extraTitle = shortTitle ?: config?.dy?.shortTitle,
            extraContent = title ?: config?.dy?.title,
            extraTags = tags ?: config?.dy?.tags,
            gameId = gameId
        )

        fun douyinPublishSingleVideo(
            requestId: String,
            videoPath: String,
            config: ShareConfig? = null,
            shortTitle: String? = null,
            title: String? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_DOUYIN_PUBLISH,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            extraTitle = shortTitle ?: config?.dy?.shortTitle,
            extraContent = title ?: config?.dy?.title,
            extraTags = tags ?: config?.dy?.tags,
            gameId = gameId
        )

        fun douyinFriendSingleImage(
            requestId: String,
            imagePath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_DOUYIN_FRIEND,
            ShareHelper.MODE_SINGLE_IMAGE,
            images = listOf(imagePath),
            gameId = gameId
        )

        fun douyinFriendMultiImages(
            requestId: String,
            imagePaths: List<String>,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_DOUYIN_FRIEND,
            ShareHelper.MODE_MULTI_IMAGES,
            images = imagePaths,
            gameId = gameId
        )

        fun douyinFriendSingleVideo(
            requestId: String,
            videoPath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_DOUYIN_FRIEND,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            gameId = gameId
        )

        fun douyinFriendWebCard(
            requestId: String,
            sticker: Sticker,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_DOUYIN_FRIEND,
            ShareHelper.MODE_SINGLE_WEB_CARD,
            sticker = sticker,
            gameId = gameId
        )

        fun kuaishouPublishSingleImage(
            requestId: String,
            imagePath: String,
            config: ShareConfig? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
            ShareHelper.MODE_SINGLE_IMAGE,
            images = listOf(imagePath),
            extraTags = tags ?: config?.ks?.tags,
            gameId = gameId
        )

        fun kuaishouPublishMultiImages(
            requestId: String,
            imagePaths: List<String>,
            config: ShareConfig? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
            ShareHelper.MODE_MULTI_IMAGES,
            images = imagePaths,
            extraTags = tags ?: config?.ks?.tags,
            gameId = gameId
        )

        fun kuaishouPublishSingleVideo(
            requestId: String,
            videoPath: String,
            config: ShareConfig? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            extraTags = tags ?: config?.ks?.tags,
            gameId = gameId
        )

        fun kuaishouFriendSingleImage(
            requestId: String,
            imagePath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_KUAISHOU_FRIEND,
            ShareHelper.MODE_SINGLE_IMAGE,
            images = listOf(imagePath),
            gameId = gameId
        )

        fun kuaishouFriendMultiImages(
            requestId: String,
            imagePaths: List<String>,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_KUAISHOU_FRIEND,
            ShareHelper.MODE_MULTI_IMAGES,
            images = imagePaths,
            gameId = gameId
        )

        fun kuaishouFriendSingleVideo(
            requestId: String,
            videoPath: String,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_KUAISHOU_FRIEND,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            gameId = gameId
        )

        fun kuaishouFriendWebCard(
            requestId: String,
            sticker: Sticker,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_KUAISHOU_FRIEND,
            ShareHelper.MODE_SINGLE_WEB_CARD,
            sticker = sticker,
            gameId = gameId
        )

        fun xiaohongshuPublishSingleImage(
            requestId: String,
            imagePath: String,
            config: ShareConfig? = null,
            title: String? = null,
            content: String? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH,
            ShareHelper.MODE_SINGLE_IMAGE,
            images = listOf(imagePath),
            extraTitle = title ?: config?.xhs?.title,
            extraContent = content ?: config?.xhs?.content,
            extraTags = tags ?: config?.xhs?.tags,
            gameId = gameId
        )

        fun xiaohongshuPublishMultiImages(
            requestId: String,
            imagePaths: List<String>,
            config: ShareConfig? = null,
            title: String? = null,
            content: String? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH,
            ShareHelper.MODE_MULTI_IMAGES,
            images = imagePaths,
            extraTitle = title ?: config?.xhs?.title,
            extraContent = content ?: config?.xhs?.content,
            extraTags = tags ?: config?.xhs?.tags,
            gameId = gameId
        )

        fun xiaohongshuPublishSingleVideo(
            requestId: String,
            videoPath: String,
            config: ShareConfig? = null,
            title: String? = null,
            content: String? = null,
            tags: List<String>? = null,
            gameId: String? = null
        ) = ShareData(
            requestId,
            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH,
            ShareHelper.MODE_SINGLE_VIDEO,
            videos = listOf(videoPath),
            extraTitle = title ?: config?.xhs?.title,
            extraContent = content ?: config?.xhs?.content,
            extraTags = tags ?: config?.xhs?.tags,
            gameId = gameId
        )
    }

    fun notifySuccess() {
        ShareResult.notifySuccess(requestId, platform)
    }

    fun notifyCancel() {
        ShareResult.notifyCancel(requestId, platform)
    }

    fun notifyFail(code: Int, msg: String?, sdkCode1: Int = 0, sdkCode2: Int = 0) {
        ShareResult.notifyFail(
            requestId,
            platform,
            code,
            msg,
            sdkCode1 = sdkCode1,
            sdkCode2 = sdkCode2
        )
    }

//    fun toDirectShareData4Test() = DirectShareData(
//        requestId,
//        platform,
//        mode,
//        images = images,
//        videos = videos,
//        sticker = sticker,
//        trackParams = null,
//        needCustomInfo = null,
//        customInfoTId = null,
//        customInfoTKey = null,
//        customInfoTitle = extraTitle,
//        customInfoDesc = extraContent,
//        customInfoTags = extraTags
//
//    )

    fun toThirdShareParams(): ThirdShareParams {
        var path: String? = null
        var paths: List<String>? = null
        when (mode) {
            ShareHelper.MODE_SINGLE_IMAGE -> {
                path = images?.firstOrNull()
            }

            ShareHelper.MODE_SINGLE_VIDEO -> {
                path = videos?.firstOrNull()
            }

            ShareHelper.MODE_MULTI_IMAGES -> {
                paths = images
            }
        }
        return ThirdShareParams(
            requestId,
            platform,
            mode,
            path,
            paths,
            sticker,
            gameId,
            extraTitle,
            extraContent,
            extraTags,
            from
        )
    }

    @Parcelize
    data class Sticker(
        val icon: String = "",
        val title: String = "",
        val desc: String = "",
        val url: String = "",
    ) : Parcelable
}

data class DirectSharePlatforms(
    val singleImage: ArrayList<String> = arrayListOf(
        SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH,
        SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
        SharePlatform.PLATFORM_DOUYIN_PUBLISH,
        SharePlatform.PLATFORM_WECHAT_MOMENT,
        SharePlatform.PLATFORM_QQ_ZONE,
        SharePlatform.PLATFORM_KUAISHOU_FRIEND,
        SharePlatform.PLATFORM_DOUYIN_FRIEND,
        SharePlatform.PLATFORM_WECHAT_FRIEND,
        SharePlatform.PLATFORM_QQ_FRIEND,
    ),
    val multiImages: ArrayList<String> = arrayListOf(
        SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH,
        SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
        SharePlatform.PLATFORM_DOUYIN_PUBLISH,
        SharePlatform.PLATFORM_QQ_ZONE,
        SharePlatform.PLATFORM_KUAISHOU_FRIEND,
        SharePlatform.PLATFORM_DOUYIN_FRIEND,
        SharePlatform.PLATFORM_WECHAT_FRIEND,
        SharePlatform.PLATFORM_QQ_FRIEND,
    ),
    val singleVideo: ArrayList<String> = arrayListOf(
        SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
        SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH,
        SharePlatform.PLATFORM_DOUYIN_PUBLISH,
        SharePlatform.PLATFORM_WECHAT_MOMENT,
        SharePlatform.PLATFORM_QQ_ZONE,
        SharePlatform.PLATFORM_KUAISHOU_FRIEND,
        SharePlatform.PLATFORM_DOUYIN_FRIEND,
        SharePlatform.PLATFORM_WECHAT_FRIEND,
        SharePlatform.PLATFORM_QQ_FRIEND,
    ),
    val singleWebCard: List<String> = listOf(
        SharePlatform.PLATFORM_WECHAT_MOMENT,
        SharePlatform.PLATFORM_QQ_ZONE,
        SharePlatform.PLATFORM_KUAISHOU_FRIEND,
        SharePlatform.PLATFORM_DOUYIN_FRIEND,
        SharePlatform.PLATFORM_WECHAT_FRIEND,
        SharePlatform.PLATFORM_QQ_FRIEND,
    )
)

data class DirectShareData(
    val requestId: String,
    val platform: String,
    val mode: String,
    val images: List<String>? = null,
    val videos: List<String>? = null,
    val webCard: ShareData.Sticker? = null,
    val trackParams: Map<String, String>? = null,
    val needCustomInfo: Boolean? = null,
    val customInfoTId: Int? = null,
    val customInfoTKey: String? = null,
    val customInfoTitle: String? = null,
    val customInfoDesc: String? = null,
    val customInfoTags: List<String>? = null
) {

    fun toShareData(
        extraTitle: String? = customInfoTitle,
        extraContent: String? = customInfoDesc,
        extraTags: List<String>? = customInfoTags,
        sticker: ShareData.Sticker? = webCard
    ) = ShareData(
        requestId,
        platform,
        mode,
        images,
        videos,
        sticker,
        extraTitle = extraTitle,
        extraContent = extraContent,
        extraTags = extraTags
    )

    suspend fun toShareDataWithConfig(context: Context): ShareData {
        if (needCustomInfo == false) {
            return toShareData()
        }
        return when (platform) {
            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                if (customInfoTitle != null && customInfoTags != null) {
                    toShareData(
                        extraTitle = customInfoTitle,
                        extraTags = customInfoTags.toArrayList()
                    )
                } else {
                    toShareDataWithConfigHelper {
                        toShareData(
                            extraTitle = it.dy?.title,
                            extraTags = it.dy?.tags
                        )
                    }
                }
            }

            SharePlatform.PLATFORM_KUAISHOU_PUBLISH -> {
                if (customInfoTags != null) {
                    toShareData(extraTags = customInfoTags.toArrayList())
                } else {
                    toShareDataWithConfigHelper {
                        toShareData(extraTags = it.ks?.tags)
                    }
                }
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                if (customInfoTitle != null && customInfoDesc != null && customInfoTags != null) {
                    toShareData(
                        extraTitle = customInfoTitle,
                        extraContent = customInfoDesc,
                        extraTags = customInfoTags.toArrayList()
                    )
                } else {
                    toShareDataWithConfigHelper {
                        toShareData(
                            extraTitle = customInfoTitle ?: it.xhs?.title,
                            extraContent = customInfoDesc ?: it.xhs?.content,
                            extraTags = (customInfoTags ?: it.xhs?.tags)?.toArrayList()
                        )
                    }
                }
            }

            else -> {
                toShareData()
            }
        }
    }

    private suspend fun toShareDataWithConfigHelper(block: ShareConfigCallback): ShareData {
        val shareConfig = GlobalContext.get()
            .get<TTaiInteractor>()
            .getShareConfig(
                customInfoTId ?: TTaiKV.ID_KEY_ID_GAME_SHARE_CONFIG,
                customInfoTKey ?: MWBizBridge.currentGameId()
            ).single()
        return block(shareConfig)
    }

    fun validate(context: Context): Int {
        when (platform) {
            SharePlatform.PLATFORM_WECHAT_FRIEND -> {
                if (!com.meta.share.util.InstallUtil.isWeChatInstalled(context)) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_WEB_CARD -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            SharePlatform.PLATFORM_WECHAT_MOMENT -> {
                if (!com.meta.share.util.InstallUtil.isWeChatInstalled(context)) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_WEB_CARD -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            SharePlatform.PLATFORM_QQ_FRIEND -> {
                if (!com.meta.share.util.InstallUtil.isQQFamiliesInstalled(context).first) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_WEB_CARD -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            SharePlatform.PLATFORM_QQ_ZONE -> {
                if (!com.meta.share.util.InstallUtil.isQQFamiliesInstalled(context).first) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_WEB_CARD -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                if (!InstallUtil.isInstalledDouyinFamilies4ShareSdk(context).first) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            SharePlatform.PLATFORM_DOUYIN_FRIEND -> {
                if (!InstallUtil.isInstalledDouyinFamilies4ShareSdk(context).first) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_WEB_CARD -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            SharePlatform.PLATFORM_KUAISHOU_PUBLISH -> {
                if (!InstallUtil.isInstalledKuaishouFamilies(context).first) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            SharePlatform.PLATFORM_KUAISHOU_FRIEND -> {
                if (!InstallUtil.isInstalledKuaishouFamilies(context).first) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_WEB_CARD -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                if (!InstallUtil.isInstalledXhs(context)) {
                    return ShareHelper.CODE_NOT_INSTALLED
                }
                when (mode) {
                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (images.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        if (videos.isNullOrEmpty()) {
                            return ShareHelper.CODE_INVALID_PARAMS
                        }
                    }

                    else -> {
                        return ShareHelper.CODE_UNSUPPORTED_MODE
                    }
                }
            }

            else -> {
                return ShareHelper.CODE_UNSUPPORTED_PLATFORM
            }
        }
        return ShareHelper.CODE_OK
    }
}