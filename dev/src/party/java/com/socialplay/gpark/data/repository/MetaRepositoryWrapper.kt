package com.socialplay.gpark.data.repository

import androidx.paging.ExperimentalPagingApi
import com.meta.box.data.model.realname.RealNameCheckResult
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.api.MetaApiWrapper
import com.socialplay.gpark.data.api.MetaDTokenApi
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.DataSource
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.DeviceInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.local.AppDatabase
import com.socialplay.gpark.data.local.SimpleDiskLruCache
import com.socialplay.gpark.data.mapper.MetaMapper
import com.socialplay.gpark.data.model.AdAnalyticQueryBody
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.Recharge
import com.socialplay.gpark.data.model.UpdateInfo
import com.socialplay.gpark.data.model.pay.PayChannelList
import com.socialplay.gpark.data.model.pay.PayResultEntity
import com.socialplay.gpark.data.model.pay.TakeOrderInfo
import com.socialplay.gpark.data.model.pay.TakeOrderParams
import com.socialplay.gpark.data.model.pay.UserBalance
import com.socialplay.gpark.data.model.pay.mobile.MobilePointsParam
import com.socialplay.gpark.data.model.realname.RealNameAutoInfo
import com.socialplay.gpark.data.model.realname.RealNameConfig
import com.socialplay.gpark.data.model.realname.RealNameSurplusGameTime
import com.socialplay.gpark.data.model.user.AuthInfoApiResult
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.OAuthResponse
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

@OptIn(ExperimentalPagingApi::class)
class MetaRepositoryWrapper(
    private val metaApi: MetaApiWrapper,
    private val dtokenMetaApi: MetaDTokenApi,
    private val metaKV: MetaKV,
    private val db: AppDatabase,
    private val cache: SimpleDiskLruCache,
    private val deviceInteractor: DeviceInteractor,
    private val metaMapper: MetaMapper
) : MetaRepository(metaApi, dtokenMetaApi, metaKV, db, cache, deviceInteractor, metaMapper),
    IMetaRepositoryWrapper {
    private var authRepository = AuthRepository(metaApi)
    private var userRepository =
        UserRepositoryWrapper(metaApi, dtokenMetaApi, metaKV, authRepository, db.shareRecordDao)
    private var payRepository: PayRepository = PayRepository(metaApi)
    private var userPrivilegeRepository: UserPrivilegeRepository = UserPrivilegeRepository(metaApi)

    // 游客登录
    override suspend fun postGuestLogin(): Flow<DataResult<AuthInfoApiResult>> =
        userRepository.postGuestLogin()


    // qq登录
    override suspend fun loginByQQ(
        response: OAuthResponse,
        onlyLogin: Boolean,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> =
        userRepository.loginByQQ(response, onlyLogin, loginType)

    // 微信登录
    override suspend fun loginByWX(
        response: OAuthResponse,
        onlyLogin: Boolean,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> =
        userRepository.loginByWX(response, onlyLogin, loginType)

    // 抖音登录
    override suspend fun loginByDouYin(
        response: OAuthResponse,
        onlyLogin: Boolean,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> =
        userRepository.loginByDouYin(response, onlyLogin, loginType)

    // 快手登录
    override suspend fun loginByKwai(
        response: OAuthResponse,
        onlyLogin: Boolean,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> =
        userRepository.loginByKwai(response, onlyLogin, loginType)

    // 233乐园登录
    override suspend fun loginByLeYuan(
        response: OAuthResponse,
        onlyLogin: Boolean,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> =
        userRepository.loginByLeYuan(response, onlyLogin, loginType)

    // 手机号登录
    override suspend fun loginByPhone(
        response: OAuthResponse,
        onlyLogin: Boolean,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> =
        userRepository.loginByPhone(response, onlyLogin, loginType)

    // 获取验证码
    override suspend fun getLoginPhoneCode(phoneNumber: String): Flow<DataResult<Boolean>> =
        userRepository.getLoginPhoneCode(phoneNumber)

    // 获取忘记密码的短信验证码
    override suspend fun getForgetPwdPhoneCode(phoneNumber: String): Flow<DataResult<Boolean>> =
        userRepository.getForgetPwdPhoneCode(phoneNumber)

    // 根据类型获取短信验证码
    override suspend fun getPhoneSmsCode(phoneNumber: String, sendType: String): Flow<DataResult<Boolean>> =
        userRepository.getPhoneSmsCode(phoneNumber, sendType)

    // 校验验证码
    override suspend fun verifyPhoneCode(phoneNumber: String, phoneCode: String, sendType: String): Flow<DataResult<Boolean>> =
        userRepository.verifyPhoneCode(phoneNumber, phoneCode, sendType)

    override suspend fun loginByOneKey(response: OAuthResponse, onlyLogin: Boolean, loginType: LoginType): Flow<LoginState<MetaUserInfo>> =
        userRepository.loginByOneKey(response, onlyLogin, loginType)

    override suspend fun accountLogin(account: String, password: String, loginType: LoginType): Flow<LoginState<MetaUserInfo>> =
        userRepository.accountLogin(account, password, loginType)

    override fun updateCustomToken(customToken: String?) =
        userRepository.updateCustomToken(customToken)

    override fun fetchCustomToken() = userRepository.fetchCustomToken()
    override fun getRecharges(): Flow<DataResult<ArrayList<Recharge>>> =
        payRepository.getRecharges()

    override suspend fun partyMWPay(hashMap: Map<String, Any?>): Flow<DataResult<*>> = userPrivilegeRepository.partyMWPay(hashMap)

    /**
     * Party: 获取支持的支付渠道
     */
    override suspend fun getPayChannels(
        selfPkgName: String,
        scenceCode: String,
        gamePkgName: String,
        gameId: String
    ): Flow<DataResult<PayChannelList>> =
        payRepository.getPayChannels(selfPkgName, scenceCode, gamePkgName, gameId)

    override fun privilegePlaceOrder(
        payChannel: Int,
        takeOrderInfo: TakeOrderInfo
    ): Flow<DataResult<PayResultEntity>> =
        userPrivilegeRepository.privilegePlaceOrder(payChannel, takeOrderInfo)

    override fun cancelOrder(orderId: String): Flow<DataResult<Boolean>> =
        payRepository.cancelOrder(orderId)

    override suspend fun rechargingLoopParty(orderId: String): DataResult<Boolean> =
        payRepository.rechargingLoopParty(orderId)

    override suspend fun takeOrderV3(takeOrderParams: TakeOrderParams, payChannel: Int, mobilePointsParam: MobilePointsParam?): Flow<DataResult<PayResultEntity?>> {
        return payRepository.takeOrderV3(takeOrderParams, payChannel, mobilePointsParam)
    }

    override fun passwordReset(email: String, code: String, newPassword: String): Flow<DataResult<Boolean>> {
        return userRepository.passwordReset(email, code, newPassword)
    }

    override suspend fun realNameCheck(name: String, cardNo: String): DataResult<RealNameCheckResult> {
        return userRepository.realNameCheck(name, cardNo)
    }

    override suspend fun realNameSave(
        name: String,
        cardNo: String,
        pkgName: String,
        gameId: String
    ): DataResult<Any> {
        return userRepository.realNameSave(name, cardNo, pkgName, gameId)
    }

    override suspend fun realNameClear(name: String, cardNo: String): Flow<DataResult<Any>> =
        userRepository.realNameClear(name, cardNo)


    override suspend fun realNameDetail(): DataResult<RealNameAutoInfo> {
        return userRepository.realNameDetail()
    }

    override suspend fun getRealNameSurplusGameTimeV3(
        gameId: Long,
        gamePkg: String
    ): DataResult<RealNameSurplusGameTime> {
        return userRepository.getRealNameSurplusGameTimeV3(gameId, gamePkg)
    }

    override suspend fun realNameConfig(gameId: String?): Flow<DataResult<RealNameConfig>> =
        userRepository.realNameConfig(gameId)

    // 应用升级
    override fun getUpdateInfo(): Flow<DataResult<UpdateInfo>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.getUpdateInfo(BuildConfig.VERSION_CODE) })
    }

    /**
     * 广告埋点上报
     */
    override fun reportAdAnalytic(body: AdAnalyticQueryBody): Flow<DataResult<Boolean>> = flow {
        emit(DataSource.getDataResultForApi { metaApi.reportAdAnalytic(body) })
    }

    override suspend fun gparkIdLogin(
        gparkId: String,
        password: String,
        loginType: LoginType
    ): Flow<LoginState<MetaUserInfo>> = userRepository.gparkIdLogin(gparkId, password, loginType)
}