package com.socialplay.gpark.data.model.realname

import com.socialplay.gpark.data.model.PartyEventConstants
import com.socialplay.gpark.function.analytics.Analytics
import kotlin.collections.set
import kotlin.text.isNullOrEmpty
import kotlin.to

object PartyAnalytics {

    fun trackRealNameDialogShow() {
        Analytics.track(PartyEventConstants.EVENT_REALNAME_SHOW)
    }

    fun trackRealNameDialogCloseClick() {
        Analytics.track(PartyEventConstants.EVENT_REALNAME_CLOSE_CLICK)
    }

    fun trackRealNameResult(success: Boolean, msg: String? = null) {
        val map = mutableMapOf<String, Any>(
            "result" to if (success) "success" else "failed"
        )
        if (!msg.isNullOrEmpty()) {
            map["toast"] = msg
        }
        Analytics.track(PartyEventConstants.EVENT_REALNAME_RESULT, map)
    }

    fun trackRealNameInterceptShow(age: Int, from: String) {
        Analytics.track(PartyEventConstants.EVENT_REALNAME_INTERCEPT, "from" to from, "age" to "$age")
    }

    fun trackUpdateDialogShow(force: Boolean, targetVersion: String) {
        val map = mutableMapOf<String, Any>(
            "renew_type" to getUpdateType(force), "targetVersionCode" to targetVersion
        )
        Analytics.track(PartyEventConstants.EVENT_RENEW_SHOW, map)
    }

    fun trackUpdateRenewClick(force: Boolean) {
        Analytics.track(PartyEventConstants.EVENT_RENEW_CLICK, "renew_type" to getUpdateType(force))
    }

    fun trackUpdateCloseClick(force: Boolean) {
        Analytics.track(PartyEventConstants.EVENT_RENEW_CLOSE, "renew_type" to getUpdateType(force))
    }

    private fun getUpdateType(force: Boolean) = if (force) "force" else "no_force"
}