package com.socialplay.gpark.data.model.realname

/**
 * @param verifyStatus 0未实名 1已实名
 * @param cardType 0-身份证 1护照 2海外护照 3其他
 * @param oversea 是否海外用户
 * @param cardNo 身份证号
 */
data class RealNameSaveResult(val code: Int, val message: String)

data class RealNameAutoInfo(
    var uuid: String? = null,
    var packageName: String? = null,
    var verifyStatus: Int? = null,
    var oversea: Boolean? = null,
    var cardType: Int? = null,
    var cardNo: String? = null,
    var age: Int? = null,
    var birthday: String? = null,
    var realName: String? = null,
    var allowModify: Boolean? = null,
    var client: Int = -1
) {

    companion object {

        const val RESULT_CODE_SUCCESS = 200
        const val RESULT_CODE_NET_DISABLED = 1
        const val RESULT_CODE_URL_NONSTANDARD = 2
        const val RESULT_CODE_REQUEST_FAILED = 4
        const val RESULT_CODE_PARAMS_BLANK = 5
        const val RESULT_CODE_RESULT_BLANK = 6
        const val RESULT_CODE_INTERFACE_NOT_IMPL = 7
        const val RESULT_CODE_PARSE_FAILED = 8
        const val RESULT_CODE_BUSINESS_FAILED = 9
        const val RESULT_CODE_CANCEL = 10
        const val RESULT_CODE_CHECK = 11
    }
}
