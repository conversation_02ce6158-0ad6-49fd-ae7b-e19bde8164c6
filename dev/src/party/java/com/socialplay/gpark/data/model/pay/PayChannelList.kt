package com.socialplay.gpark.data.model.pay

import android.content.Context
import com.socialplay.gpark.R
import com.socialplay.gpark.util.InstallUtil

data class PayChannelList(
    val helpPay: <PERSON><PERSON><PERSON>,
    var channelList: ArrayList<Int>?,
    var tips: String?,
    val channelShowList: ArrayList<ChannelShow?>?
){
    /**
     * 支付方式状态设置
     */
    companion object {
        fun getPayWayBean(
            context: Context,
            type: Int,
            channelShowList: ArrayList<ChannelShow?>?
        ): PayChannelInfo? {
            val payChannelBean = PayChannelInfo()
            payChannelBean.payChannel = type
            payChannelBean.channelShow = channelShowList?.find { it?.channel == type }
            when (type) {
                AgentPayType.PAY_TYPE_WX       -> {
                    val wxInstall = InstallUtil.isInstalledWX(context)
                    payChannelBean.wayName = context.getString(R.string.iap_wechat_pay)
                    payChannelBean.wayIcon = R.drawable.icon_wechat_pay
                    payChannelBean.isInstall = wxInstall
                    return payChannelBean
                }

                AgentPayType.PAY_TYPE_ALI      -> {
                    val aliInstall = InstallUtil.isInstalledAliPay(context)
                    payChannelBean.wayName = context.getString(R.string.iap_alipay)
                    payChannelBean.wayIcon = R.drawable.icon_alipay
                    payChannelBean.isInstall = aliInstall
                    return payChannelBean
                }

                AgentPayType.PAY_TYPE_SIMULATE -> {
                    //模拟支付
                    payChannelBean.wayName = context.getString(R.string.iap_simulate_payment)
                    payChannelBean.wayIcon = R.drawable.pay_channel_similute
                    payChannelBean.isInstall = true
                    return payChannelBean
                }

                else                           -> {
                    return null
                }
            }
        }
    }

}

data class ChannelShow(
    val channel: Int,
    val content: String?,
    val backColor: String?
)
