package com.socialplay.gpark.data.model.pay

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/05/31
 *     desc   : 旧版本支付参数类型，wiki：https://wiki.appshahe.com/pages/viewpage.action?pageId=107549456
 *
 */
object AgentPayType {

    //【1:支付宝,2:微信支付,4:QQ钱包支付,8:平台代币支付,16模拟支付,32乐币支付,69移动积分】
    const val PAY_TYPE_ALI = 1
    const val PAY_TYPE_WX = 2
    const val PAY_TYPE_QQ = 4
    const val PAY_TYPE_TOKEN = 8
    const val PAY_TYPE_SIMULATE = 16
    const val PAY_TYPE_LECOIN = 32
    //帮付
    const val PAY_TYPE_HELP_PAY = 3

    // 派对币支付
    const val PAY_TYPE_PARTY_COINS = 68
    //移动积分
    const val PAY_TYPE_MOBILE_POINTS = 69

    //【1:APP应用支付,2:扫码支付,4:h5支付,8:JSAPI支付方式,16:PC支付】
    const val CHANEL_PAY = 1


    //【100: 联运业务编码,101:标识mgs业务编码,150:自有平台,160:内购兑换】
    //scenceCode标识mgs业务编码
    const val PAY_INTERMODAL_SENCECODE = "100"
    const val PAY_MGS_SENCECODE = "101"

    //自有平台的scenceCode
    const val PAY_OWN_SENCECODE = "150"

    //内购兑换 scenceCode
    const val PAY_INTERNAL_PURCHASE_SENCECODE = "160"
    // 乐币充值业务场景 170
    const val PAY_LECOIN_SENCECODE = "170"
    //派对支付
    const val PAY_PARTY_SCENECODE = "172"

    //旧版本【2:支付宝,0微信支付,6:QQ钱包支付,7:平台代币支付】
    const val PAY_TYPE_WE_CHAT_V1 = 0
    const val PAY_TYPE_ALI_V1 = 2
    const val PAY_TYPE_QQ_V1 = 6
    const val PAY_TYPE_TOKEN_V1 = 7

    /**
     * 未实名
     * 后端：FORCE_RECHARGE_REALNAME_EXCEPTION(22300, "根据国家有关部门规定，您无法进行充值操作，若要继续进行充值操作，需要进行实名认证。")
     */
    const val REAL_NAME_CODE = 22300

    /**
     * 已登录，已实名，单笔充值达到限制
     * 后端：SINGLE_RECHARGE_LIMIT_EXCEPTION(22301, "单笔充值金额已高于%s元上限,请重新选择")
     */
    const val REASON_RECHARGE_SINGLE_LIMIT = 22301

    /**
     * 已登录，已实名，本月充值达到上限
     * TOTAL_RECHARGE_LIMIT_EXCEPTION(22302, "本月充值金额已达到%s元上限,请理性消费")
     */
    const val REASON_RECHARGE_UPPER_LIMIT = 22302

    /**
     * 已登录，已实名，小于8岁
     * UNDER_AGE_RECHARGE_LIMIT_EXCEPTION(22303, "根据国家有关部门规定，您无法进行充值操作，您还可以继续体验233乐园的其他服务。")
     */
    const val REASON_RECHARGE_NOT_ALLOW = 22303

    /**
     * 家长模式限制充值金额（日充值总额）
     * PATRIARCH_MODE_RECHARGE_LIMIT_EXCEPTION(22304, "由于受到家长守护模式限制，您无法完成本次充值，请联系家长修改充值信息。")
     */
    const val REASON_RECHARGE_PATRIARCH_LIMIT = 22304

    /**
     * 自动退款限制
     * 发起了自动退款后，再进入充值支付功能，在下单接口会返回这个code
     * 肯能涉及的下单接口：/order/v1/create 、/order/v1/self/create、v1/compat/takeorder
     */
    const val REASON_RECHARGE_AUTO_REFUND_LIMIT = 11008

    /**
     * 充值警告,用户不能充值
     * 肯能涉及的下单接口：/order/v1/create 、/order/v1/self/create、v1/compat/takeorder
     */
    const val REASON_RECHARGE_WARNING = 12000

    /**
     * 充值告警带申诉
     */
    const val REASON_RECHARGE_WARNING_APPEAL = 12003

    /**
     * 充值提示,用户需要再次实名认证
     * 肯能涉及的下单接口：/order/v1/create 、/order/v1/self/create、v1/compat/takeorder
     */
    const val REASON_RECHARGE_TIPS = 12001

    // 未登录的状态码
    const val RECHARGE_LOGIN_CODE = 233233233
  //给web端定义的支付结果状态码
    //支付成功
    const val THIRD_PAY_SUCCESS = 0

    //支付失败
    const val THIRD_PAY_FAILED = -1

    //支付取消
    const val THIRD_PAY_CANCEL = -2
    //第三方付支付失败
    const val THIRD_PAY_FAIL = -4
    //支付方式异常
    const val THIRD_PAY_CHANNEL_FAILED = -5
    //自定义错误码
    val errorCodeList =
        arrayOf(THIRD_PAY_FAILED, THIRD_PAY_CANCEL, THIRD_PAY_FAIL, THIRD_PAY_CHANNEL_FAILED)

    //联运SDK来源标识
    const val SOURCE_MPG_PAY_SDK = "MPG_PAY_SDK"
    // mgs sdk来源标识
    const val SOURCE_MGS_SDK = "MGS_SDK"
    //云游戏来源标识
    const val SOURCE_CLOUD_MPG_PAY_SDK = "CLOUD_MPG_PAY_SDK"
    // 充值节点，父节点id
    const val ROOT_ID = "0"
}