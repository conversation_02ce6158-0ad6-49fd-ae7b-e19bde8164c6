package com.socialplay.gpark.data.model.pay

import com.socialplay.gpark.data.model.pay.mobile.MobilePointsParam
import timber.log.Timber
import kotlin.math.ceil

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/05/31
 *     desc   : 支付
 *
 */
data class PayParamsParty(
    //支付版本
    var agentPayVersion: AgentPayVersion = AgentPayVersion.VERSION_V1,

    //支付渠道
    var payChannelList: PayChannelList? = null,

    //下单后的订单信息
    var orderCode: String? = null,
    var payResult: PayResultEntity? = null,

    /**
     * 游戏包名
     */
    var gamePackageName: String? = null,
    var gameId: String? = null,

    /**
     * 价格ID(内购游戏使用)
     */
    var pid: String? = null,

    /**
     * 支付的价格（商品原本的价格）单位: 分
     */
    var pPrice: Int = 0,

    /**
     * 优惠价格(代金券的价格或者需要优惠的价格)
     */
    var preferentialPrice: Float = 0.0f,

    /**
     * 注意:只是旧版本sdk使用的支付付的类型（微信，支付宝）
     */
    var payType: Int = 0,

    /**
     * 商品名称(联运游戏使用)
     */
    var pName: String? = null,

    /**
     * 商品唯一标识(联运游戏使用)
     */
    var pCode: String? = null,

    /**
     * 商品个数(联运游戏使用)
     */
    var pCount: Int = 0,

    /**
     * 代金券ID(优惠代金券的ID,没有则为空)
     */
    var baseCouponId: String? = null,
    /**
     * 实例化的代金券Id,后端处理的
     */
    var voucherId: String? = null,
    /**
     * 乐币与现金的转化比例
     */
    var LeCoinRate: Int = 0,

    //移动积分兑换比例1:240
    var mobilePointRate: Int = 240,


    /**
     * 支付的参数（发起支付时，调用接口获取订单，需要给接口传输的参数）
     */
    var map: Map<String, String>? = null,

    // ========为了少改的代码 mgs支付和新版本联运参数======
    var cpOrderId: String? = null,
    var cpExtra: String? = null,
    var appkey: String? = null,
    //sdk的版本号
    var sdkVersion: String? = null,
    //来源,用来区分mgs还是联运
    var source: String? = null,

    //支付方式
    var payChannel: Int = 0,

    //上次支付方式
    var previousPayChannel: String? = null,

    //会员通用支付参数
    var memberCommonMemberParams: CommonMemberParams? = null,

    //移动积分参数
    var mobilePointsParams: MobilePointsParam? = null,
//    //移动积分的手机号码
//    var mobilePhone: String? = null,
    // 推荐的reqId
    var reqId: String? = null,
    var extraBuyInfo: ExtraBuyInfo? = null,
    var leCoinGradeItem: LeCoinGrade? = null,
    //会员支付有关参数
    var memberParams: MemberParams? = null,
    //会员支付版本
    var memberVersion: MemberVersion = MemberVersion.VERSION_V1,
    /**
     * 页面来源
     */
    var pageSource: String? = null,
) {
    fun getRealPrice(): Int {
        Timber.d("实际需要支付 %s", ceil(getAllPrice()).toInt())
        return ceil(getAllPrice()).toInt()
    }

    private fun getAllPrice(): Float {
        val extraBuyPrice = extraBuyInfo?.getExtraBuyRealPrice() ?: 0
        val leCoinPrice = leCoinGradeItem?.price ?: 0
        var productPrice = pPrice - preferentialPrice
        if (productPrice < 0) {
            productPrice = 0f
        }
        return if (leCoinGradeItem == null) {
            (productPrice + extraBuyPrice)
        } else {
            leCoinPrice.toFloat()
        }
    }

    fun getSceneCode(): String {
        return if (agentPayVersion == AgentPayVersion.VERSION_OWN) {
            when (memberVersion) {
                MemberVersion.VERSION_V1 -> {
                    memberParams?.sceneCode ?: ""
                }

                MemberVersion.VERSION_V2 -> {
                    memberCommonMemberParams?.sceneCode ?: ""
                }
            }
        } else {
            if (source == AgentPayType.SOURCE_MPG_PAY_SDK) AgentPayType.PAY_INTERMODAL_SENCECODE else AgentPayType.PAY_MGS_SENCECODE
        }
    }

    fun getExtendJson(): String? {
        return memberCommonMemberParams?.extendJson
    }
}
