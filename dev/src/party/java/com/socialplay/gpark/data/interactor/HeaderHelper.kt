package com.socialplay.gpark.data.interactor

import com.socialplay.gpark.di.CommonHeader
import com.socialplay.gpark.di.CommonParamsProvider

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/02/18
 *     desc   :
 * </pre>
 */
object HeaderHelper {

    fun addExtraCommonHeaders(
        ch: CommonHeader,
        commonParams: CommonParamsProvider,
        shouldAddAuthHeaders: <PERSON><PERSON><PERSON>
    ) {
        ch.addHeader("channelId", commonParams.channelId)
        ch.addHeader("apkChannelId", commonParams.apkChannelId)
    }
}