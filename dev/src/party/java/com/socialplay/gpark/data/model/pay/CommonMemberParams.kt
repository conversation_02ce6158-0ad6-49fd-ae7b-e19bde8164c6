package com.socialplay.gpark.data.model.pay

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2022/03/03
 *     desc   : 通用会员参数
 *
 */
data class CommonMemberParams(
    //功能id
    //支付类型,会员"0",大会员"1",乐币充值"",新大会员"2", 钻石大会员"diamond_members"
    var fun_id: String? = null,
    //实际支付价格，单位：分
    var price: Int? = 0,
    //商品原价,单位：分
    var productPrice: Int? = 0,
    //会员档位，跟着服务器返回参数返回
    var grade: String? = null,
    //来源（游戏福利展示：game、我也页面：mine、悬浮球：ball）
    var source: String? = null,
    //支付方式（支付宝：1、微信：2）
    var pay_type: Int? = null,
    //游戏包名
    var param_str1: String? = null,
    var param_str2: String? = null,
    var param_str3: String? = null,
    //游戏id
    var param_long1: String? = null,
    var param_long2: String? = null,
    //商品订单号码
    var productCode: String,
    //商品名字
    var productName: String,
    //类型
    var sceneCode: String? = AgentPayType.PAY_OWN_SENCECODE,
    //赠送乐币数量
    var happyCoin: String? = null,
    var cpExtra: String? = null,
    var reason: String? = null,
    var extendJson :String? = null,
    //平台立减token(乐币充值)
    var platformReduceToken: String? = null,
    //乐币充值赠送权益
    var promotionToken: String? = null
) : BasePayParams()
