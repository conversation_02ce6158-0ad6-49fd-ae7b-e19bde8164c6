package com.socialplay.gpark.app.initialize

import android.content.Context
import com.socialplay.gpark.BuildConfig
import com.xingin.xhssharesdk.callback.XhsShareRegisterCallback
import com.xingin.xhssharesdk.core.XhsShareSdk
import com.xingin.xhssharesdk.model.config.XhsShareGlobalConfig


object XhsInit {
    fun init(context: Context) {
        XhsShareSdk.registerApp(
            context,
            BuildConfig.XHS_KEY,
            XhsShareGlobalConfig()
                .setEnableLog(true)
                .setClearCacheWhenShareComplete(true)
                .setFileProviderAuthority(BuildConfig.APPLICATION_ID + ".fileprovider"),
            object : XhsShareRegisterCallback {
                override fun onSuccess() {
                }

                override fun onError(
                    errorCode: Int,
                    errorMessage: String?,
                    exception: Exception?
                ) {
                }
            }
        )
    }
}