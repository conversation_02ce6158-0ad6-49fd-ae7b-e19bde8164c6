package com.socialplay.gpark.util

import android.content.Context
import com.socialplay.gpark.R
import com.socialplay.gpark.ui.permission.PermissionRequest

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/02/08
 *     desc   :
 * </pre>
 */
object PermissionUtil {

    fun diverse(context: Context, scene: Int, builder: PermissionRequest.Builder) = builder.apply {
        when (scene) {
            PermissionRequest.SCENE_SCREENSHOT -> {
                val title = context.getString(R.string.permission_dialog_storage)
                val content = context.getString(R.string.permission_desc_screenshot)
                des(content)
                title(title)
                content(content)
                forceEnableRationaleDialog()
            }

            PermissionRequest.SCENE_QR_CODE -> {
                val title = context.getString(R.string.permission_dialog_camera)
                val content = context.getString(R.string.permission_desc_qr_code)
                des(content)
                title(title)
                content(content)
                forceEnableRationaleDialog()
            }

            PermissionRequest.SCENE_ALBUM -> {
                val title = context.getString(R.string.permission_dialog_storage)
                val content = context.getString(R.string.permission_desc_album)
                des(content)
                title(title)
                content(content)
                forceEnableRationaleDialog()
            }

            PermissionRequest.SCENE_SAVE_IMAGE_VIDEO -> {
                val title = context.getString(R.string.permission_dialog_storage)
                val content = context.getString(R.string.permission_desc_save_image_video)
                des(content)
                title(title)
                content(content)
                forceEnableRationaleDialog()
            }

            PermissionRequest.SCENE_RECORD_AUDIO -> {
                val title = context.getString(R.string.permission_dialog_record_audio)
                val content = context.getString(R.string.permission_desc_record_audio)
                des(content)
                title(title)
                content(content)
                forceEnableRationaleDialog()
            }

            PermissionRequest.SCENE_RECORD_VIDEO -> {
                val title = context.getString(R.string.permission_dialog_storage)
                val content = context.getString(R.string.permission_desc_record_video)
                des(content)
                title(title)
                content(content)
                forceEnableRationaleDialog()
            }
        }
    }

    fun needPermission4PublishPost(context: Context): Boolean {
        return !PermissionRequest.hasMediaPermission(context)
    }
}