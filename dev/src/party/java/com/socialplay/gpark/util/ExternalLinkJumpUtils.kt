package com.socialplay.gpark.util

import android.content.Context
import android.content.Intent
import android.net.Uri
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import timber.log.Timber
import java.util.regex.Matcher
import java.util.regex.Pattern


object ExternalLinkJumpUtils {

    private const val TYPE_KS = "1"
    private const val TYPE_DY = "2"
    private const val TYPE_XHS = "3"
    private const val TYPE_BBL = "5"
    private const val TYPE_WEIBO = "6"
    private const val KS_SCHEMA = "kwai://webview?url=%s"
    private const val DY_SCHEMA = "snssdk1128://webview?url=%s&from=webview&refer=web"
    private const val DY_LITE_SCHEMA = "snssdk2329://webview?url=%s&from=webview&refer=web"
    private const val DY_LIVE_SCHEMA = "snssdk1112://webview?url=%s&from=webview&refer=web"
    private const val XHS_SCHEMA = "xhsdiscover://user/%s"
    private const val BBL_SCHEMA = "bilibili://browser?url=%s"
    private const val SINA_WB_SCHEMA = "sinaweibo://userinfo?uid=%s"
    private const val REGEX_FIND_USER_ID_XHS = "user/profile/([^?]+)"
    private const val REGEX_FIND_USER_ID_WB = "[^/]+\$"

    fun jumpExternalLink(context: Context, profileLinkInfo: ProfileLinkInfo) {
        Timber.d("jumpExternalLink:${profileLinkInfo.type} url:${profileLinkInfo.url}")
        if (profileLinkInfo.url.isNullOrEmpty()) {
            return
        }
        val jumpUrl = when (profileLinkInfo.type) {
            TYPE_KS -> {
                if (InstallUtil.isInstalledKuaiShou(context)) {
                    String.format(KS_SCHEMA, profileLinkInfo.url)
                } else {
                    profileLinkInfo.url
                }
            }

            TYPE_DY -> {
                if (InstallUtil.isInstalledDouYin(context)) {
                    String.format(DY_SCHEMA, profileLinkInfo.url)
                } else if (InstallUtil.isInstalledDouYinLite(context)) {
                    String.format(DY_LITE_SCHEMA, profileLinkInfo.url)
                } else if (InstallUtil.isInstalledDouYinLive(context)) {
                    String.format(DY_LIVE_SCHEMA, profileLinkInfo.url)
                } else {
                    profileLinkInfo.url
                }
            }

            TYPE_XHS -> {
                if (InstallUtil.isInstalledXhs(context)) {
                    val userId = findXhsUserId(profileLinkInfo.url)
                    if (userId.isNullOrEmpty()) {
                        profileLinkInfo.url
                    } else {
                        Timber.d("xhs userId:$userId")
                        String.format(XHS_SCHEMA, userId)
                    }
                } else {
                    profileLinkInfo.url
                }
            }

            TYPE_BBL -> {
                if (InstallUtil.isInstalledBiliBili(context)) {
                    String.format(BBL_SCHEMA, profileLinkInfo.url)
                } else {
                    profileLinkInfo.url
                }
            }

            TYPE_WEIBO -> {
                if (InstallUtil.isInstallSinaWeibo(context)) {
                    val userId = findSinaWeiboUserId(profileLinkInfo.url)
                    if (userId.isNullOrEmpty()) {
                        profileLinkInfo.url
                    } else {
                        Timber.d("weibo userId:$userId")
                        String.format(SINA_WB_SCHEMA, userId)
                    }
                } else {
                    profileLinkInfo.url
                }
            }

            else -> {
                profileLinkInfo.url
            }
        }
        kotlin.runCatching {
            context.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(jumpUrl)))
        }.getOrElse {
            Timber.d("failed back default jump")
            context.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(profileLinkInfo.url)))
        }
    }

    private fun findXhsUserId(url: String): String? {
        val matcher: Matcher = Pattern.compile(REGEX_FIND_USER_ID_XHS).matcher(url)
        return if (matcher.find()) {
            return matcher.group(1)
        } else {
            null
        }
    }

    private fun findSinaWeiboUserId(url: String): String? {
        val matcher: Matcher = Pattern.compile(REGEX_FIND_USER_ID_WB).matcher(url)
        return if (matcher.find()) {
            return matcher.group()
        } else {
            null
        }
    }
}