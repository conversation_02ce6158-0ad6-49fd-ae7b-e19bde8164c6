package com.socialplay.gpark.util

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import androidx.core.content.FileProvider
import com.socialplay.gpark.data.model.GameDetailInfo
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import java.io.File

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/02/14
 *     desc   :
 * </pre>
 */
object PackageUtilWrapper {

    /**
     * 安装app
     */
    fun installApp(
        context: Context,
        file: File,
        info: GameDetailInfo? = null,
        resIdBean: ResIdBean? = null,
        isUpdate: Boolean = false
    ): Boolean {
        if (file.extension != "apk") {
            return false
        }

        // 打开开关情况下, 交由OutsideFloatingManager.onEvent(OutsideInstallingEvent)处理
        val intent = Intent(Intent.ACTION_VIEW)
        val uriForFile = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            Uri.fromFile(file)
        } else {
            val authority = context.applicationContext.packageName + ".fileprovider"
            FileProvider.getUriForFile(context, authority, file)
        }
        intent.setDataAndType(uriForFile, "application/vnd.android.package-archive")
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
        if (info != null && resIdBean != null) {
            val map = hashMapOf<String, Any>("pkgName" to info.packageName)
            map.putAll(ResIdUtils.getAnalyticsMap(resIdBean))
            map["apk_size"] = info.fileSize
            map["internal_free_size"] = StorageUtils.getInternalMemoryFreeSize()
            map["download_categoryid"] = resIdBean.getCategoryID()
            map["gameid"] = info.id

//            AdReportAnalytics.sendAnalyticsAndAdReport(
//                EventConstants.OUTSIDE_DOWNLOAD_INSTALL,
//                map,
//                info.packageName,
//                resIdBean
//            )
        }
        return true
    }
}