package com.socialplay.gpark.util.extension

import android.text.TextUtils
import com.meta.biz.ugc.model.RechargeArkMsg
import com.socialplay.gpark.data.model.ProductListEntity
import com.socialplay.gpark.util.GsonUtil

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/29
 * desc   :
 * </pre>
 */

fun RechargeArkMsg.getCommodityId(): String? {
    return kotlin.runCatching {
        val list =
            GsonUtil.gsonSafeParseCollection<List<ProductListEntity>>(rawData["productList"].toString())
                ?: return null
        TextUtils.join(",", list.map { it.commodityId })
    }.getOrNull()
}

fun RechargeArkMsg.getCommodityName(): String? {
    return kotlin.runCatching {
        val list =
            GsonUtil.gsonSafeParseCollection<List<ProductListEntity>>(rawData["productList"].toString())
                ?: return null
        TextUtils.join(",", list.map { it.commodityName })
    }.getOrNull()
}

