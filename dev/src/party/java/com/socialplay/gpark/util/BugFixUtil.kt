package com.socialplay.gpark.util


import android.common.HwFrameworkFactory
import android.common.IHwApiCacheManagerEx
import java.lang.reflect.InvocationHandler
import java.lang.reflect.Method
import java.lang.reflect.Proxy

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/05/25
 * desc   : 修复兼容性问题的工具类
 * </pre>
 */


object BugFixUtil {

    /**
     * 修复华为手机上部分游戏奔溃 朱未
     */
    fun fixEmuiCrash() {
        if (!DeviceUtil.isEmui()) {
            return
        }
        kotlin.runCatching {
            // 豁免所有的反射黑名单限制，让我们可以通过反射访问黑名单中的API
            // Reflection.exemptAll()
            //1、关闭华为缓存
            val activityThreadClass = Reflection.getActivityThreadClass()
            val useCacheField = activityThreadClass.getDeclaredField("USE_CACHE")
            useCacheField.isAccessible = true
            useCacheField.set(null, false)

            HwFrameworkFactory.getHwApiCacheManagerEx().disableCache()

            //https://apm.umeng.com/platform/60a7700053b6726499093f66/error_analysis/crash/detail/4288407076102?errorId=4288407076102&startDay=20210602%20000000&endDay=20210602%20235959&version=&errorAbstract=&errorType=crash&crashType=JAVA&status=&orderBy=happenTimes&timeUnit=today&dataSourceId=60a7700053b6726499093f66&carrier=&customField=&device=&os=&puid=ai6ec57605dbf5a215d4d7e10953b5b30b&areaType=&channel=&area=[]&dateType=today
            fixIHwApiCacheManagerExException()
        }
    }

    /*代理华为系统类，防止调用某些方法时报错*/
    private fun fixIHwApiCacheManagerExException() {
        getHwApiCacheManagerEx()?.let { target ->
            Proxy.newProxyInstance(target.javaClass.classLoader, target.javaClass.interfaces, object : InvocationHandler {
                override fun invoke(proxy: Any?, method: Method?, args: Array<out Any>?): Any? {
                    when (method?.name) {
                        "getApplicationInfoAsUser" -> {
                            return kotlin.runCatching {
                                method.invoke(target, args)
                            }.getOrNull()
                        }
                    }
                    return method?.invoke(target, args)
                }
            })
        }
    }

    /*获取华为系统服务第一个不为空的实例，获取不到则返回null*/
    private fun getHwApiCacheManagerEx(): IHwApiCacheManagerEx? {
        return kotlin.runCatching {
            val array = arrayOf(
                HwFrameworkFactory.getHwApiCacheManagerEx(), Reflection.invokeStaticMethod(
                    "huawei.android.common.HwFrameworkFactoryImpl", "getHwApiCacheManagerEx"
                ) as IHwApiCacheManagerEx?, Reflection.invokeStaticMethod(
                    "huawei.android.app.HwApiCacheMangerEx", "getDefault"
                ) as IHwApiCacheManagerEx?
            )
            array.filterNotNull().firstOrNull()
        }.getOrNull()
    }

}