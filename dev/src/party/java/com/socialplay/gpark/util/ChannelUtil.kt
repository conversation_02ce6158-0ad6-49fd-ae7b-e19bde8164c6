package com.socialplay.gpark.util

import android.content.Context
import com.bytedance.hume.readapk.HumeSDK
import com.kwai.monitor.payload.TurboHelper
import com.meituan.android.walle.ChannelInfo
import com.meituan.android.walle.WalleChannelReader
import com.socialplay.gpark.data.interactor.DeviceInteractor
import com.socialplay.gpark.data.kv.MetaKV
import org.koin.core.context.GlobalContext
import timber.log.Timber

object ChannelUtil {
    const val DEFAULT_CHANNEL = "default"
    const val DEFAULT_META_TRACKING = "all"
    private const val META_TRACKING_KEY = "tracking"

    /**
     * Meta Tracking
     * 类型暂时有三种：
     *  all:默认值，开启三种激活上报
     *  td:TalkingData
     *  ry:热云
     *  meta:233乐园自有
     */
    private val metaKV: MetaKV = GlobalContext.get().get()
    private var channelInfo: ChannelInfo? = null


    fun getApkChannelId(context: Context): String {
        return getChannelInfo(context)?.channel ?: DEFAULT_CHANNEL
    }

    fun getChannelId(): String {
        return GlobalContext.get().get<DeviceInteractor>().channelId
    }

    /**
     * 获取Meta处理激活相关逻辑的标记
     */
    fun getMetaTracking(context: Context): String {
        return getApkConfig(context, META_TRACKING_KEY, "")
    }

    /**
     * 获取渠道内超级推荐位id
     */
    fun getSuperGameId(): Long {
        if (metaKV.appKV.superGameId == -1L) {
            metaKV.appKV.superGameId = kotlin.runCatching { getChannelInfo(GlobalContext.get().get())?.extraInfo?.get("super_game_id")?.toLong() ?: 0L }.getOrElse { 0L }
        }
        return metaKV.appKV.superGameId
    }

    // 抖音
    const val CHANNEL_DY = "dyjuliangty1"

    // 快手
    const val CHANNEL_KS = "kszhubaoty1"

    fun getSubChannelId(context: Context, apkChannelId: String?): String? {
        return when (apkChannelId) {
            CHANNEL_DY -> HumeSDK.getChannel(context)
            CHANNEL_KS -> TurboHelper.getChannel(context)
            else -> null
        }
    }

    private fun getApkConfig(context: Context, key: String, defaultValue: String): String {
        val channelInfo = getChannelInfo(context) ?: return defaultValue
        return channelInfo.extraInfo?.get(key) ?: defaultValue
    }


    private fun getChannelInfo(context: Context): ChannelInfo? {
        if (this.channelInfo != null) {
            return channelInfo
        }
        val channelInfo = WalleChannelReader.getChannelInfo(context)
        if (channelInfo != null) {
            Timber.d("ChannelUtil getChannelInfo save cache")
            this.channelInfo = channelInfo
        }
        return channelInfo
    }

    fun getChannelInfoStr(context: Context): String {
        return getChannelInfo(context)?.let { "${it.channel} ${it.extraInfo}" } ?: ""
    }

    fun getApkSetting(context: Context, key: String, defaultValue: String): String {
        val channelInfo = getChannelInfo(context) ?: return defaultValue
        return channelInfo.extraInfo?.get(key) ?: defaultValue
    }


    fun isTsGameChannel(): Boolean {
        return getChannelId().contains("_bhxt002_")
    }
}