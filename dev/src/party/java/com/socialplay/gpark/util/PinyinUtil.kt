package com.socialplay.gpark.util

import android.text.TextUtils
import com.github.promeg.pinyinhelper.Pinyin
import timber.log.Timber
import java.util.Locale
import java.util.regex.Pattern

/**
 * Created by bo.li
 * Date: 2021/1/24
 */
object PinyinUtil {
    private const val DEFAULT_GROUP_NAME = "#"

    private val LETTER_REGEX = Pattern.compile("^[a-zA-Z]+$")
    private val CHINESE_REGEX = Pattern.compile("^[\u4e00-\u9fa5]+$")

    /**
     * 获取字符串对应的groupName
     * 规则：
     * 1、字母直接转换成大写字母
     * 2、中文转换成拼音并返回拼音首个字符对应的大写字母
     * 3、其他返回#
     */
    fun getLetter(name: String): String {
        var result = DEFAULT_GROUP_NAME
        if (!TextUtils.isEmpty(name)) {
            val firstNameLetter = name[0].toString()
            if (isLetter(firstNameLetter)) {
                result = firstNameLetter.toUpperCase(Locale.ROOT)
            } else if (isChinese(firstNameLetter)) {
                result = Pinyin.toPinyin(firstNameLetter[0])[0].toString().toUpperCase(Locale.ROOT)
            }
        }
        return result
    }

    /**
     * 判断是否是字母
     */
    private fun isLetter(str: String): Boolean {
        if (TextUtils.isEmpty(str)) {
            return false
        }
        return LETTER_REGEX.matcher(str).matches()
    }

    /**
     * 判断是否是中文
     */
    private fun isChinese(str: String): Boolean {
        if (TextUtils.isEmpty(str)) {
            return false
        }

        return CHINESE_REGEX.matcher(str).matches()
    }


    fun getFriendListSortName(str: String, default: Char): Char {
        try {
            if (!TextUtils.isEmpty(str)) {
                val firstChar = str[0]

                if (firstChar in 'A'..'Z') {
                    return firstChar
                }

                if (firstChar in 'a'..'z') {
                    return firstChar.toUpperCase()
                }

                if (firstChar in '0'..'9') {
                    return firstChar
                }

                return Pinyin.toPinyin(firstChar)[0]
            } else {
                return default
            }
        } catch (e: Exception) {
            Timber.e(e, "comm_friend getPinyin")
        }

        return default
    }

    /**
     * 获取字符串对应的拼音字符串，分两个步骤
     * 一、先对每个字符进行处理，规则：
     * 1、中文字符，返回对应的大写的拼音
     * 2、字母，转换成大写字母
     * 3、其他，不作处理
     * 二、判断字符串的首个字符是否是字母或汉字，如果是，不做处理，否则在第一步转换后的结果前面添加
     * 一个Unicode值比字母大的任意一个字符（保证compare排序的时候位置在字母后面）
     */
    fun getPinyin(str: String, default: String): String {
        val resultSb = StringBuilder()
        try {
            if (!TextUtils.isEmpty(str)) {
                for (item in str.trim { it <= ' ' }.toCharArray()) {
                    val itemStr = item.toString()
                    when {
                        isChinese(itemStr) -> {
                            resultSb.append(Pinyin.toPinyin(item).toUpperCase(Locale.ROOT))
                        }

                        isLetter(itemStr) -> {
                            resultSb.append(item.toString().toUpperCase(Locale.ROOT))
                        }

                        else -> {
                            resultSb.append(item)
                        }
                    }
                }
            }
            if (DEFAULT_GROUP_NAME == getLetter(str)) {
                return default
            }
            return resultSb.toString()
        } catch (e: Exception) {
            Timber.e(e, "comm_friend getPinyin")
        }

        return default
    }

    /**
     * 获取字符串的首字母, 返回结果与 str 的长度一致
     */
    fun getShortPinyin(str: String): String {
        val resultSb = StringBuilder()
        try {
            if (!TextUtils.isEmpty(str)) {
                for (item in str.toCharArray()) {
                    val itemStr = item.toString()
                    when {
                        isChinese(itemStr) -> {
                            val pinyin = Pinyin.toPinyin(item)
                            if (pinyin.isNullOrEmpty()) {
                                resultSb.append(item)
                            } else {
                                resultSb.append(pinyin[0].lowercase())
                            }
                        }

                        isLetter(itemStr) -> {
                            resultSb.append(item.toString().lowercase())
                        }

                        else -> {
                            resultSb.append(item)
                        }
                    }
                }
            }
            return resultSb.toString()
        } catch (e: Exception) {
            Timber.e(e, "getShortPinyin failed")
        }
        return str
    }

    fun getPinyinList(str: String): List<String> {
        if (str.isEmpty()) {
            return emptyList()
        }
        try {
            val result = mutableListOf<String>()
            for (item in str.toCharArray()) {
                val itemStr = item.toString()
                when {
                    isChinese(itemStr) -> {
                        result.add(Pinyin.toPinyin(item).lowercase())
                    }

                    isLetter(itemStr) -> {
                        result.add(item.toString().lowercase())
                    }

                    else -> {
                        result.add(itemStr)
                    }
                }
            }
            return result
        } catch (e: Exception) {
            Timber.e(e, "getPinyinList failed")
        }

        return emptyList()
    }
}