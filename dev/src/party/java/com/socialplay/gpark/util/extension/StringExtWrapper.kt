package com.socialplay.gpark.util.extension

import com.socialplay.gpark.util.PinyinUtil
import com.socialplay.gpark.util.WebUtil

fun String?.friendListIndexChar(default: Char): Char {
    return if (this.isNullOrEmpty() || this.isBlank()) {
        default
    } else {
        PinyinUtil.getFriendListSortName(this, default)
    }
}

fun String?.pinyin(default: String): String {
    return if (this.isNullOrEmpty() || this.isBlank()) {
        default
    } else {
        PinyinUtil.getPinyin(this, default)
    }
}

fun String.pinyinShort(): String {
    return PinyinUtil.getShortPinyin(this)
}

fun String.pinyinList(): List<String> {
    return PinyinUtil.getPinyinList(this)
}

fun String?.is233Scheme(): Boolean {
    if (!WebUtil.isScheme(this)) return false
    return this?.startsWith("metaapp://233party") == true || this?.startsWith("233party://233party") == true
}
