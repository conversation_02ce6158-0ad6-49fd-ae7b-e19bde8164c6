package com.socialplay.gpark.ui.gamepay.platform

import com.socialplay.gpark.data.model.pay.AgentPayType
import com.socialplay.gpark.data.model.pay.PayParamsParty
import com.socialplay.gpark.data.model.pay.PayResultEntity
import com.socialplay.gpark.ui.gamepay.PayController
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/06/02
 *     desc   : 模拟支付
 *
 */
class PartySimulationPlatform : BasePayPlatformParty<PayParamsParty>() {
    override fun platformType(): Int = AgentPayType.PAY_TYPE_SIMULATE

    override fun startPay(payResultEntity: PayResultEntity) {
        Timber.d("PaySimulation succeeded")
        PayController.setIsThirdPaying(true)
        onStartThirdPay()
    }
}