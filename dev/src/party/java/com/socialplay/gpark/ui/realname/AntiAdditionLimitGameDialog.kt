package com.socialplay.gpark.ui.realname

import android.app.Activity
import android.app.Application
import android.app.Dialog
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.Window
import android.view.WindowManager
import androidx.core.content.ContextCompat
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogAntiAdditionLimitGameBinding
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setSize

/**
 * @description 实名认证弹窗/编辑实名认证
 * <AUTHOR>
 * @date 2021/9/22 15:24
 */
class AntiAdditionLimitGameDialog(
    val limit: TimeLimitChild,
    activity: Activity,
    private val metaApp: Application,
    private val exit: () -> Unit = {}
) : Dialog(activity, R.style.GameDialogStyle) {


    private lateinit var binding: DialogAntiAdditionLimitGameBinding


    init {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        initView()
        setOnDismissListener {
            exit()
        }
    }

    private fun initDialogAttribute() {
        window?.apply {
            requestFeature(Window.FEATURE_NO_TITLE) //设置无标题栏
            val layoutParams = attributes
            // Android 28及以上
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // 允许窗口布局延伸到了刘海区域
                layoutParams.layoutInDisplayCutoutMode =
                    WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }
            setBackgroundDrawable(
                ContextCompat.getDrawable(
                    metaApp, R.color.transparent
                )
            ) //设置window背景透明
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.dimAmount = 0.75f
            layoutParams.gravity = Gravity.CENTER
            attributes = layoutParams
        } ?: dismiss()
    }


    private fun initView() {
        initDialogAttribute()
        binding = DialogAntiAdditionLimitGameBinding.inflate(LayoutInflater.from(metaApp))
        window?.setContentView(binding.root)
        binding.llContent.setSize(
            (ScreenUtil.getScreenWidth(metaApp) * 0.8).toInt()
                .coerceAtMost(ScreenUtil.dp2px(metaApp, 285f)),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
        binding.title.text = metaApp.getString(R.string.already_real_name)
        binding.content.text = limit.limitMessage
        binding.ivClose.setOnAntiViolenceClickListener {
            dismiss()
        }
        binding.btnRight.setOnAntiViolenceClickListener {
            dismiss()
        }
    }


}