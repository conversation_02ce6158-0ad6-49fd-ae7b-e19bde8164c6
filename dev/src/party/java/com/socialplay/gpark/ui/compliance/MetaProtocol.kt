package com.socialplay.gpark.ui.compliance

import androidx.fragment.app.FragmentActivity
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import org.koin.core.context.GlobalContext

/**
 * @author: ning.wang
 * @date: 2021-05-31 11:14 上午
 * @desc: 合规弹窗入口
 */
object MetaProtocol {

    // 是否已经同意用户协议
    var agreedProtocol = false
    private val metaKV by lazy { GlobalContext.get().get<MetaKV>() }

    /**
     * 检查是否需要展示合规弹窗
     *
     * @return
     */
    fun needLegal(): Boolean {
        return BuildConfig.IS_NEED_LEGAL && !metaKV.protocol.getProtocolAgree()
    }

    /**
     * 显示合规协议&权限
     */
    fun showProtocolDialog(activity: FragmentActivity, callback: () -> Unit) {
        // 合规协议
        PartyProtocolDialogFragment.show(activity.supportFragmentManager, {
            // 协议同意
            Analytics.track(EventConstants.AGREE_YHXY_CLICK_ONE)
            callback()
        }, {
            // 协议不同意，显示合规协议二次确认弹窗
            Analytics.track(EventConstants.DISAGREE_YHXY_CLICK_ONE)
            activity.finish()
            // 为了保证下次启动数据是正常的，所以退出整个App
            System.exit(0)
//            exitProcess(0)
        })
    }


}