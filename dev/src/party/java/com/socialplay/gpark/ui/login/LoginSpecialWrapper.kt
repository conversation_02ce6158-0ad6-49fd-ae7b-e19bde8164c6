package com.socialplay.gpark.ui.login

import android.text.SpannableStringBuilder
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.meta.pandora.Pandora
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.Source
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.compliance.PartyFitAgeDialogFragment
import com.socialplay.gpark.ui.compliance.PartyProtocolDialogBottomFragment
import com.socialplay.gpark.util.SpanClick
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.getColorByRes

object LoginSpecialWrapper {

    val visitorWay = LoginWay.Tourist.way

    fun getAgeRestrictionIconRes(): Int {
        return R.drawable.icon_age_restriction
    }

    fun showAgeRestrictionDialog(requireActivity: FragmentActivity, fragment: Fragment) {
        PartyFitAgeDialogFragment.show(fragment.childFragmentManager)
    }

    fun showProtocolDialogBottomFragment(activity: FragmentActivity, fragment: Fragment, function: () -> Unit) {
//        ConfirmDialog.Builder(fragment).content(getAgreementStringBuilder(fragment, h5PageConfig))
//            .cancelBtnTxt(resources.getString(R.string.dialog_cancel), lightBackground = false)
//            .confirmBtnTxt(resources.getString(R.string.im_conversation_menu_remove), lightBackground = true)
//            .isRed(true)
//            .confirmCallback {
//                viewModel.removeConversation(metaConversation)
//                viewModel.refreshConversationList()
//                viewModel.getUnReadCount()
//            }.navigate()
        PartyProtocolDialogBottomFragment.show(fragment.parentFragmentManager, nope = {
            Pandora.send(EventConstants.EVENT_CONTRACT_ACCESS) {
                put("state", 1)
            }
        }, agree = {
            function.invoke()
        })
    }

    fun getAgreementStringBuilder(fragment: Fragment, h5PageConfig: H5PageConfigInteractor): SpannableStringBuilder {
//        val color1 = ContextCompat.getColor(fragment.requireContext(), R.color.color_0C75FF)
        val color = fragment.getColorByRes(R.color.secondary_color_1)
        val spannable = SpannableHelper.Builder()
            .text(fragment.getString(R.string.read_and_agree))
            .text(fragment.getString(R.string.user_protocol_with_brackets_no_space))
//            .textAppearance(fragment.requireActivity(), R.style.MetaTextView_S12_PoppinsMedium500)
            .click(SpanClick {
                Analytics.track(EventConstants.EVENT_USER_AGREEMENT_CLICK) {
                    put("source", Source.SOURCE_LOGIN)
                }
                val item = h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.USER_AGREEMENT)
//                    MetaRouter.Web.navigate(this, item)
                jumpWeb(fragment, item.url, fragment.getString(R.string.terms_of_service_text))
            }).color(color)
            .text(fragment.getString(R.string.intl_i_read_agree_and))
            .text(fragment.getString(R.string.privacy_protocol_with_brackets_no_space))
//            .textAppearance(fragment.requireActivity(), R.style.MetaTextView_S12_PoppinsMedium500)
            .click(SpanClick {
                Analytics.track(EventConstants.EVENT_PRIVACY_AGREEMENT_CLICK) {
                    put("source", Source.SOURCE_LOGIN)
                }

                val item = h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.PRIVACY_AGREEMENT)
//                    MetaRouter.Web.navigate(this, item)
                jumpWeb(fragment, item.url, fragment.getString(R.string.privacy_protocol))
            }).color(color)
            .text(fragment.getString(R.string.intl_i_read_agree_and))
            .text(fragment.getString(R.string.children_protocol_with_brackets_no_space))
//            .textAppearance(fragment.requireActivity(), R.style.MetaTextView_S12_PoppinsMedium500)
            .click(SpanClick {
                Analytics.track(EventConstants.EVENT_CHILDREN_PROTOCOL_CLICK) {
                    put("source", Source.SOURCE_LOGIN)
                }

                val item = h5PageConfig.getH5PageConfigItem(H5PageConfigInteractor.CHILDREN_PROTOCOL)
//                    MetaRouter.Web.navigate(this, item)
                jumpWeb(fragment, item.url, fragment.getString(R.string.children_protocol))
            }).color(color)
            .build()
        return spannable
    }


    private fun jumpWeb(fragment: Fragment, url: String, title: String = "") {
        MetaRouter.Web.navigate(
            fragment,
            title = title,
            url = url,
        )
    }

    fun continueLogin(fragment: Fragment, loginWay: LoginWay, source: String, continueInfo: ContinueAccountInfo): Boolean {
        when (loginWay) {
            LoginWay.Phone -> {
                MetaRouterWrapper.Account.loginByPhone(fragment, source, continueInfo.loginKey, true, true)
                return true
            }

            else -> {
                return false
            }
        }
        return false
    }
}