package com.socialplay.gpark.ui.recharge

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.pay.AgentPayType
import com.socialplay.gpark.databinding.FragmentRechargeChannelConfirmBinding
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.property.viewBinding
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/04/01
 *     desc   :
 *
 */
class RechargeChannelConfirmDialogFragment : BaseDialogFragment() {
    val adapter = RechargeChannelAdapter()
    private val viewModel by viewModel<RechargeViewModel>()

    companion object {
        const val KEY_RECHARGE_CHANNEL = "KEY_RECHARGE_CHANNEL"

        fun show(fragment: Fragment) {
            MetaRouter.Control.navigate(
                fragment,
                R.id.rechargeChannelConfirmDialogFragment
            )
        }
    }

    override val binding by viewBinding(FragmentRechargeChannelConfirmBinding::inflate)


    override fun gravity(): Int {
        return Gravity.CENTER
    }

    override fun windowWidth(context: Context): Int {
        return 260.dp
    }

    override fun isBackPressedDismiss() = false
    override fun isClickOutsideDismiss() = false

    override fun init() {
        binding.rvRechargeChannel.adapter = adapter
        viewModel.channelLiveData.observe(viewLifecycleOwner) {
            adapter.setList(it)
        }
        adapter.setOnItemClickListener { view, position ->
            if (!checkPayWayInstalled(adapter.getItem(position)?.payChannel ?: 0)) {
                //未安装的方式拦截
                return@setOnItemClickListener
            }
            dismissWithResult(adapter.getItem(position).payChannel)
        }
        binding.ivClose.setOnClickListener {
            navigateUp()
        }
    }
    /**
     * 检查支付方式是否安装
     * @return 是否安装指定的支付方式
     */
    private fun checkPayWayInstalled(payType: Int): Boolean {
        val metaApp = requireContext()
        when (payType) {
            AgentPayType.PAY_TYPE_WX  -> {
                val installed: Boolean = InstallUtil.isInstalledWX(metaApp)
                if (!installed) {
                    ToastUtil.showLong(R.string.pay_not_install_weixin)
                }
                return installed
            }
            AgentPayType.PAY_TYPE_ALI -> {
                val installed: Boolean = InstallUtil.isInstalledAliPay(metaApp)
                if (!installed) {
                    ToastUtil.showLong( R.string.pay_not_install_alipay)
                }
                return installed
            }
            AgentPayType.PAY_TYPE_QQ  -> {
                val installed: Boolean = InstallUtil.isInstalledQQ(metaApp)
                if (!installed) {
                    ToastUtil.showLong( R.string.pay_not_install_qq)
                }
                return installed
            }
            //其他的类型默认能支付
            else                      -> return true
        }
    }

    override fun loadFirstData() {
        viewModel.getPayChannel(activity?.packageName ?: BuildConfig.APPLICATION_ID)
    }
    private fun dismissWithResult(type: Int) {
        val bundle = Bundle()
        bundle.putInt(KEY_RECHARGE_CHANNEL, type)
        setFragmentResult(KEY_RECHARGE_CHANNEL, bundle)
        navigateUp()
    }
}