package com.socialplay.gpark.ui.task

import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PartyPayInteractor
import com.socialplay.gpark.ui.gamepay.PayController
import kotlinx.coroutines.*
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * author : yi.zhang
 * e-mail : <EMAIL>
 * time   : 2021/06/07
 * desc   :  轮训支付结果
 */
class PayQueryTask {
    val payInteractor by lazy { GlobalContext.get().get<IPayInteractor>() as PartyPayInteractor }
    private val handler: Handler
    // 轮询的时间间隔
    private var interval: Long = 1000L
    // 轮询的总次数
    private var loopTimes: Int = 0
    // 当前已轮询的次数
    private var currentTimes = 0
    //轮训总时长
    private val loopTotalTime = 20 * 60 * 1000L
    private var stopSendMessageFlag: Boolean = false

    //支付结果回调
    private var onQueryCallback: OnQueryCallback? = null
    private var coroutineScope: Job? = null

    /**
     * 查询支付结果
     */
    init {
        handler = Handler(Looper.getMainLooper(), Handler.Callback { msg ->
            if (msg != null && msg.what == MSG_QUERY_PAY_RESULT) {
                val orderId = msg.obj as String
                if (currentTimes < loopTimes && (!stopSendMessageFlag)) {
                    requestApi(orderId)
                    currentTimes++
                }
            }
            false
        })
    }

    fun setOnQueryCallback(onQueryCallback: OnQueryCallback?) {
        this.onQueryCallback = onQueryCallback
    }

    /**
     * @param interval
     * @param loopTotalTime 轮询总时间
     */
    fun startWithTime(orderId: String) {
        // 轮询总时间/轮询间隔=轮询次数
        Timber.d("MGS_MOD_PAY_CODE_PAY startWithTime interval:%s  loopTotalTime:%s", interval, loopTotalTime)
        this.loopTimes = (loopTotalTime / interval).toInt()
        this.stopSendMessageFlag = false
        this.currentTimes = 1
        handler.sendMessageDelayed(handler.obtainMessage(MSG_QUERY_PAY_RESULT, orderId), interval)
    }

    /**
     * @param orderId 订单
     */
    private fun requestApi(orderId: String) {
        Timber.d("MGS_MOD_PAY_CODE_PAY  requestApi"+ PayController.getPay())
        if (TextUtils.isEmpty(orderId) && PayController.getPay()) {
            return
        }
        coroutineScope?.cancel()
        coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO).launch {
            val result = payInteractor.rechargeLoop(orderId)
            withContext(Dispatchers.Main) {
                if (result.succeeded) {
                    if (onQueryCallback != null) {
                        onQueryCallback?.onPaySuccessCallback(orderId)
                    }
                    queryInterrupt()
                } else {
                    loopNext(orderId)
                }
            }
        }
    }

    /**
     * 继续轮询
     */
    private fun loopNext(orderId: String) {
        if (PayController.getPay()) {
            val delayTime = (currentTimes * interval).toLong()
            handler.sendMessageDelayed(handler.obtainMessage(MSG_QUERY_PAY_RESULT, orderId), delayTime)
        } else {
            queryInterrupt()
        }
    }

    private fun queryInterrupt() {
        Timber.d("联运,轮询结果 结束")
        PayController.setIsThirdPaying(false)
        handler.removeCallbacksAndMessages(null)
        this.stopSendMessageFlag = true
        coroutineScope?.cancel()
    }

    fun manualInterruptQuery() {
        Timber.d("联运,轮询结果 手动结束")
        queryInterrupt()
    }

    interface OnQueryCallback {
        fun onPaySuccessCallback(orderId: String)
    }

    companion object {
        private const val MSG_QUERY_PAY_RESULT = 901
        private var codePayQuery: PayQueryTask? = null

        val instance: PayQueryTask
            get() {
                if (codePayQuery == null) {
                    codePayQuery = PayQueryTask()
                }
                return codePayQuery as PayQueryTask
            }
    }


}
