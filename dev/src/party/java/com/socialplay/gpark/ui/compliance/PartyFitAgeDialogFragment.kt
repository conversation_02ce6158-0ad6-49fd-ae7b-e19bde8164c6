package com.socialplay.gpark.ui.compliance

import android.content.Context
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.view.Gravity
import android.view.WindowManager
import androidx.fragment.app.FragmentManager
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.DialogFragmentPartyFitsAgeBinding
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding

class PartyFitAgeDialogFragment : BaseDialogFragment() {
    override val binding by viewBinding(DialogFragmentPartyFitsAgeBinding::inflate)

    override fun init() {
        binding.tvTitle.setText(R.string.appropriate_age)
        binding.ivClose.setOnAntiViolenceClickListener {
            dismiss()
        }
        binding.tvMessage.text = createProtocolContent()
        binding.tvMessage.movementMethod = LinkMovementMethod.getInstance()
        binding.bg.setOnAntiViolenceClickListener {
            dismiss()
        }
    }

    override fun windowHeight() = WindowManager.LayoutParams.MATCH_PARENT
//    override fun windowWidth(context: Context): Int {
//        return 320.dp
//    }

    override fun gravity(): Int {
        return Gravity.CENTER
    }

    override fun loadFirstData() {

    }

    private fun createProtocolContent(): SpannableStringBuilder {
        return SpannableHelper.Builder()
            .text("1${getString(R.string.dunhao)}").text(getString(R.string.party_with_bracket)).bold(true).text(getString(R.string.appropriate_age_desc_1))
            .text("\n\n${getString(R.string.appropriate_age_desc_2)}")
            .text("\n\n${getString(R.string.appropriate_age_desc_3)}")
            .text("\n\n${getString(R.string.appropriate_age_desc_4)}")
            .build()
    }


    companion object {
        const val TAG = "PartyFitAgeDialogFragment"
        fun show(fragmentManager: FragmentManager) {
            PartyFitAgeDialogFragment().show(fragmentManager, TAG)
        }
    }
}