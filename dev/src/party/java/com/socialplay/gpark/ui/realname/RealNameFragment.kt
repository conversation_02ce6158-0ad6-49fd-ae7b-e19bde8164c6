package com.socialplay.gpark.ui.realname

import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.meta.pandora.Pandora
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.model.PartyEventConstants
import com.socialplay.gpark.databinding.FragmentRealNamePartyBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.login.LoginActivity
import com.socialplay.gpark.ui.realname.party.PartyChildDelayLimitTask
import com.socialplay.gpark.util.SpanClick
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.TextWatcherAdapter
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.core.context.GlobalContext


class RealNameFragment : BaseFragment<FragmentRealNamePartyBinding>(R.layout.fragment_real_name_party) {
    private val viewModel: RealNameViewModel by fragmentViewModel()
    private val accountInteractor by inject<AccountInteractor>()
    private val h5PageConfigInteractor: H5PageConfigInteractor by lazy { GlobalContext.get().get() }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentRealNamePartyBinding? {
        return FragmentRealNamePartyBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
        Pandora.send(PartyEventConstants.EVENT_REALNAME_SHOW)
    }

    override fun onResume() {
        super.onResume()
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                Pandora.send(PartyEventConstants.EVENT_REALNAME_CLOSE_CLICK)
                logout()
            }
        })
    }

    private fun initData() {
        viewModel.registerToast(RealNameState::toast)
        viewModel.onEach(RealNameState::isSubmitting, action = binding.loading::with)
        viewModel.onEach(RealNameState::result, action = ::onResult)
    }

    private fun onResult(state: Async<AfterRealNameAction>) {
        if (state !is Success) {
            return
        }
        when (val result = state.invoke()) {
            is AfterRealNameAction.AgeLimited -> {
                checkLimitTime(result.limit)
            }

            AfterRealNameAction.OpenGuide -> {
                routerToGuide()
            }

            AfterRealNameAction.OpenMain -> {
                routerToMain()
            }
        }
    }

    /**
     * 非初次登录，实名后直接去主页
     */
    private fun routerToMain() {
        toast(R.string.login_succeed)
        if (requireActivity() is LoginActivity) {
            //如果是LoginActivity就直接干掉
            requireActivity().finish()
        } else {
            MetaRouter.Main.tabHome(this@RealNameFragment, requireContext())
        }
    }

    /**
     * 初次登录，实名后走角色创建流程（按照目前逻辑来看，需要实名的都是新用户，所以必然会重新创建角色才对-20250210）
     */
    private fun routerToGuide() {
        toast(R.string.login_succeed)
        // 跳转去角色编辑
        MetaRouter.Startup.createAvatar(this, false, false)
    }


    private fun routerToBack() {
        // 上一页
        navigateUp()
    }

    private fun checkLimitTime(limit: TimeLimitChild) {
        if (limit.timeLeft > 0) {
            // 还有剩余的可用时长，设置延时任务，指定时间后弹框
            PartyChildDelayLimitTask.startDelayLimit(requireActivity(), limit)
//            if (args.needStartLaunchGuide) {
            routerToGuide()
//            } else {
//                routerToMain()
//            }
        } else {
            // 没有可用时长了，弹框提示，继续进入App
            AntiAdditionLimitDialog.show(requireActivity(), limit, "0") {
                routerToGuide()
            }
        }

    }

    private fun initView() {
        binding.etRealName.addTextChangedListener(realNameInputTextWatcher)
        binding.etIdCardNumber.addTextChangedListener(idCardInputTextWatcher)
        binding.ivRealNameDelete.setOnClickListener {
            binding.etRealName.text?.clear()
        }
        binding.ivIdCardDelete.setOnClickListener {
            binding.etIdCardNumber.text?.clear()
        }
        binding.tbl.setOnBackAntiViolenceClickedListener {
            logout()
        }
        binding.btnStartVerification.setOnAntiViolenceClickListener {
            viewModel.submitRealNameInfo(binding.etRealName.text.toString(), binding.etIdCardNumber.text.toString())
        }
        binding.apply {
            tvRealNamePolice.text = createProtocolContent()
            tvRealNamePolice.movementMethod = LinkMovementMethod.getInstance()
        }
    }

    private fun logout() {
        viewLifecycleOwner.lifecycleScope.launch {
            accountInteractor.logout(true).collect()
//            MetaRouter.Login.login(fragment = this@RealNameFragment, loginSource = LoginSource.REAL_NAME)
            routerToBack()
        }
    }

    private val realNameInputTextWatcher = object : TextWatcherAdapter() {
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            val accountLength = binding.etRealName.text?.length ?: 0
            val phoneLength = binding.etIdCardNumber.text?.length ?: 0
            binding.ivRealNameDelete.visible(accountLength > 0)
            binding.btnStartVerification.isEnabled = accountLength > 0 && phoneLength > 0
        }
    }
    private val idCardInputTextWatcher = object : TextWatcherAdapter() {
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            val accountLength = binding.etRealName.text?.length ?: 0
            val phoneLength = binding.etIdCardNumber.text?.length ?: 0
            binding.ivIdCardDelete.visible(phoneLength > 0)
            binding.btnStartVerification.isEnabled = accountLength > 0 && phoneLength > 0
        }
    }

    private fun createProtocolContent(): SpannableStringBuilder {
        return SpannableHelper.Builder()
            .text(getString(R.string.real_name_dialog_notice)).colorRes(R.color.secondary_color_1).click(userSpan1)
            .text(getString(R.string.real_name_what_is_id)).colorRes(R.color.black_60)
            .build()
    }

    private val userSpan1 = SpanClick {
        val h5PageConfigItem = h5PageConfigInteractor.getH5PageConfigItem(H5PageConfigInteractor.REALNAME_NEED_PROTOCOL)
        jumpWeb(h5PageConfigItem.url, h5PageConfigItem.title)
    }

    private fun jumpWeb(url: String, title: String) {
        MetaRouter.Web.navigate(
            this,
            title = title,
            url = url,
        )
    }

    override fun onDestroyView() {
        binding.etRealName.removeTextChangedListener(realNameInputTextWatcher)
        binding.etIdCardNumber.removeTextChangedListener(idCardInputTextWatcher)
        super.onDestroyView()
    }

    override fun invalidate() {}

    override fun getPageName(): String {
        return PageNameConstants.FRAGMENT_NAME_REAL_NAME
    }
}