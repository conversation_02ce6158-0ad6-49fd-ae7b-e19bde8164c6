package com.socialplay.gpark.ui.realname.party

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.Intent
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.LoginStatusEvent
import com.socialplay.gpark.data.model.realname.ChildPlayTimeOutFromGameEvent
import com.socialplay.gpark.ui.realname.AntiAdditionLimitDialog
import com.socialplay.gpark.ui.realname.TimeLimitChild
import com.socialplay.gpark.util.ActivityLifecycleCallbacksAdapter
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.core.context.GlobalContext
import kotlin.system.exitProcess

@SuppressLint("StaticFieldLeak")
object PartyChildDelayLimitTask {

    private var mCurrentActivity: Activity? = null
    private var needShowDialog: Boolean = true
    private var job: Job? = null
    fun startDelayLimit(activity: Activity, limit: TimeLimitChild) {
        CpEventBus.register(this)
        mCurrentActivity = activity
        val application = GlobalContext.get().get<Application>()
        application.unregisterActivityLifecycleCallbacks(activityLifecycleCallback)
        application.registerActivityLifecycleCallbacks(activityLifecycleCallback)
        job?.cancel()
        job = GlobalScope.launch {
            delay(limit.timeLeft)
            showChildLimitDialog(limit)
        }
    }

    private val activityLifecycleCallback = object : ActivityLifecycleCallbacksAdapter() {
        override fun onActivityResumed(activity: Activity) {
            if (activity != mCurrentActivity) {
                mCurrentActivity = activity
            }
        }

        override fun onActivityPaused(activity: Activity) {
        }
    }

    private fun showChildLimitDialog(limit: TimeLimitChild) {
        val activity = mCurrentActivity
        if (activity is FragmentActivity) {
            activity.lifecycleScope.launchWhenResumed {
                if (needShowDialog) {
                    AntiAdditionLimitDialog.show(activity, limit, "0") {
                        activity.lifecycleScope.launch {
                            GlobalContext.get().get<AccountInteractor>().clearUserInfo()
                            exitGame(activity)
                        }
                    }
                } else {
                    exitGame(activity)
                }
            }
        }
    }

    private fun exitGame(activity: Activity) {
        gotoHomeLauncher(activity)
        activity.finishAffinity()
        mCurrentActivity = null
        exitProcess(0)
    }

    private fun gotoHomeLauncher(activity: Activity) {
        val intent = Intent()
        intent.action = Intent.ACTION_MAIN
        intent.addCategory(Intent.CATEGORY_HOME)
        try {
            activity.startActivity(intent)
        } catch (e: Throwable) {
            e.printStackTrace()
            exitProcess(0)
        }
    }

    @Subscribe
    fun onEvent(event: ChildPlayTimeOutFromGameEvent) {
        needShowDialog = false
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(loginStatusEvent: LoginStatusEvent) {
        if (loginStatusEvent == LoginStatusEvent.LOGOUT_SUCCESS) {
            job?.cancel()
        }
    }
}