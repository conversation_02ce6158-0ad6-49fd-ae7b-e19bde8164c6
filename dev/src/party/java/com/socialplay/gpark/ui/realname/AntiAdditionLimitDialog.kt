package com.socialplay.gpark.ui.realname

import androidx.fragment.app.FragmentActivity
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.realname.PartyAnalytics
import com.socialplay.gpark.ui.dialog.ConfirmDialog

object AntiAdditionLimitDialog {
    /**
     * @param source 0：登录完提示  1：游戏内提示
     */
    fun show(activity: FragmentActivity, limit: TimeLimitChild, source: String, callback: () -> Unit) {
        PartyAnalytics.trackRealNameInterceptShow(limit.age, source)  // 0：登录完提示  1：游戏内提示 2: 启动App时提示

        ConfirmDialog.Builder()
            .title(activity.getString(R.string.already_real_name))
            .content(limit.limitMessage)
            .confirmBtnTxt(isBold = true)
            .cancelBtnTxt("", isVisible = false)
            .dismissCallback {
                callback()
            }
            .navigate(activity, "showLimitedDialog")
    }
}