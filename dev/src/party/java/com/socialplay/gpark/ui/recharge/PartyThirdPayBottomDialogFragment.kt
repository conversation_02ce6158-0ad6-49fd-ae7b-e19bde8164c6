package com.socialplay.gpark.ui.recharge

import android.content.DialogInterface
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.setFragmentResultListener
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.pay.AgentPayType
import com.socialplay.gpark.databinding.DialogPartyThirdPayBottomBinding
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseBottomSheetDialogFragment
import com.socialplay.gpark.ui.recharge.RechargeChannelConfirmDialogFragment.Companion.KEY_RECHARGE_CHANNEL
import com.socialplay.gpark.ui.view.InterceptClickEventLinkMovementMethod
import com.socialplay.gpark.util.InstallUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import org.koin.androidx.viewmodel.ext.android.viewModel

class PartyThirdPayBottomDialogFragment : BaseBottomSheetDialogFragment() {
    companion object {
        fun show(
            fragmentManager: FragmentManager,
            rechargeCoinsCount: Long,
            price: Int,
            callback: (Boolean, Int, RechargeViewModel) -> Unit
        ) {
            PartyThirdPayBottomDialogFragment().apply {
                this.mRechargeCoinsCount = rechargeCoinsCount
                this.mPrice = price
                this.callback = callback
            }.show(fragmentManager, "PartyThirdPayBottomDialogFragment")
        }
    }

    override val binding: DialogPartyThirdPayBottomBinding by viewBinding(
        DialogPartyThirdPayBottomBinding::inflate
    )
    private val viewModel by viewModel<RechargeViewModel>()
    private var callback: ((Boolean, Int, RechargeViewModel) -> Unit)? = null
    private var mRechargeCoinsCount: Long = 0L
    private var mPrice: Int = 0
    private val adapter = RechargeChannelAdapter(itemHeight = dp(46))

    override fun isClickOutsideDismiss() = false
    override fun isBackPressedDismiss() = false

    override fun init() {
        skipCollapsed()
        enableDrag(false)
        enableHide(false)
        binding.payRv.adapter = adapter
        viewModel.channelLiveData.observe(viewLifecycleOwner) {
            adapter.setList(it)
            binding.root.visible(true)
        }
        binding.ivClose.setOnAntiViolenceClickListener {
            dismissWithResult(false)
        }
        adapter.setOnItemClickListener { view, position ->
            if (!checkPayWayInstalled(adapter.getItem(position).payChannel ?: 0)) {
                //未安装的方式拦截
                return@setOnItemClickListener
            }
            viewModel.setSelected(position)
        }
        setFragmentResultListener(KEY_RECHARGE_CHANNEL) { _, bundle ->
            val channelType = bundle.getInt(KEY_RECHARGE_CHANNEL)
            dismissWithResult(true, channelType)
        }
        binding.apply {
            binding.tvAmount.text = UnitUtilWrapper.formatCoinCont(mRechargeCoinsCount)
            val formattedPrice = UnitUtil.formattedServerPrice(mPrice)
            tvConfirm.text =
                resources.getString(R.string.iap_third_pay_confirm_text, formattedPrice)
            tvConfirm.setOnAntiViolenceClickListener {
                if (NetUtil.isNetworkAvailable()) {
                    val type = adapter.data.firstOrNull { it.isSelected }?.payChannel
                    if (type == null) {
                        ToastUtil.showShort(R.string.iap_toast_third_pay_select_channel)
                    } else {
                        if (!checkPayWayInstalled(type)) {
                            //未安装的方式拦截
                            return@setOnAntiViolenceClickListener
                        }
                        dismissWithResult(true, type)
                    }
                } else {
                    ToastUtil.showShort(R.string.net_unavailable)
                }
            }

            // 充值协议
            val agreementBuilder = SpannableHelper.Builder()
            val textList = getString(R.string.party_third_pay_recharge_agreement).split("%s")
            if (textList.isNotEmpty() && textList[0].isNotEmpty()) {
                agreementBuilder.text(textList[0])
                    .colorRes(R.color.color_666666)
            }
            agreementBuilder.text(getString(R.string.party_third_pay_recharge_agreement_highlight))
                .colorRes(R.color.color_0083FA)
                .click {
                    MetaRouter.Web.navigate(
                        this@PartyThirdPayBottomDialogFragment,
                        title = null,
                        url = BuildConfig.RECHARGE_PROTOCOL_URL,
                        showTitle = false,
                    )
                }
            if (textList.size > 1 && textList[1].isNotEmpty()) {
                agreementBuilder.text(textList[1])
                    .colorRes(R.color.color_666666)
            }
            tvRechargeAgreement.movementMethod =
                InterceptClickEventLinkMovementMethod(tvRechargeAgreement)
            tvRechargeAgreement.text = agreementBuilder.build()
        }
    }

    private fun checkPayWayInstalled(payType: Int): Boolean {
        val metaApp = requireContext()
        when (payType) {
            AgentPayType.PAY_TYPE_WX -> {
                val installed: Boolean = InstallUtil.isInstalledWX(metaApp)
                if (!installed) {
                    ToastUtil.showShort(R.string.pay_not_install_weixin)
                }
                return installed
            }

            AgentPayType.PAY_TYPE_ALI -> {
                val installed: Boolean = InstallUtil.isInstalledAliPay(metaApp)
                if (!installed) {
                    ToastUtil.showShort(R.string.pay_not_install_alipay)
                }
                return installed
            }

            AgentPayType.PAY_TYPE_QQ -> {
                val installed: Boolean = InstallUtil.isInstalledQQ(metaApp)
                if (!installed) {
                    ToastUtil.showShort(R.string.pay_not_install_qq)
                }
                return installed
            }
            // 其他的类型默认能支付
            else -> return true
        }
    }

    override fun loadFirstData() {
        viewModel.getPayChannel(activity?.packageName ?: BuildConfig.APPLICATION_ID)
    }

    private fun dismissWithResult(isConfirm: Boolean, type: Int? = null) {
        callback?.invoke(isConfirm, type ?: 0, viewModel)
        callback = null
        dismissAllowingStateLoss()
    }

    override fun onDismiss(dialog: DialogInterface) {
        // 窗口意外关闭, 没有回调情况的兜底
        callback?.invoke(false, 0, viewModel)
        callback = null
        super.onDismiss(dialog)
    }

    override fun onBackPressed(): Boolean {
        dismissWithResult(false)
        return super.onBackPressed()
    }

    override fun needCountTime(): Boolean = false

    override fun getPageName(): String = ""
}