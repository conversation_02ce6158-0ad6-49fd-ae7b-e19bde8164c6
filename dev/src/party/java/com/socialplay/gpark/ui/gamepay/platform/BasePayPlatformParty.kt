package com.socialplay.gpark.ui.gamepay.platform

import com.socialplay.gpark.data.model.pay.PayResultEntity
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.function.pay.PartyPayInteractor
import org.koin.core.context.GlobalContext

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/06/02
 *     desc   :
 *
 */
abstract class BasePayPlatformParty<P> {
    val payInteractor by lazy { GlobalContext.get().get<IPayInteractor>() as PartyPayInteractor }

    /**
     * 支付结果回调
     */
    private var payCallback: IPayCallbackParty<P>? = null

    /**
     * 平台支付参数
     */
    private var agentPayParams: P? = null


    /**
     * @return 支付平台类型
     */
    abstract fun platformType(): Int

    /**
     * 开始支付
     *
     * @param payParams 支付参数
     */
    abstract fun startPay(payResultEntity: PayResultEntity)

    /**
     * 设置平台支付参数
     *
     * @param agentPayParams 平台支付参数
     */
    fun setAgentPayParams(agentPayParams: P?) {
        this.agentPayParams = agentPayParams
    }

    /**
     * 设置支付结果回调
     *
     * @param payCallback 结果回调
     */
    fun setPayCallback(payCallback: IPayCallbackParty<P>) {
        this.payCallback = payCallback
    }

    /**
     * @return 获取平台支付参数
     */
    protected open fun getAgentPayParams(): P? {
        return agentPayParams
    }

    /**
     * 支付成功
     */
    protected open fun paySuccess() {
        if (payCallback != null) {
            payCallback?.onPaySuccess(agentPayParams)
        }
    }

    /**
     * 支付失败
     */
    protected open fun payFailed(errorMessage: String?, code: Int) {
        if (payCallback != null) {
            payCallback?.onPayFailed(agentPayParams, errorMessage, code)
        }
    }
    /**
     * 第三方支付
     */
    fun onStartThirdPay(){
        if (payCallback != null) {
            payCallback?.onStartThirdPay(agentPayParams)
        }
    }
}