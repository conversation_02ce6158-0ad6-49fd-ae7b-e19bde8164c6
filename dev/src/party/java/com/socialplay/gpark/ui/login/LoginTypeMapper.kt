package com.socialplay.gpark.ui.login

import com.socialplay.gpark.R
import com.socialplay.gpark.util.getStringByGlobal

/**
 * <pre>
 * author : qijijie
 * e-mail : <EMAIL>
 * time   : 2025/08/21
 * desc   : 通过后端下发的loginType字段来映射本地的登录类型字符串，表格文档：https://meta.feishu.cn/wiki/BvFGw9orriABClkfd8uczMumnCc
 * </pre>
 */
object LoginTypeMapper {
    private const val QQ = "qq"
    private const val WECHAT = "wechat"
    private const val PHONE= "phone"
    private const val APPLE = "apple"
    private const val LEYUAN = "leyuan"
    private const val MULTIP_USERNUMBER= "multip_usernumber"
    private const val MULTIP_PHONE = "multip_phone"
    private const val CREATOR_QR_LOGIN = "creator_qr_login"

    fun getLoginType(loginType: String?): String {
        return when (loginType) {
            QQ -> getStringByGlobal(R.string.account_last_login_qq)
            WECHAT -> getStringByGlobal(R.string.account_last_login_wechat)
            PHONE -> getStringByGlobal(R.string.account_last_login_phone)
            APPLE -> getStringByGlobal(R.string.account_last_login_apple)
            LEYUAN -> getStringByGlobal(R.string.account_last_login_leyuan)
            MULTIP_USERNUMBER -> getStringByGlobal(R.string.account_last_login_userid)
            MULTIP_PHONE -> getStringByGlobal(R.string.account_last_login_phone)
            CREATOR_QR_LOGIN -> getStringByGlobal(R.string.account_last_login_qr)
            else -> getStringByGlobal(R.string.account_last_login_account)
        }
    }
}