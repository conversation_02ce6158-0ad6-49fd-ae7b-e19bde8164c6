package com.socialplay.gpark.ui.gamepay.platform

import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.model.pay.AgentPayType
import com.socialplay.gpark.data.model.pay.PayParamsParty
import com.socialplay.gpark.data.model.pay.PayResultEntity
import com.socialplay.gpark.function.pay.way.GamePayResultEvent
import com.socialplay.gpark.function.pay.way.Wxpay
import com.socialplay.gpark.ui.gamepay.PayController
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/06/02
 *     desc   : 微信支付
 *
 */
class WetChatPayPlatform : BasePayPlatformParty<PayParamsParty>(), IThirdPayCallBack<GamePayResultEvent> {
    override fun platformType(): Int = AgentPayType.PAY_TYPE_WX


    override fun startPay(payResultEntity: PayResultEntity) {
        payInteractor.setGamePayResultCallBack(this)
        PayController.setIsThirdPaying(true)
        onStartThirdPay()
        val bean = payResultEntity.wxPayInfo
        Wxpay.startPay(
            PayController.getCurrentActivity(),
            payResultEntity.merchantId,
            bean?.prepayId,
            bean?.nonceStr,
            bean?.timestamp,
            bean?.sign,
            payResultEntity.orderCode
        )
    }

    override fun onPayResult(gamePayResult: GamePayResultEvent) {
        if (getAgentPayParams()?.orderCode == gamePayResult.payOrderId) {
            Timber.d("微信支付结果:%s",gamePayResult.payStatus)
            setAgentPayParams(null)
            payInteractor.setGamePayResultCallBack(null)
            if (!PayController.getIsThirdPaying()) {
                return
            }
            PayController.setIsThirdPaying(false)
            when (gamePayResult.payStatus) {
                0    -> {
                    //支付成功
                    paySuccess()
                }
                -2   -> {
                    val errorMessage = LifecycleInteractor.activityRef?.get()?.getString(R.string.pay_fail_wechat_pay_cancel)?:""
                    payFailed(errorMessage, AgentPayType.THIRD_PAY_CANCEL)
                }
                else -> {
                    val errorMessage = LifecycleInteractor.activityRef?.get()?.getString(R.string.pay_fail_wechat_pay_failed)?:""
                    payFailed(errorMessage, AgentPayType.THIRD_PAY_FAILED)
                }
            }

        }
    }
}