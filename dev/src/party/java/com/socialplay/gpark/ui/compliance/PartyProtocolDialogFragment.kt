package com.socialplay.gpark.ui.compliance

import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.Gravity
import android.view.View
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.PRIVACY_AGREEMENT
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.USER_AGREEMENT
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.CHILDREN_PROTOCOL
import com.socialplay.gpark.databinding.PartyDialogProtocolFragmentBinding
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.SpanClick
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import org.koin.android.ext.android.inject

/**
 * @author: ning.wang
 * @date: 2021-05-30 9:17 下午
 * @desc: 合规协议弹窗
 */
open class PartyProtocolDialogFragment : BaseDialogFragment() {

    companion object {
        fun show(fm: FragmentManager, agree: () -> Unit, nope: () -> Unit) {
            PartyProtocolDialogFragment().apply {
                this.agree = agree
                this.nope = nope
            }.show(
                fm,
                "ProtocolDialogFragment"
            )
        }
    }

    override val binding by viewBinding(PartyDialogProtocolFragmentBinding::inflate)

    // 同意callback
    var agree: (() -> Unit)? = null

    // 不同意callback
    var nope: (() -> Unit)? = null
    val h5PageConfigInteractor by inject<H5PageConfigInteractor>()

    override fun init() {
        isCancelable = false
        binding.apply {
            tvContent.text = createProtocolContent()
            tvContent.movementMethod = LinkMovementMethod.getInstance()
            tvNope.setOnAntiViolenceClickListener {
                nope?.invoke()
                dismissAllowingStateLoss()
            }
            tvAgree.setOnAntiViolenceClickListener {
                agree?.invoke()
                dismissAllowingStateLoss()
            }
            title.setTextColor(Color.BLACK)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        dismissAllowingStateLoss()
        agree = null
        nope = null
    }

    private val userSpan1 = SpanClick {
        val h5PageConfigItem = h5PageConfigInteractor.getH5PageConfigItem(USER_AGREEMENT)
        jumpWeb(h5PageConfigItem.url, h5PageConfigItem.title)
    }
    private val userSpan2 = SpanClick {
        val h5PageConfigItem = h5PageConfigInteractor.getH5PageConfigItem(USER_AGREEMENT)
        jumpWeb(h5PageConfigItem.url, h5PageConfigItem.title)
    }

    private val personalSpan1 = SpanClick {
        val h5PageConfigItem = h5PageConfigInteractor.getH5PageConfigItem(PRIVACY_AGREEMENT)
        jumpWeb(h5PageConfigItem.url, h5PageConfigItem.title)
    }
    private val personalSpan2 = SpanClick {
        val h5PageConfigItem = h5PageConfigInteractor.getH5PageConfigItem(PRIVACY_AGREEMENT)
        jumpWeb(h5PageConfigItem.url, h5PageConfigItem.title)
    }

    private val childrenSpan1 = SpanClick {
        val h5PageConfigItem = h5PageConfigInteractor.getH5PageConfigItem(CHILDREN_PROTOCOL)
        jumpWeb(h5PageConfigItem.url, h5PageConfigItem.title)
    }
    private val childrenSpan2 = SpanClick {
        val h5PageConfigItem = h5PageConfigInteractor.getH5PageConfigItem(CHILDREN_PROTOCOL)
        jumpWeb(h5PageConfigItem.url, h5PageConfigItem.title)
    }

    private fun jumpWeb(url: String, title: String?) {
        MetaRouterWrapper.Web.goProtocolWeb(requireActivity(), url, title ?: "")
    }

    private fun createProtocolContent(): SpannableStringBuilder {
        val color = ContextCompat.getColor(requireContext(), R.color.black)
        val brandName = getString(R.string.app_name)
        return SpannableHelper.Builder()
            .text(getString(R.string.protocol_content_1, brandName))
            .text(getString(R.string.user_protocol_with_brackets_no_space)).bold(true).click(userSpan1).color(color)
            .text(getString(R.string.dunhao))
            .text(getString(R.string.privacy_protocol_with_brackets_no_space)).bold(true).click(personalSpan1).color(color)
            .text(getString(R.string.protocol_content_2))
            .text(getString(R.string.children_protocol_with_brackets_no_space)).bold(true).click(childrenSpan1).color(color)
            .text(getString(R.string.protocol_content_3))
            .text("\n")
            .text("\n1.${getString(R.string.protocol_content_4)}").text(getString(R.string.user_protocol_with_brackets_no_space)).bold(true).click(userSpan2).color(color).text(getString(R.string.protocol_content_5))
            .text("\n2.${getString(R.string.protocol_content_4)}").text(getString(R.string.privacy_protocol_with_brackets_no_space)).bold(true).click(personalSpan2).color(color).text(getString(R.string.protocol_content_6))
            .text("\n3.${getString(R.string.protocol_content_7, brandName)}")
            .text("\n4.${getString(R.string.protocol_content_8)}").text(getString(R.string.children_protocol_with_brackets_no_space)).bold(true).click(childrenSpan2).color(color)
            .text(getString(R.string.protocol_content_9))
            .text("\n")
            .text("\n${getString(R.string.protocol_content_10)}")
            .build()
    }


    /**
     * 富文本初始化
     */
    fun getProtocol(fragment: Fragment, content: String): CharSequence {
        val contentSpan = SpannableStringBuilder(content)
        contentSpan.setSpan(object : ClickableSpan() {
            override fun onClick(widget: View) {
                MetaRouter.Web.navigate(fragment, null, h5PageConfigInteractor.getH5PageUrl(USER_AGREEMENT), false)
            }

            override fun updateDrawState(ds: TextPaint) {
                ds.color = Color.parseColor("#FF5000")
                ds.bgColor = Color.parseColor("#FFFFFF")
            }

        }, content.indexOf("《"), content.indexOf("》") + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

        contentSpan.setSpan(
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    MetaRouter.Web.navigate(fragment, null, h5PageConfigInteractor.getH5PageUrl(PRIVACY_AGREEMENT), false)
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.color = Color.parseColor("#FF5000")
                    ds.bgColor = Color.parseColor("#FFFFFF")
                }

            },
            content.lastIndexOf("《"),
            content.lastIndexOf("》") + 1,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return contentSpan
    }

    override fun gravity(): Int {
        return Gravity.CENTER
    }

    override fun loadFirstData() {
    }
}