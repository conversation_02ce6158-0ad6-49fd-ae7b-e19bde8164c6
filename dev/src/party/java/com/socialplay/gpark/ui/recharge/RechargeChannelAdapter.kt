package com.socialplay.gpark.ui.recharge

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.pay.PayChannelInfo
import com.socialplay.gpark.databinding.ItemRechargeChannelAdapterBinding
import com.socialplay.gpark.ui.base.adapter.BaseAdapter
import com.socialplay.gpark.ui.base.adapter.BindingViewHolder
import com.socialplay.gpark.ui.base.adapter.ItemClickListener

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2024/04/01
 *     desc   :
 *
 */
class RechargeChannelAdapter(val itemHeight: Int? = null) :
    BaseAdapter<PayChannelInfo, ItemRechargeChannelAdapterBinding>() {
    override fun createViewBinding(
        layoutInflater: LayoutInflater,
        parent: ViewGroup,
        viewType: Int
    ): ItemRechargeChannelAdapterBinding {
        return ItemRechargeChannelAdapterBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        ).apply {
            if (itemHeight != null) {
                val layoutParams = root.layoutParams
                layoutParams.height = itemHeight
            }
        }
    }

    override fun convert(
        holder: BindingViewHolder<ItemRechargeChannelAdapterBinding>,
        item: PayChannelInfo,
        position: Int
    ) {
        holder.binding.apply {
            if (item.wayIcon == null) {
                ivChannelIcon.setImageResource(R.drawable.pay_channel_similute)
            } else {
                ivChannelIcon.setImageResource(item.wayIcon!!)
            }
            item.wayIcon?.let { ivChannelIcon.setImageResource(it) }
            tvChannelName.text = item.wayName
            ivSelect.setImageResource(if (item.isSelected) R.drawable.iap_third_pay_checked else R.drawable.iap_third_pay_unchecked)
            holder.itemView.setOnClickListener {
                val position = holder.bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    mItemClickListener?.invoke(it, position)
                }
            }
        }
    }

    private var mItemClickListener: ItemClickListener? = null

    /**
     * 父类的设置点击方法, 有防抖动, 切换不灵敏, 所以这里进行重写
     */
    override fun setOnItemClickListener(listener: ItemClickListener?) {
        this.mItemClickListener = listener
    }
}