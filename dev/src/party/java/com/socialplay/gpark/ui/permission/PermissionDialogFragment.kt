package com.socialplay.gpark.ui.permission

import android.content.DialogInterface
import android.os.Bundle
import android.view.WindowManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.os.bundleOf
import androidx.navigation.fragment.navArgs
import com.socialplay.gpark.databinding.DialogFragmentPermissionBinding
import com.socialplay.gpark.ui.base.BaseDialogFragment
import com.socialplay.gpark.util.extension.setFragmentResultByActivity
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/05/18
 * desc   :
 * </pre>
 */


class PermissionDialogFragment : BaseDialogFragment() {

    companion object {
        const val REQUEST_KEY_PERMISSION = "PermissionDialogFragment_REQUEST_KEY_PERMISSION"
        const val KEY_PERMISSION_RESULT = "KEY_PERMISSION_RESULT"
    }

    private var launcher: ActivityResultLauncher<Array<String>>? = null
    override val binding by viewBinding(DialogFragmentPermissionBinding::inflate)
    private var hadSetResult = false
    private val args by navArgs<PermissionDialogFragmentArgs>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        launcher = registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            val deniedList = result.filter { !it.value }.map { it.key }
            hadSetResult = true
            if (deniedList.isEmpty()) {
                setFragmentResultByActivity(REQUEST_KEY_PERMISSION, bundleOf(KEY_PERMISSION_RESULT to true))
                dismissAllowingStateLoss()
            } else {
                setFragmentResultByActivity(REQUEST_KEY_PERMISSION, bundleOf(KEY_PERMISSION_RESULT to false))
                dismissAllowingStateLoss()
            }
        }
        launcher?.launch(args.permissions)
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        if (!hadSetResult) {
            setFragmentResultByActivity(REQUEST_KEY_PERMISSION, bundleOf(KEY_PERMISSION_RESULT to false))
        }
    }

    override fun isTransparent(): Boolean {
        return true
    }

    override fun windowHeight(): Int {
        return if (!args.des.isNullOrEmpty()) {
            WindowManager.LayoutParams.MATCH_PARENT
        } else {
            0
        }
    }

    override fun init() {
        if (!args.des.isNullOrEmpty()) {
            binding.tvDes.text = args.des
            binding.flLocationPermissions.visible()
        }
    }

    override fun loadFirstData() {
    }

    override fun onDestroy() {
        super.onDestroy()
        launcher?.unregister()
        launcher = null
        hadSetResult = false
    }
}