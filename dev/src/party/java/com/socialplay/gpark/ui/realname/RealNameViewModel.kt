package com.socialplay.gpark.ui.realname

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.realname.PartyAnalytics
import com.socialplay.gpark.data.model.realname.RealNameAutoInfo
import com.socialplay.gpark.data.model.realname.RealNameSurplusGameTime
import com.socialplay.gpark.data.model.realname.RealNameUpdateEvent
import com.socialplay.gpark.data.repository.UserRepositoryWrapper
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.extension.getStringByResIfNull
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.ext.android.get
import timber.log.Timber

sealed class AfterRealNameAction {
    data class AgeLimited(val limit: TimeLimitChild) : AfterRealNameAction()
    data object OpenMain : AfterRealNameAction()
    data object OpenGuide : AfterRealNameAction()
}

data class RealNameState(
    val isSubmitting: Boolean = false,
    val toast: ToastData = ToastData.EMPTY,
    val result: Async<AfterRealNameAction> = Uninitialized
) : MavericksState

class RealNameViewModel(
    initialState: RealNameState,
    private val userRepository: IMetaRepositoryWrapper
) : BaseViewModel<RealNameState>(initialState) {

    fun submitRealNameInfo(name: String, idCard: String) = viewModelScope.launch {
        if (idCard.length != 15 && idCard.length != 18) {
            setState { copy(toast = toast.toResMsg(R.string.real_name_auth_failed_invalid_id_num)) }
            return@launch
        }
        setState { copy(isSubmitting = true) }

        val result = userRepository.realNameCheck(name, idCard)
        // 处理实名次数过多的情况
        if (result.succeeded && result.data != null && !result.data!!.useCountLimit.isNullOrEmpty()) {
            PartyAnalytics.trackRealNameResult(false, result.data!!.useCountLimit)
            setState {
                copy(
                    isSubmitting = false,
                    toast = toast.toMsg(
                        result.data?.useCountLimit,
                        R.string.real_name_auth_failed_registered_too_many_times
                    )
                )
            }
            return@launch
        }

        // 认证失败情况
        val saveResult = userRepository.realNameSave(name, idCard, BuildConfig.APPLICATION_ID, "")
        if (!saveResult.succeeded) {
            PartyAnalytics.trackRealNameResult(
                false,
                getStringByResIfNull(saveResult.message, R.string.real_name_auth_failed)
            )
            setState {
                copy(
                    isSubmitting = false,
                    toast = toast.toMsg(saveResult.message, R.string.real_name_auth_failed)
                )
            }
            return@launch
        }

        val detailResult = userRepository.realNameDetail()
        val info = detailResult.data
        // 认证失败情况
        if (info == null) {
            PartyAnalytics.trackRealNameResult(false, detailResult.message)
            setState {
                copy(
                    isSubmitting = false,
                    toast = toast.toMsg(detailResult.message, R.string.real_name_auth_failed)
                )
            }
            return@launch
        }
        PartyAnalytics.trackRealNameResult(true)
        CpEventBus.post(RealNameUpdateEvent(info.age ?: -1, 0))


        checkAntiAddiction(info)

    }

    fun realNameDetail(callBack: (RealNameAutoInfo?) -> Unit) = viewModelScope.launch {
        val result = userRepository.realNameDetail()
        withContext(Dispatchers.Main) {
            callBack(result.data)
        }
    }


    private fun checkAntiAddiction(info: RealNameAutoInfo) = viewModelScope.launch {
        Timber.d("startAntiAddiction")
        val age = info.age ?: -1
        // 小于12岁，直接拦死
//        if (age in 0..11) {
//            // 产品说不做12岁以下的限制了
//            setState { copy(isSubmitting = false, result = Success(AfterRealNameAction.AgeLimited(TimeLimitChild(timeStart = 0, limitMessage = "12岁以下无法使用233派对", age = age)))) }
//            return@launch
//        }

        // 获取锁区剩余时长
        val result = userRepository.getRealNameSurplusGameTimeV3(0, "")
        val data = result.data
        if (data != null) {
            Timber.d("startAntiAddiction ${data.popup}")
            when (data.popup) {
                RealNameSurplusGameTime.Companion.Popup.NO -> {
                    // 成功通过实名认证
//                    val action = if (GuideManager.needStartLaunchGuide) AfterRealNameAction.OpenGuide else AfterRealNameAction.OpenMain
                    // 直接进行下一步，鉴于首次实名，应该都是新用户了，所以直接去跳转到角色创建页面
                    val action = AfterRealNameAction.OpenGuide
                    setState { copy(isSubmitting = false, result = Success(action)) }
                }

                RealNameSurplusGameTime.Companion.Popup.REAL_NAME -> {
                    // 实名认证有其他问题
                    setState {
                        copy(
                            isSubmitting = false,
                            toast = toast.toMsg(data.message, R.string.real_name_auth_not_passed)
                        )
                    }
                }

                else -> {
                    // 获取锁区剩余时长，根据时长看是否还能继续游玩
                    val timeLeft = data.surplusGameTime
                    val timeLimitChild = TimeLimitChild(
                        timeLeft,
                        getStringByResIfNull(data.message, R.string.play_game_only_weekend_tips),
                        System.currentTimeMillis(),
                        age = age
                    )
                    setState { copy(isSubmitting = false, result = Success(AfterRealNameAction.AgeLimited(timeLimitChild))) }

                }
            }
        } else {
            Timber.d("startAntiAddiction failed")
            setState {
                copy(
                    isSubmitting = false,
                    toast = toast.toMsg(result.message, R.string.real_name_auth_not_passed)
                )
            }
        }
    }


    companion object : KoinViewModelFactory<RealNameViewModel, RealNameState>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: RealNameState
        ): RealNameViewModel {
            return RealNameViewModel(state, get())
        }
    }
}