package com.socialplay.gpark.ui.gamepay.client

import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.PartyEventConstants
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.LifecycleInteractor
import com.socialplay.gpark.data.model.pay.AgentPayType.THIRD_PAY_CHANNEL_FAILED
import com.socialplay.gpark.data.model.pay.AgentPayVersion
import com.socialplay.gpark.data.model.pay.PayParamsParty
import com.socialplay.gpark.data.model.pay.TakeOrderInfo
import com.socialplay.gpark.function.analytics.Analytics

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/07/22
 *     desc   : 自有平台支付
 *
 */
class OwnPayClient :BasePayClient() {
    override fun startPay(params: PayParamsParty) {
        currentParams = params
        payInteractor.placeOrder(params.payChannel, getTakeOrderBean(params)) {
            if (it.succeeded) {
                val type = it.data?.channel
                if (type == null) {
                    val errorMessage = LifecycleInteractor.activityRef?.get()?.getString(R.string.pay_fail_payment_way_not_support)?:""
                    payFailed(errorMessage, THIRD_PAY_CHANNEL_FAILED)
                    return@placeOrder
                }
                Analytics.track(
                    PartyEventConstants.EVENT_PARTY_CLIENT_PAY_CLICK,
                    "gamecode" to (params.gameId ?: "null"),
                    "price" to params.pPrice.toString(),
                    "channel" to type,
                    "orderid" to (it.data?.orderCode ?: ""),
                    "productid" to (params.pCode ?: ""),
                    "source" to (params.pageSource ?: "")
                )
                kotlin.runCatching { startThirdPay(payResultEntity = it, type.toInt(), params) }
            } else {
                payFailed(it.message, it.code)
            }
        }
    }
    /**
     * 下单参数构建
     * @param params
     * @return
     */
    private fun getTakeOrderBean(params: PayParamsParty): TakeOrderInfo {
        val bean = TakeOrderInfo()
        //商品原价
        bean.amount = params.pPrice
        bean.productCode = params.pCode
        bean.productName = params.pName
        bean.count = params.pCount
        bean.cpOrderId = params.cpOrderId
        //实际支付价格
        bean.payAmount = params.getRealPrice()
        bean.nonce = params.pCode + System.currentTimeMillis().toString()
        bean.appKey = params.appkey
        bean.attachJson = params.cpExtra
        bean.extendJson = params.getExtendJson()
        bean.couponCode = params.voucherId
        //商品原价
        bean.productPrice = params.pPrice
        bean.sdkVersion = params.sdkVersion
        bean.sceneCode = params.getSceneCode()
        bean.platformReduceToken = params.memberCommonMemberParams?.platformReduceToken
        bean.promotionToken = params.memberCommonMemberParams?.promotionToken
        return bean
    }

    override fun type(): AgentPayVersion = AgentPayVersion.VERSION_OWN
}