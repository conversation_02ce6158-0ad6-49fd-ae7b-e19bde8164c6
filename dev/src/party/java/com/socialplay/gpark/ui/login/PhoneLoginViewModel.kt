package com.socialplay.gpark.ui.login

import android.content.ComponentCallbacks
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.R
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.getStringByGlobal
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import timber.log.Timber

sealed class LoginViewState {
    data class PhoneLogin(val phoneNumber: String = "") : LoginViewState()
    data class PhoneCode(val phoneNumber: String) : LoginViewState()
}

data class LoginStateV(
    val viewState: LoginViewState = LoginViewState.PhoneLogin(),
    val isLoading: Boolean = false,
    val toast: ToastData = ToastData.EMPTY,
    val loginFailedState: String = "",
    val success: MetaUserInfo? = null
) : MavericksState

class PhoneLoginViewModel(
    initialState: LoginStateV,
    private val metaRepository: IMetaRepositoryWrapper,
    private val accountInteractor: AccountInteractor,
) : BaseViewModel<LoginStateV>(initialState) {
    var firstLoginViewState: LoginViewState = LoginViewState.PhoneLogin()
    private val loginSource = LoginSource.MainForceLogin


    fun setLoginViewState(state: LoginViewState) = setState { copy(viewState = state) }
    fun resetLoginFailedState() {
        setState { copy(loginFailedState = "") }
    }

    private var lastPhoneNumber = ""
    fun finishedSmsCodeCountDown() {
        lastPhoneNumber = ""
    }

    fun getLoginPhoneCode(phoneNumber: String) = viewModelScope.launch {
        if (lastPhoneNumber == phoneNumber) {
            setState { copy(viewState = LoginViewState.PhoneCode(phoneNumber), toast = toast.toResMsg(R.string.party_login_phone_code_valid)) }
            return@launch
        }
        setState { copy(isLoading = true) }
        metaRepository.getLoginPhoneCode(phoneNumber).collect {
            if (it.data == true || it.code == 2092) {
                lastPhoneNumber = phoneNumber
                setState { copy(isLoading = false, viewState = LoginViewState.PhoneCode(phoneNumber)) }
            } else {
                setState { copy(isLoading = false, toast = it.message?.let { it1 -> toast.toMsg(it1) } ?: toast.toResMsg(R.string.party_acquire_code_faile)) }
            }
        }
    }


    fun loginByPhone(phone: String, phoneCode: String) = viewModelScope.launch {
        setState { copy(isLoading = true, loginFailedState = "") }
        val oAuthResponse = OAuthResponse(LoginWay.Phone, phoneCode, phone)
        oAuthResponse.loginType
        accountInteractor.loginByAuthResponse(oAuthResponse, true).collect {
            when (it) {
                is LoginState.Loading -> {

                }

                is LoginState.Succeeded -> {
//                    Analytics.track(EventConstants.EVENT_LOGIN_SUCCEED) {
//                        put(EventConstants.KEY_LOGIN_WAY, oAuthResponse.oauthThirdWay.way)
//                        put(EventConstants.KEY_LOGIN_TYPE, oAuthResponse.loginType ?: "")
//                    }
                    onLoginSuccess(LoginWay.Phone, it.userInfo)
                }

                is LoginState.Failed -> {
                    onLoginFailed(LoginWay.Phone, it.message)
                }

                is LoginState.UserCanceled -> {
                    setState { copy(isLoading = false) }
                }
            }
        }
    }

    private suspend fun onLoginSuccess(loginWay: LoginWay, userInfo: MetaUserInfo?) {
        Timber.d("onLoginSuccess:$userInfo")

//        Pandora.send(EventConstants.EVENT_LOGIN_RESULT) {
//            put("type", loginWay.way)
//            put("result", "success")
//            put("state", if (firstBinding) 1 else 0)
//        }
        setState { copy(isLoading = false, success = userInfo) }
    }

    private fun onLoginFailed(type: LoginWay, message: String?) {
//        Pandora.send(EventConstants.EVENT_LOGIN_RESULT) {
//            put("type", type.way)
//            put("result", "failed")
//            put("toast", (message ?: "登录失败"))
//        }
        val loginFailedMessage = message ?: getStringByGlobal(R.string.party_acquire_code_faile)
        setState { copy(isLoading = false, toast = toast.toMsg(message), loginFailedState = loginFailedMessage) }
    }

    fun resetLoginVState() {
        setState { copy(success = null) }
    }

    companion object : KoinViewModelFactory<PhoneLoginViewModel, LoginStateV>() {
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: LoginStateV
        ): PhoneLoginViewModel {
            return PhoneLoginViewModel(state, get(), get())
        }
    }
}