package com.socialplay.gpark.ui.realname

import android.app.Application
import androidx.fragment.app.FragmentActivity
import com.bin.cpbus.CpEventBus
import com.meta.box.data.model.realname.*
import com.socialplay.gpark.data.IMetaRepositoryWrapper
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.realname.PartyAnalytics
import com.socialplay.gpark.data.model.realname.RealNameSurplusGameTime
import com.socialplay.gpark.data.model.realname.RealNameUpdateEvent
import com.socialplay.gpark.function.startup.core.StartupContext
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.util.*

/**
 * @author: ning.wang
 * @date: 2021-06-15 11:09 上午
 * @desc:
 */
object RealName {

    const val TAG = "real-name"

    /**
     *  暂留的埋点
     * val EVENT_REAL_NAME_DIALOG_SHOW = Event("event_real_name_dialog_show", "实名认证，弹窗展示")
     * val EVENT_IDENTIFICATION_TIPS_DIALOG_SHOW = Event("event_identification_tips_dialog_show", "实名_提示弹窗_展示")
     * val EVENT_IDENTIFICATION_TIPS_DIALOG_CANCEL_CLICK = Event("event_identification_tips_dialog_cancel_click", "实名_提示弹窗_取消_点击")
     * val EVENT_IDENTIFICATION_TIPS_DIALOG_SUBMIT_CLICK = Event("event_identification_tips_dialog_submit_click", "实名_提示弹窗_提交_点击")
     */

    /**
     * 埋点参数 source 来源（string）
     * [com.meta.box.function.analytics.EventConstants.EVENT_IDENTIFICATION_PAGE_SHOW]
     * 0：登录页面
     * 1：游戏内-时长限制
     * 2：游戏内-支付限制
     * 3：账号设置
     * 4：首页-底栏浮窗
     * 5: 支付限制
     * 6: 注册成功
     * 7：悬浮球游戏助手？
     * 8：web
     * 9：软性弹窗-去实名
     * 11：福利
     * 12：游戏评价
     *
     * 14:社区
     * 15:下载游戏时
     * 16:游戏中播放广告时
     * 18：ugc评论
     */
    const val FROM_LOGIN = 0
    const val FROM_TIME_LIMIT_GAME = 1
    const val FROM_PAY_LIMIT_GAME = 2
    const val FROM_ACCOUNT_SETTING = 3
    const val FROM_HOME = 4
    const val FROM_PAY_LIMIT_APP = 5
    const val FROM_REGISTER = 6
    const val FROM_FLOATING_GAME_ASSISTANT = 7
    const val FROM_WEB = 8
    const val FROM_FLEXIBLE = 9
    const val FROM_WELFARE_JOIN = 11
    const val FROM_APPRAISE = 12
    const val FROM_COMMUNITY = 13
    const val FROM_DOWNLOAD_GAME = 15
    const val FROM_GAME_AD = 16
    const val FROM_FAMILY = 17
    const val FROM_UGC_COMMENT = 18


    /**
     * 埋点参数 type 类型（long）
     * [com.meta.box.function.analytics.EventConstants.EVENT_IDENTIFICATION_PAGE_SHOW]
     * 0: 来自游戏
     * 1: 来自APP
     */
    const val FROM_GAME = 0
    const val FROM_APP = 1

    /**
     * 埋点参数 reason 类型（String）
     */
    // 第一层级
    const val REASON_SINGLE_GAME = "single_game" // 单机游戏
    const val REASON_ONLINE_GAME = "online_game" // 网络游戏
    const val REASON_BITTER = "bitter" // 黑名单

    // 这个比较特殊，是第一层级的子级，属于单机网游里；会单独多发一次
    const val REASON_BAN_PKG_LIST = "banpkglist" // 命中黑名单app
    // 第二层级接口返回的


    const val REASON_FLEXIBLE_DIALOG = "flexible_dialog"

    private val accountInteractor: AccountInteractor by lazy { GlobalContext.get().get() }
    private val metaRepository: IMetaRepositoryWrapper by lazy { GlobalContext.get().get() }
    private val metaKV: MetaKV by lazy { GlobalContext.get().get() }

    var application: Application? = null

    fun init(application: Application, isMainProcess: Boolean) {
        Timber.d("${RealName.TAG} init - application = $application")
        RealName.application = application
        if (isMainProcess) {
            CpEventBus.register(this)
        }
    }

    @Subscribe
    fun onEvent(event: RealNameUpdateEvent) {
        Timber.i(
            "收到实名认证变化: %s , 进程: %s， 当前进程: %s",
            event.age,
            StartupContext.get().processType,
            StartupContext.get().processName
        )
        accountInteractor.updateRealNameUserInfo(event.age, event.realNameSource)
        metaKV.realName.resetUpdateStatus()
    }

//    fun toLogin(pkgName: String?, isTs: Boolean, gameId: Long?) {
//        application?.let { MetaRouter.Main.gameLogin(it, isTs, gameId, pkgName, VISITOR_DIALOG) }
//    }
//
//    fun toRealName(pkgName: String?, from: Int) {
//        application?.let { MetaRouter.Main.gameRealName(it, pkgName, from) }
//    }

    private var retryCount = 0

    fun refreshRealNameInfo() {
//        if (accountInteractor.isGuestLogin()) return

        MainScope().launch {
            val result = metaRepository.realNameDetail()
            if (!result.succeeded || result.data == null || result.data!!.age == null) {
                if (isNeedUpdate() && retryCount < 3) {
                    refreshRealNameInfo()
                }
                retryCount++
                return@launch
            }
            result.data!!.age?.let {
                accountInteractor.updateRealNameUserInfo(it, result.data!!.client)
                metaKV.realName.resetUpdateStatus()
            }
        }
    }

    /**
     * 是否可以正常游玩
     * @return true: 可以正常游玩，false：未成年，受时长限制，不能正常游玩
     */
    fun fetchRealNameInfo(callback: (TimeLimitChild?, Boolean?) -> Unit) {
        MainScope().launch {
            val result = metaRepository.getRealNameSurplusGameTimeV3(0, "")
            val data = result.data
            if (!result.succeeded || data == null) {
                callback(null, true)
                return@launch
            }
            if (data.popup != RealNameSurplusGameTime.Companion.Popup.NO) {
                val timeLimitChild = TimeLimitChild(
                    data.surplusGameTime,
                    data.message ?: "",
                    System.currentTimeMillis()
                )
                if (timeLimitChild.timeLeft <= 0) {
                    // 未成年，受时长限制，不能正常游玩
                    callback(timeLimitChild, false)
                } else {
                    callback(timeLimitChild, true)
                }
            }
        }
    }


    private fun isNeedUpdate(): Boolean {
        return metaKV.realName.isNeedUpdate()
    }

    fun showLimitDialog(child: TimeLimitChild, activity: FragmentActivity, source: String, function: () -> Nothing) {
        AntiAdditionLimitDialog.show(activity, child, source) {
            function.invoke()
        }
    }

//    fun toWeb(pkgName: String?, url: String, title: String?) {
//        /*application?.let {
//            MetaRouter.Main.gameWeb(it, url, pkgName, title)
//        }*/
//
//        application?.startActivity(Intent(application, WebActivity::class.java).also {
//            it.putExtras(
//                WebActivityArgs(
//                    url = url,
//                    showTitle = true,
//                    statusBarColor = "#FF8938",
//                    from = "game",
//                    gamePackageName = pkgName,
//                    showStatusBar = false,
//                    needWebLifecycle = true
//                ).toBundle()
//            )
//            it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//        })
//    }

}