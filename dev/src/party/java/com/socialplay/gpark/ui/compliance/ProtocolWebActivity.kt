package com.socialplay.gpark.ui.compliance

import android.os.Bundle
import android.webkit.WebView
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ActivityProtocolWebBinding
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.property.viewBinding

class ProtocolWebActivity : BaseActivity() {
    override var canLandscape = true
    override val binding by viewBinding(ActivityProtocolWebBinding::inflate)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding.tbl.setOnBackClickedListener {
            finish()
        }
        val web = kotlin.runCatching { WebView(this) }.getOrElse {
            ToastUtil.showShort(R.string.web_init_failed)
            finish()
            return
        }
        binding.flWebContainer.addView(web)
        val url = intent.getStringExtra("url")!!
        web.loadUrl(url)

        val title = intent.getStringExtra("title")
        if (title != null && !title.isNullOrEmpty() && title.isNotBlank()) {
            binding.tbl.titleView.text = title
        }
    }

}