package com.meta.box.di

import android.content.Context
import com.socialplay.gpark.BuildConfig
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import org.koin.android.ext.koin.androidApplication
import org.koin.dsl.module

/**
 * @author: ning.wang
 * @date: 2021-05-25 3:08 下午
 * @desc:
 */
val wechatModule = module {
    factory { provideWechatApi(androidApplication()) }
}

internal fun provideWechatApi(context: Context): IWXAPI{
    val wxapi = WXAPIFactory.createWXAPI(context, BuildConfig.WECHAT_APP_ID)
    wxapi.registerApp(BuildConfig.WECHAT_APP_ID)
    return wxapi
}