package com.socialplay.gpark.function.share.callback

import android.os.Bundle
import android.os.SystemClock
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import com.bin.cpbus.CpEventBus
import com.bumptech.glide.Glide
import com.meta.share.MetaShare
import com.meta.share.base.ShareCallback
import com.meta.share.util.InstallUtil
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.data.model.share.ThirdShareParams
import com.socialplay.gpark.databinding.ActivityQqCallbackBinding
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.FileProviderUtil
import com.socialplay.gpark.util.extension.registerHermes
import com.socialplay.gpark.util.extension.resumeGameById
import com.socialplay.gpark.util.extension.unregisterHermes
import com.socialplay.gpark.util.ifNullOrEmpty
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.inject
import java.lang.ref.WeakReference


/**
 * 游戏内qq分享
 */
class WXShareCallbackActivity : BaseActivity() {
    override var canLandscape = true

    private var needFinish: Boolean = false
    private var needPostEvent: Boolean = false

    private var mFinishJob: Job? = null
    private var mPauseTs: Long = -1L

    private lateinit var args: ThirdShareParams

    override val binding by viewBinding(ActivityQqCallbackBinding::inflate)

    companion object {
        var reqId: String = ""
        var platform: String = ""
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val params = intent.getParcelableExtra<ThirdShareParams>(ShareWrapper.THIRD_PARAMS)
        if (params == null) {
            invalidParams()
            return
        }
        args = params
        reqId = args.reqId
        platform = args.platform

        registerHermes()
        shareWX()
    }

    private fun shareWX() {
        needPostEvent = true
        var directCallback = false
        val callback = object : ShareCallback {
            override fun invoke(ts: Long, success: Boolean, errorMessage: String?) {
                if (directCallback) {
                    unregisterHermes()
                    needPostEvent = false
                    if (success) {
                        ShareResult.notifySuccess(args.reqId, args.platform)
                        needFinish = true
                    } else {
                        ShareResult.notifyFail(
                            args.reqId,
                            args.platform,
                            -1,
                            errorMessage.ifNullOrEmpty { getString(R.string.share_fail) }
                        )
                        finishOrMark()
                    }
                } else if (needPostEvent && !success) {
                    unregisterHermes()
                    needPostEvent = false
                    ShareResult.notifyFail(
                        args.reqId,
                        args.platform,
                        -1,
                        errorMessage.ifNullOrEmpty { getString(R.string.share_fail) }
                    )
                    finishOrMark()
                }
            }
        }
        when (args.platform) {
            SharePlatform.PLATFORM_WECHAT_FRIEND -> {
                when (args.mode) {
                    ShareHelper.MODE_SINGLE_WEB_CARD -> {
                        val webCard = args.sticker
                        if (webCard == null) {
                            invalidParams()
                            return
                        }
                        val path = getValidPath(webCard.icon)
                        if (path.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.weChatCard(
                                0,
                                webCard.title,
                                webCard.desc,
                                webCard.url,
                                path,
                            ),
                            callback
                        )
                    }

                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        val path = getValidPath(args.path)
                        if (path.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.weChatImage(
                                0,
                                path
                            ),
                            callback
                        )
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        val paths = args.paths
                        if (paths.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.weChatMultiImages(
                                0,
                                paths.mapNotNull(::getValidPath)
                            ),
                            callback
                        )
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        val path = getValidPath(args.path)
                        if (path.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        directCallback = true
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.weChatVideo(
                                0,
                                path
                            ),
                            callback
                        )
                    }

                    else -> {
                        unsupported()
                        return
                    }
                }
            }

            SharePlatform.PLATFORM_WECHAT_MOMENT -> {
                when (args.mode) {
                    ShareHelper.MODE_SINGLE_WEB_CARD -> {
                        val webCard = args.sticker
                        if (webCard == null) {
                            invalidParams()
                            return
                        }
                        val path = getValidPath(webCard.icon)
                        if (path.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.weChatMomentCard(
                                0,
                                webCard.title,
                                webCard.desc,
                                webCard.url,
                                path
                            ),
                            callback
                        )
                    }

                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        val path = getValidPath(args.path)
                        if (path.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.weChatMomentImage(
                                0,
                                path
                            ),
                            callback
                        )
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        val path = getValidPath(args.path)
                        if (path.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.weChatMomentVideo(
                                0,
                                path
                            ),
                            callback
                        )
                    }

                    else -> {
                        unsupported()
                        return
                    }
                }
            }

            else -> {
                unsupported()
                return
            }
        }
    }

    private fun getValidPath(path: String?): String? {
        return FileProviderUtil.getValidPathV2(
            this,
            path,
            InstallUtil.PACKAGE_WECHAT
        )
    }

    private fun unsupported() {
        needPostEvent = false
        ShareResult.notifyFail(
            args.reqId,
            args.platform,
            ShareHelper.CODE_UNSUPPORTED_PLATFORM,
            null
        )
        finish()
    }

    private fun invalidParams() {
        needPostEvent = false
        ShareResult.notifyFail(
            args.reqId,
            args.platform,
            ShareHelper.CODE_INVALID_PARAMS,
            null
        )
        finish()
    }

    @Subscribe
    fun onEvent(event: ShareResult) {
        if (event.match(reqId, platform)) {
            unregisterHermes()
            needPostEvent = false
            finishOrMark()
        }
    }

    private fun finishOrMark() {
        if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
            finish()
        } else {
            needFinish = true
        }
    }

    override fun onPause() {
        super.onPause()
        needFinish = true
        mPauseTs = SystemClock.elapsedRealtime()
        mFinishJob?.cancel()
        mFinishJob = null
    }

    override fun onResume() {
        super.onResume()
        if (needFinish) {
            if (SystemClock.elapsedRealtime() - mPauseTs > 200) {
                finish()
            } else {
                mFinishJob?.cancel()
                mFinishJob = lifecycleScope.launch {
                    delay(1_000)
                    finishOrMark()
                }
            }
        }
    }

    override fun onDestroy() {
        unregisterHermes()
        super.onDestroy()
    }

    override fun finish() {
        args.gameId?.let {
            resumeGameById(it)
        }
        if (needPostEvent) {
            needPostEvent = false
            unregisterHermes()
            ShareResult.notifySuccess(reqId, platform)
        }
        super.finish()
    }
}