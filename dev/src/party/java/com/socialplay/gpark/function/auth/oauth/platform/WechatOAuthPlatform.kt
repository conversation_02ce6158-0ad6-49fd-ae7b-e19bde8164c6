package com.socialplay.gpark.function.auth.oauth.platform

import android.app.Activity
import android.app.Application
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.data.model.user.OauthTransInfo
import com.socialplay.gpark.function.auth.oauth.OAuthManager
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.ToastUtil
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.IWXAPI
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.context.GlobalContext

/**
 * @author: ning.wang
 * @date: 2021-09-23 8:21 下午
 * @desc:
 */
class WechatOAuthPlatform : OAuthPlatform, KoinComponent {
    private val wxApi: IWXAPI by inject()

    companion object {
        private const val TAG = "WechatOAuthPlatform"
    }


    override fun login(activity: Activity?, source: String?, loginType: LoginType?) {
        val context = activity ?: (GlobalContext.get().get() as Application)
        if (!wxApi.isWXAppInstalled) {
            OAuthManager.callbacks.dispatchOnMainThread {
                this.onFailed(LoginWay.Wechat, context.getString(R.string.withdraw_wechat_not_install), -1)
            }
            return
        }
        sendReq(GsonUtil.safeToJson(OauthTransInfo(source ?: "", loginType?.type ?: "")))
    }

    override fun logout() {
        val context = (GlobalContext.get().get() as Application)
    }

    /**
     * 发送微信授权请求
     */
    private fun sendReq(transition: String?) {
        val req = SendAuth.Req()
        req.scope = "snsapi_userinfo"
        req.state = "wechat_sdk_demo_test"
        req.transaction = transition
        wxApi.sendReq(req)
    }

}