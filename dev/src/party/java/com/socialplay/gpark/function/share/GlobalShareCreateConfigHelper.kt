package com.socialplay.gpark.function.share

import android.content.Context
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.data.model.editor.UgcGameDetail
import com.socialplay.gpark.data.model.post.PostShareDetail
import com.socialplay.gpark.data.model.share.ShareConfig
import com.socialplay.gpark.data.model.share.SharePendingData
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.ui.share.GlobalShareState
import com.socialplay.gpark.ui.share.GlobalShareViewModel
import com.socialplay.gpark.util.ifNullOrEmpty
import org.koin.core.context.GlobalContext

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/08/29
 *     desc   :
 * </pre>
 */
object GlobalShareCreateConfigHelper {

    fun getLang(): String {
        return ""
    }

    fun createConfig4Profile(type: Int, platform: String): Int {
        when (platform) {
            SharePlatform.PLATFORM_SYSTEM -> {
                return GlobalShareViewModel.CONFIG_TTAI
            }

            SharePlatform.PLATFORM_WECHAT_FRIEND,
            SharePlatform.PLATFORM_QQ_FRIEND -> {
                return GlobalShareViewModel.CONFIG_NOTHING
            }
        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4Pgc(type: Int, platform: String): Int {
        when (type) {
            GlobalShareState.TYPE_NORMAL -> {
                when (platform) {
                    SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
                    SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH,
                    SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                        return GlobalShareViewModel.CONFIG_TTAI
                    }

                    SharePlatform.PLATFORM_WECHAT_MOMENT,
                    SharePlatform.PLATFORM_QQ_ZONE,
                    SharePlatform.PLATFORM_WECHAT_FRIEND,
                    SharePlatform.PLATFORM_QQ_FRIEND -> {
                        return GlobalShareViewModel.CONFIG_LINK
                    }
                }
            }

            GlobalShareState.TYPE_LONG_IMAGE -> {
                when (platform) {
                    SharePlatform.PLATFORM_WECHAT_MOMENT,
                    SharePlatform.PLATFORM_QQ_ZONE,
                    SharePlatform.PLATFORM_WECHAT_FRIEND,
                    SharePlatform.PLATFORM_QQ_FRIEND -> {
                        return GlobalShareViewModel.CONFIG_NOTHING
                    }
                }
            }
        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4Ugc(type: Int, platform: String): Int {
        when (type) {
            GlobalShareState.TYPE_NORMAL -> {
                when (platform) {
                    SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
                    SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH,
                    SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                        return GlobalShareViewModel.CONFIG_TTAI
                    }

                    SharePlatform.PLATFORM_WECHAT_MOMENT,
                    SharePlatform.PLATFORM_QQ_ZONE,
                    SharePlatform.PLATFORM_WECHAT_FRIEND,
                    SharePlatform.PLATFORM_QQ_FRIEND -> {
                        return GlobalShareViewModel.CONFIG_LINK
                    }
                }
            }

            GlobalShareState.TYPE_LONG_IMAGE -> {
                when (platform) {
                    SharePlatform.PLATFORM_WECHAT_MOMENT,
                    SharePlatform.PLATFORM_QQ_ZONE,
                    SharePlatform.PLATFORM_WECHAT_FRIEND,
                    SharePlatform.PLATFORM_QQ_FRIEND -> {
                        return GlobalShareViewModel.CONFIG_NOTHING
                    }
                }
            }
        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4Post(type: Int, platform: String): Int {
        when (platform) {
            SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH,
            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                return GlobalShareViewModel.CONFIG_TTAI
            }

            SharePlatform.PLATFORM_WECHAT_MOMENT,
            SharePlatform.PLATFORM_QQ_ZONE,
            SharePlatform.PLATFORM_KUAISHOU_FRIEND,
            SharePlatform.PLATFORM_DOUYIN_FRIEND,
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            SharePlatform.PLATFORM_QQ_FRIEND -> {
                return GlobalShareViewModel.CONFIG_TTAI_LINK
            }
        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4Video(type: Int, platform: String): Int {
        when (platform) {

        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4OcMoment(type: Int, platform: String): Int {
        when (platform) {
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            SharePlatform.PLATFORM_WECHAT_MOMENT,
            SharePlatform.PLATFORM_QQ_FRIEND,
            SharePlatform.PLATFORM_QQ_ZONE -> {
                return GlobalShareViewModel.CONFIG_NOTHING
            }

            SharePlatform.PLATFORM_DOUYIN_PUBLISH,
            SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                return GlobalShareViewModel.CONFIG_TTAI
            }
        }

        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun createConfig4Screenshot(type: Int, platform: String): Int {
        when (platform) {
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            SharePlatform.PLATFORM_WECHAT_MOMENT,
            SharePlatform.PLATFORM_QQ_FRIEND,
            SharePlatform.PLATFORM_QQ_ZONE -> {
                return GlobalShareViewModel.CONFIG_NOTHING
            }

            SharePlatform.PLATFORM_DOUYIN_PUBLISH,
            SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                return GlobalShareViewModel.CONFIG_TTAI
            }
        }
        return GlobalShareViewModel.CONFIG_UNSUPPORTED
    }

    fun ttaiConfigTransformer(
        context: Context,
        platform: String,
        shareConfig: ShareConfig
    ): ShareConfig {
        return when (platform) {
            else -> {
                shareConfig
            }
        }
    }

    fun getPgcGameTitleContent(
        config: ShareConfig?,
        data: SharePendingData,
        pgcGame: ShareRawData.Game
    ): Pair<String?, String?> {
        val result = config?.titleContentByPlatform(
            data.platform,
            gameName = pgcGame.name,
            authorName = pgcGame.authorName,
            userName = GlobalContext.get().get<AccountInteractor>().curNameOrNull,
            pvCount = pgcGame.playerCount
        )
        return result ?: (null to null)
    }

    fun getUgcGameTitleContent(
        config: ShareConfig?,
        data: SharePendingData,
        ugcGame: UgcGameDetail
    ): Pair<String?, String?> {
        val result = config?.titleContentByPlatform(
            data.platform,
            gameName = ugcGame.ugcGameName,
            authorName = ugcGame.author?.name,
            userName = GlobalContext.get().get<AccountInteractor>().curNameOrNull,
            pvCount = ugcGame.pvCount
        )
        return result ?: (null to null)
    }

    fun getPostTitleContent(
        config: ShareConfig?,
        data: SharePendingData,
        postShareDetail: PostShareDetail
    ): Pair<String?, String?> {
        val result = config?.titleContentByPlatform(
            data.platform,
            postContent = postShareDetail.content
        )
        return result ?: (null to null)
    }

    fun getProfileTitleContent(
        context: Context,
        config: ShareConfig?,
        nickname: String
    ): Pair<String, String> {
        return ShareConfig.replaceContent(config?.system?.title, userName = nickname)
            .ifNullOrEmpty {
                context.getString(
                    R.string.share_profile_title,
                    nickname
                )
            } to ShareConfig.replaceContent(config?.system?.desc, userName = nickname)
            .ifNullOrEmpty {
                context.getString(
                    R.string.share_profile_content
                )
            }
    }
}