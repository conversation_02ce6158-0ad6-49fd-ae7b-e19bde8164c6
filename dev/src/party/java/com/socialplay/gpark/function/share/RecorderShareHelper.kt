package com.socialplay.gpark.function.share

import com.socialplay.gpark.databinding.DialogScreenRecordEndGameBinding
import com.socialplay.gpark.util.extension.gone

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/24
 *     desc   :
 * </pre>
 */
object RecorderShareHelper {

    fun initBinding(binding: DialogScreenRecordEndGameBinding) {
        binding.clOfficialIds.gone()
        binding.tvTitleDiscord.text = ""
        binding.tvContentDiscord.text = ""
        binding.tvTitleTiktok.text = ""
        binding.tvContentTiktok.text = ""
        binding.tvTitleYoutube.text = ""
        binding.tvContentYoutube.text = ""
    }
}