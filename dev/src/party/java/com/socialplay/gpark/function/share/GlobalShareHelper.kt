package com.socialplay.gpark.function.share

import android.view.View
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.mw.develop.util.ifNullOrBlank
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.editor.UgcDetailInfo
import com.socialplay.gpark.data.model.editor.UgcGameDetail
import com.socialplay.gpark.data.model.post.PostShareDetail
import com.socialplay.gpark.data.model.share.ShareData
import com.socialplay.gpark.data.model.share.SharePendingData
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.videofeed.VideoFeedItem
import com.socialplay.gpark.databinding.ViewShareScreenshotBinding
import com.socialplay.gpark.function.cdnview.CacheCdnImageTask
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.editor.share.AvatarShareViewModelState
import com.socialplay.gpark.ui.share.GlobalShareState
import com.socialplay.gpark.ui.share.GlobalShareViewModel
import com.socialplay.gpark.ui.share.IGlobalShareDialogMixin
import com.socialplay.gpark.util.QRCode
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setTextWithArgs
import kotlinx.coroutines.CoroutineScope
import org.koin.core.context.GlobalContext
import java.io.File

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/22
 *     desc   :
 * </pre>
 */
object GlobalShareHelper {

    fun shareProfile(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_WECHAT_FRIEND -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.wechatFriendSingleImage(
                            requestId,
                            it
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_QQ_FRIEND -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.qqFriendSingleImage(
                            requestId,
                            it
                        )
                    )
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun sharePgc(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String,
        pgcGame: ShareRawData.Game,
        scope: CoroutineScope
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_KUAISHOU_PUBLISH -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    MetaShare.share(
                        activity,
                        ShareData.kuaishouPublishMultiImages(
                            requestId,
                            s.paths,
                            config = s.config
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    val (title, content) = GlobalShareCreateConfigHelper.getPgcGameTitleContent(
                        s.config,
                        data,
                        pgcGame
                    )
                    MetaShare.share(
                        activity,
                        ShareData.xiaohongshuPublishMultiImages(
                            requestId,
                            s.paths,
                            config = s.config,
                            title = title,
                            content = content
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    val (shortTitle, title) = GlobalShareCreateConfigHelper.getPgcGameTitleContent(
                        s.config,
                        data,
                        pgcGame
                    )
                    MetaShare.share(
                        activity,
                        ShareData.douyinPublishMultiImages(
                            requestId,
                            s.paths,
                            config = s.config,
                            shortTitle = shortTitle,
                            title = title
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_WECHAT_MOMENT -> {
                val (title, content) = GlobalShareCreateConfigHelper.getPgcGameTitleContent(
                    s.config,
                    data,
                    pgcGame
                )
                MetaShare.share(
                    activity,
                    ShareData.wechatMomentsWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = pgcGame.icon.ifNullOrBlank { CacheCdnImageTask.CDN_APP_ICON_144 },
                            title = title ?: activity.getString(
                                R.string.share_game_card_title,
                                GlobalContext.get()
                                    .get<AccountInteractor>().curNameOrNull.orEmpty(),
                                pgcGame.name.orEmpty()
                            ),
                            desc = content ?: activity.getString(
                                R.string.share_game_card_content,
                                pgcGame.authorName.orEmpty(),
                                (pgcGame.playerCount ?: 0).toString()
                            ),
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_QQ_ZONE -> {
                val (title, content) = GlobalShareCreateConfigHelper.getPgcGameTitleContent(
                    s.config,
                    data,
                    pgcGame
                )
                MetaShare.share(
                    activity,
                    ShareData.qqZoneWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = pgcGame.icon.ifNullOrBlank { CacheCdnImageTask.CDN_APP_ICON_144 },
                            title = title ?: activity.getString(
                                R.string.share_game_card_title,
                                GlobalContext.get()
                                    .get<AccountInteractor>().curNameOrNull.orEmpty(),
                                pgcGame.name.orEmpty()
                            ),
                            desc = content ?: activity.getString(
                                R.string.share_game_card_content,
                                pgcGame.authorName.orEmpty(),
                                (pgcGame.playerCount ?: 0).toString()
                            ),
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_WECHAT_FRIEND -> {
                val (title, content) = GlobalShareCreateConfigHelper.getPgcGameTitleContent(
                    s.config,
                    data,
                    pgcGame
                )
                MetaShare.share(
                    activity,
                    ShareData.wechatFriendWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = pgcGame.icon.ifNullOrBlank { CacheCdnImageTask.CDN_APP_ICON_144 },
                            title = title ?: activity.getString(
                                R.string.share_game_card_title,
                                GlobalContext.get()
                                    .get<AccountInteractor>().curNameOrNull.orEmpty(),
                                pgcGame.name.orEmpty()
                            ),
                            desc = content ?: activity.getString(
                                R.string.share_game_card_content,
                                pgcGame.authorName.orEmpty(),
                                (pgcGame.playerCount ?: 0).toString()
                            ),
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_QQ_FRIEND -> {
                val (title, content) = GlobalShareCreateConfigHelper.getPgcGameTitleContent(
                    s.config,
                    data,
                    pgcGame
                )
                MetaShare.share(
                    activity,
                    ShareData.qqFriendWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = pgcGame.icon.ifNullOrBlank { CacheCdnImageTask.CDN_APP_ICON_144 },
                            title = title ?: activity.getString(
                                R.string.share_game_card_title,
                                GlobalContext.get()
                                    .get<AccountInteractor>().curNameOrNull.orEmpty(),
                                pgcGame.name.orEmpty()
                            ),
                            desc = content ?: activity.getString(
                                R.string.share_game_card_content,
                                pgcGame.authorName.orEmpty(),
                                (pgcGame.playerCount ?: 0).toString()
                            ),
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_LONG_IMAGE -> {
                when (data.subPlatform) {
                    SharePlatform.PLATFORM_WECHAT_MOMENT -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.wechatMomentsSingleImage(
                                    requestId,
                                    it
                                )
                            )
                        }
                    }

                    SharePlatform.PLATFORM_QQ_ZONE -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.qqZoneSingleImage(
                                    requestId,
                                    it
                                )
                            )
                        }
                    }

                    SharePlatform.PLATFORM_WECHAT_FRIEND -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.wechatFriendSingleImage(
                                    requestId,
                                    it
                                )
                            )
                        }
                    }

                    SharePlatform.PLATFORM_QQ_FRIEND -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.qqFriendSingleImage(
                                    requestId,
                                    it
                                )
                            )
                        }
                    }
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun sharePgcSupportLongImage(platform: String?): Boolean {
        when (platform) {
            SharePlatform.PLATFORM_WECHAT_MOMENT,
            SharePlatform.PLATFORM_QQ_ZONE,
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            SharePlatform.PLATFORM_QQ_FRIEND -> {
                return true
            }
        }

        return false
    }

    fun shareUgc(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String,
        ugcGame: UgcGameDetail,
        scope: CoroutineScope
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_KUAISHOU_PUBLISH -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    MetaShare.share(
                        activity,
                        ShareData.kuaishouPublishMultiImages(
                            requestId,
                            s.paths,
                            config = s.config
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    val (title, content) = GlobalShareCreateConfigHelper.getUgcGameTitleContent(
                        s.config,
                        data,
                        ugcGame
                    )
                    MetaShare.share(
                        activity,
                        ShareData.xiaohongshuPublishMultiImages(
                            requestId,
                            s.paths,
                            config = s.config,
                            title = title,
                            content = content
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    val (shortTitle, title) = GlobalShareCreateConfigHelper.getUgcGameTitleContent(
                        s.config,
                        data,
                        ugcGame
                    )
                    MetaShare.share(
                        activity,
                        ShareData.douyinPublishMultiImages(
                            requestId,
                            s.paths,
                            config = s.config,
                            shortTitle = shortTitle,
                            title = title
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_WECHAT_MOMENT -> {
                val (title, content) = GlobalShareCreateConfigHelper.getUgcGameTitleContent(
                    s.config,
                    data,
                    ugcGame
                )
                MetaShare.share(
                    activity,
                    ShareData.wechatMomentsWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = CacheCdnImageTask.CDN_APP_ICON_144,
                            title = title ?: activity.getString(
                                R.string.share_game_card_title,
                                GlobalContext.get()
                                    .get<AccountInteractor>().curNameOrNull.orEmpty(),
                                ugcGame.ugcGameName.orEmpty()
                            ),
                            desc = content ?: activity.getString(
                                R.string.share_game_card_content,
                                ugcGame.author?.name.orEmpty(),
                                ugcGame.pvCount.toString()
                            ),
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_QQ_ZONE -> {
                val (title, content) = GlobalShareCreateConfigHelper.getUgcGameTitleContent(
                    s.config,
                    data,
                    ugcGame
                )
                MetaShare.share(
                    activity,
                    ShareData.qqZoneWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = CacheCdnImageTask.CDN_APP_ICON_144,
                            title = title ?: activity.getString(
                                R.string.share_game_card_title,
                                GlobalContext.get()
                                    .get<AccountInteractor>().curNameOrNull.orEmpty(),
                                ugcGame.ugcGameName.orEmpty()
                            ),
                            desc = content ?: activity.getString(
                                R.string.share_game_card_content,
                                ugcGame.author?.name.orEmpty(),
                                ugcGame.pvCount.toString()
                            ),
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_WECHAT_FRIEND -> {
                val (title, content) = GlobalShareCreateConfigHelper.getUgcGameTitleContent(
                    s.config,
                    data,
                    ugcGame
                )
                MetaShare.share(
                    activity,
                    ShareData.wechatFriendWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = CacheCdnImageTask.CDN_APP_ICON_144,
                            title = title ?: activity.getString(
                                R.string.share_game_card_title,
                                GlobalContext.get()
                                    .get<AccountInteractor>().curNameOrNull.orEmpty(),
                                ugcGame.ugcGameName.orEmpty()
                            ),
                            desc = content ?: activity.getString(
                                R.string.share_game_card_content,
                                ugcGame.author?.name.orEmpty(),
                                ugcGame.pvCount.toString()
                            ),
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_QQ_FRIEND -> {
                val (title, content) = GlobalShareCreateConfigHelper.getUgcGameTitleContent(
                    s.config,
                    data,
                    ugcGame
                )
                MetaShare.share(
                    activity,
                    ShareData.qqFriendWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = CacheCdnImageTask.CDN_APP_ICON_144,
                            title = title ?: activity.getString(
                                R.string.share_game_card_title,
                                GlobalContext.get()
                                    .get<AccountInteractor>().curNameOrNull.orEmpty(),
                                ugcGame.ugcGameName.orEmpty()
                            ),
                            desc = content ?: activity.getString(
                                R.string.share_game_card_content,
                                ugcGame.author?.name.orEmpty(),
                                ugcGame.pvCount.toString()
                            ),
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_LONG_IMAGE -> {
                when (data.subPlatform) {
                    SharePlatform.PLATFORM_WECHAT_MOMENT -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.wechatMomentsSingleImage(
                                    requestId,
                                    it
                                )
                            )
                        }
                    }

                    SharePlatform.PLATFORM_QQ_ZONE -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.qqZoneSingleImage(
                                    requestId,
                                    it
                                )
                            )
                        }
                    }

                    SharePlatform.PLATFORM_WECHAT_FRIEND -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.wechatFriendSingleImage(
                                    requestId,
                                    it
                                )
                            )
                        }
                    }

                    SharePlatform.PLATFORM_QQ_FRIEND -> {
                        s.longImagePath?.let {
                            MetaShare.share(
                                activity,
                                ShareData.qqFriendSingleImage(
                                    requestId,
                                    it
                                )
                            )
                        }
                    }
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun shareUgcSupportLongImage(platform: String?): Boolean {
        when (platform) {
            SharePlatform.PLATFORM_WECHAT_MOMENT,
            SharePlatform.PLATFORM_QQ_ZONE,
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            SharePlatform.PLATFORM_QQ_FRIEND -> {
                return true
            }
        }

        return false
    }

    fun sharePost(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String,
        postShareDetail: PostShareDetail,
        scope: CoroutineScope
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_KUAISHOU_PUBLISH -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else if (s.raw.hasVideo) {
                    val videoPath = s.paths.first()
                    MetaShare.share(
                        activity,
                        ShareData.kuaishouPublishSingleVideo(
                            requestId,
                            videoPath,
                            config = s.config
                        )
                    )
                } else {
                    MetaShare.share(
                        activity,
                        ShareData.kuaishouPublishMultiImages(
                            requestId,
                            s.paths,
                            config = s.config
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    val (title, content) = GlobalShareCreateConfigHelper.getPostTitleContent(
                        s.config,
                        data,
                        postShareDetail
                    )
                    if (s.raw.hasVideo) {
                        val videoPath = s.paths.first()
                        MetaShare.share(
                            activity,
                            ShareData.xiaohongshuPublishSingleVideo(
                                requestId,
                                videoPath,
                                config = s.config,
                                title = title,
                                content = content
                            )
                        )
                    } else {
                        MetaShare.share(
                            activity,
                            ShareData.xiaohongshuPublishMultiImages(
                                requestId,
                                s.paths,
                                config = s.config,
                                title = title,
                                content = content
                            )
                        )
                    }
                }
            }

            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                if (s.paths.isNullOrEmpty()) {
                    return GlobalShareViewModel.OPT_PREPARE_FILES
                } else {
                    val (shortTitle, title) = GlobalShareCreateConfigHelper.getPostTitleContent(
                        s.config,
                        data,
                        postShareDetail
                    )
                    if (s.raw.hasVideo) {
                        val videoPath = s.paths.first()
                        MetaShare.share(
                            activity,
                            ShareData.douyinPublishSingleVideo(
                                requestId,
                                videoPath,
                                config = s.config,
                                shortTitle = shortTitle,
                                title = title
                            )
                        )
                    } else {
                        MetaShare.share(
                            activity,
                            ShareData.douyinPublishMultiImages(
                                requestId,
                                s.paths,
                                config = s.config,
                                shortTitle = shortTitle,
                                title = title
                            )
                        )
                    }
                }
            }

            SharePlatform.PLATFORM_WECHAT_MOMENT -> {
                val (title, content) = GlobalShareCreateConfigHelper.getPostTitleContent(
                    s.config,
                    data,
                    postShareDetail
                )
                MetaShare.share(
                    activity,
                    ShareData.wechatMomentsWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = postShareDetail.image.ifNullOrBlank { CacheCdnImageTask.CDN_APP_ICON_144 },
                            title = title ?: activity.getString(R.string.share_post_card_title),
                            desc = content ?: postShareDetail.content,
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_QQ_ZONE -> {
                val (title, content) = GlobalShareCreateConfigHelper.getPostTitleContent(
                    s.config,
                    data,
                    postShareDetail
                )
                MetaShare.share(
                    activity,
                    ShareData.qqZoneWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = postShareDetail.image.ifNullOrBlank { CacheCdnImageTask.CDN_APP_ICON_144 },
                            title = title ?: activity.getString(R.string.share_post_card_title),
                            desc = content ?: postShareDetail.content,
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_KUAISHOU_FRIEND -> {
                val (title, content) = GlobalShareCreateConfigHelper.getPostTitleContent(
                    s.config,
                    data,
                    postShareDetail
                )
                MetaShare.share(
                    activity,
                    ShareData.kuaishouFriendWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = postShareDetail.image.ifNullOrBlank { CacheCdnImageTask.CDN_APP_ICON_144 },
                            title = title ?: activity.getString(R.string.share_post_card_title),
                            desc = content ?:  postShareDetail.content,
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_DOUYIN_FRIEND -> {
                val (shortTitle, title) = GlobalShareCreateConfigHelper.getPostTitleContent(
                    s.config,
                    data,
                    postShareDetail
                )
                MetaShare.share(
                    activity,
                    ShareData.douyinFriendWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = postShareDetail.image.ifNullOrBlank { CacheCdnImageTask.CDN_APP_ICON_144 },
                            title = shortTitle ?: activity.getString(R.string.share_post_card_title),
                            desc = title ?: postShareDetail.content,
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_WECHAT_FRIEND -> {
                val (title, content) = GlobalShareCreateConfigHelper.getPostTitleContent(
                    s.config,
                    data,
                    postShareDetail
                )
                MetaShare.share(
                    activity,
                    ShareData.wechatFriendWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = postShareDetail.image.ifNullOrBlank { CacheCdnImageTask.CDN_APP_ICON_144 },
                            title = title ?: activity.getString(R.string.share_post_card_title),
                            desc = content ?: postShareDetail.content,
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }

            SharePlatform.PLATFORM_QQ_FRIEND -> {
                val (title, content) = GlobalShareCreateConfigHelper.getPostTitleContent(
                    s.config,
                    data,
                    postShareDetail
                )
                MetaShare.share(
                    activity,
                    ShareData.qqFriendWebCard(
                        requestId,
                        ShareData.Sticker(
                            icon = postShareDetail.image.ifNullOrBlank { CacheCdnImageTask.CDN_APP_ICON_144 },
                            title = title ?: activity.getString(R.string.share_post_card_title),
                            desc = content ?: postShareDetail.content,
                            url = data.info?.jumpUrl.orEmpty()
                        )
                    )
                )
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun shareVideo(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String,
        videoFeed: VideoFeedItem,
        scope: CoroutineScope
    ): Int {
        when (data.platform) {

        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun shareOcMoment(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                if (s.raw.hasVideo) {
                    MetaShare.share(
                        activity,
                        ShareData.douyinPublishSingleVideo(
                            requestId,
                            s.raw.video!!,
                            config = s.config
                        )
                    )
                } else if (s.raw.hasImage) {
                    MetaShare.share(
                        activity,
                        ShareData.douyinPublishMultiImages(
                            requestId,
                            s.raw.images!!,
                            config = s.config
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_KUAISHOU_PUBLISH -> {
                if (s.raw.hasVideo) {
                    MetaShare.share(
                        activity,
                        ShareData.kuaishouPublishSingleVideo(
                            requestId,
                            s.raw.video!!,
                            config = s.config
                        )
                    )
                } else if (s.raw.hasImage) {
                    MetaShare.share(
                        activity,
                        ShareData.kuaishouPublishMultiImages(
                            requestId,
                            s.raw.images!!,
                            config = s.config
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                if (s.raw.hasVideo) {
                    MetaShare.share(
                        activity,
                        ShareData.xiaohongshuPublishSingleVideo(
                            requestId,
                            s.raw.video!!,
                            config = s.config
                        )
                    )
                } else if (s.raw.hasImage) {
                    MetaShare.share(
                        activity,
                        ShareData.xiaohongshuPublishMultiImages(
                            requestId,
                            s.raw.images!!,
                            config = s.config
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_QQ_ZONE -> {
                if (s.raw.hasVideo) {
                    MetaShare.share(
                        activity,
                        ShareData.qqZoneSingleVideo(
                            requestId,
                            s.raw.video!!
                        )
                    )
                } else if (s.raw.hasImage) {
                    MetaShare.share(
                        activity,
                        ShareData.qqZoneMultiImages(
                            requestId,
                            s.raw.images!!
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_QQ_FRIEND -> {
                if (s.raw.hasVideo) {
                    MetaShare.share(
                        activity,
                        ShareData.qqFriendSingleVideo(
                            requestId,
                            s.raw.video!!
                        )
                    )
                } else if (s.raw.hasImage) {
                    MetaShare.share(
                        activity,
                        ShareData.qqFriendMultiImages(
                            requestId,
                            s.raw.images!!
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_WECHAT_MOMENT -> {
                if (s.raw.hasVideo) {
                    MetaShare.share(
                        activity,
                        ShareData.wechatFriendSingleVideo(
                            requestId,
                            s.raw.video!!
                        )
                    )
                } else if (s.raw.hasImage) {
                    MetaShare.share(
                        activity,
                        ShareData.wechatMomentsSingleImage(
                            requestId,
                            s.raw.images!!.first()
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_WECHAT_FRIEND -> {
                if (s.raw.hasVideo) {
                    MetaShare.share(
                        activity,
                        ShareData.wechatFriendSingleVideo(
                            requestId,
                            s.raw.video!!
                        )
                    )
                } else if (s.raw.hasImage) {
                    MetaShare.share(
                        activity,
                        ShareData.wechatFriendMultiImages(
                            requestId,
                            s.raw.images!!
                        )
                    )
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun shareScreenshot(
        fragment: Fragment,
        activity: FragmentActivity,
        s: GlobalShareState,
        data: SharePendingData,
        requestId: String
    ): Int {
        when (data.platform) {
            SharePlatform.PLATFORM_WECHAT_FRIEND -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.wechatFriendSingleImage(
                            requestId,
                            it
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_QQ_FRIEND -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.qqFriendSingleImage(
                            requestId,
                            it
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_WECHAT_MOMENT -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.wechatMomentsSingleImage(
                            requestId,
                            it
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_QQ_ZONE -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.qqZoneSingleImage(
                            requestId,
                            it
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.xiaohongshuPublishSingleImage(
                            requestId,
                            it,
                            config = s.config
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.douyinPublishSingleImage(
                            requestId,
                            it,
                            config = s.config
                        )
                    )
                }
            }

            SharePlatform.PLATFORM_KUAISHOU_PUBLISH -> {
                s.longImagePath?.let {
                    MetaShare.share(
                        activity,
                        ShareData.kuaishouPublishSingleImage(
                            requestId,
                            it,
                            config = s.config
                        )
                    )
                }
            }
        }

        return GlobalShareViewModel.OPT_UNSUPPORTED
    }

    fun shareAvatar(
        fragment: Fragment,
        activity: FragmentActivity,
        s: AvatarShareViewModelState,
        platform: String,
        requestId: String,
        images: List<File>
    ) {
        when (platform) {
            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                MetaShare.share(
                    activity,
                    ShareData.douyinPublishMultiImages(
                        requestId,
                        images.map { it.absolutePath },
                        config = s.config
                    )
                )
            }

            SharePlatform.PLATFORM_KUAISHOU_PUBLISH -> {
                MetaShare.share(
                    activity,
                    ShareData.kuaishouPublishMultiImages(
                        requestId,
                        images.map { it.absolutePath },
                        config = s.config
                    )
                )
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                MetaShare.share(
                    activity,
                    ShareData.xiaohongshuPublishMultiImages(
                        requestId,
                        images.map { it.absolutePath },
                        config = s.config
                    )
                )
            }
        }
    }

    fun longImageScreenshot(
        raw: ShareRawData,
        vm: GlobalShareViewModel,
        container: View,
        isPortrait: Boolean,
        mixin: IGlobalShareDialogMixin
    ) {
        val screenshot = raw.image
        if (screenshot.isNullOrBlank()) {
            vm.longImageFail()
            return
        }
        val tempKey = vm.longImageLoading(listOf(screenshot))
        val tempBinding = ViewShareScreenshotBinding.bind(container)
        val qrCodeBitmap = QRCode.newQRCodeUtil()
            .margin("0")
            .content(ShareHelper.downloadPage)
            .size(tempBinding.dp(150))
            .build()
        tempBinding.ivLongImageQrCode.setImageBitmap(qrCodeBitmap)
        mixin.longImageHelper3(
            raw,
            tempKey,
            screenshot,
            tempBinding.ivLongImageScreenshot,
            tempBinding.root
        )
    }
}