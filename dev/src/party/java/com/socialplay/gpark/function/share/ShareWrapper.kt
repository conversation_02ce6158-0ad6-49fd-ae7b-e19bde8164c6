package com.socialplay.gpark.function.share

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.core.content.FileProvider
import com.bytedance.sdk.open.douyin.api.DouYinOpenApi
import com.kwai.opensdk.sdk.model.base.BaseReq
import com.kwai.opensdk.sdk.openapi.IKwaiOpenAPI
import com.kwai.opensdk.sdk.utils.KwaiPlatformUtil
import com.meta.biz.mgs.data.model.MgsGameShareResult
import com.meta.pandora.data.entity.Event
import com.meta.share.util.InstallUtil
import com.meta.web.contract.model.WebShareNativeParams
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.share.ShareData
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.function.analytics.EventConstantsWrapper
import com.socialplay.gpark.function.share.callback.DouYinShareCallbackActivity
import com.socialplay.gpark.function.share.callback.KuaishouShareCallbackActivity
import com.socialplay.gpark.function.share.callback.QQShareCallbackActivity
import com.socialplay.gpark.function.share.callback.WXShareCallbackActivity
import com.socialplay.gpark.function.share.callback.XhsShareCallbackActivity
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.function.share.platform.SystemShare
import com.socialplay.gpark.util.InstallUtil.PACKAGE_DOUYIN
import com.socialplay.gpark.util.InstallUtil.PACKAGE_DOUYIN_LITE
import com.socialplay.gpark.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

object ShareWrapper {

    const val TTAI_ID_GAME_SHARE_CONFIG = 23303
    const val TTAI_ID_SCENE_SHARE_CONFIG = 25040201

    const val OFFICIAL_WEBSITE = "https://233party.com/"
    const val PARTY_TAG = "233Party"

    const val THIRD_PARAMS = "thirdParams"

    fun getRecordShareChannelList(): MutableList<RecordShareChannelInfo> {
        return mutableListOf(
            RecordShareChannelInfo.Community,
            RecordShareChannelInfo.Kuaishou,
            RecordShareChannelInfo.Xiaohongshu,
            RecordShareChannelInfo.Douyin,
            RecordShareChannelInfo.WechatFriend,
            RecordShareChannelInfo.QQFriend,
        )
    }

    sealed class RecordShareChannelInfo(
        @StringRes val name: Int,
        @DrawableRes val icon: Int,
        val platform: List<ComponentName>?,
        val videoTitleUseTextField: Boolean = false,
        val sharePlatform: SharePlatform? = null
    ) {
        object Community : RecordShareChannelInfo(R.string.community, R.drawable.ic_share_post, null)
        object Kuaishou : RecordShareChannelInfo(0, 0, null, sharePlatform = SharePlatform.kuaishou())
        object Douyin : RecordShareChannelInfo(0, 0, null, sharePlatform = SharePlatform.douyin())
        object QQFriend : RecordShareChannelInfo(0, 0, null, sharePlatform = SharePlatform.qqFriend())
        object WechatFriend : RecordShareChannelInfo(0, 0, null, sharePlatform = SharePlatform.wechatFriend())
        object Xiaohongshu : RecordShareChannelInfo(0, 0, null, sharePlatform = SharePlatform.xiaohongshu())
    }

    /**
     * 分享到系统
     */
    fun shareVideo(context: Activity, videoUri: Uri, shareChannel: RecordShareChannelInfo, index: Int, shareText: String = "") {
        val platform = shareChannel.platform!![index]
        when (platform) {
            else -> SystemShare.shareVideoToPlatform(context, videoUri, shareChannel, index, shareText)
        }
    }

    fun share(context: Activity, shareData: ShareData) {
        val cls: Class<*>
        when (shareData.platform) {
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            SharePlatform.PLATFORM_WECHAT_MOMENT -> {
                if (InstallUtil.isWeChatInstalled(context)) {
                    cls = WXShareCallbackActivity::class.java
                } else {
                    shareData.notifyFail(
                        ShareHelper.CODE_NOT_INSTALLED,
                        null
                    )
                    return
                }
            }

            SharePlatform.PLATFORM_QQ_FRIEND,
            SharePlatform.PLATFORM_QQ_ZONE -> {
                if (InstallUtil.isQQFamiliesInstalled(context).first) {
                    cls = QQShareCallbackActivity::class.java
                } else {
                    shareData.notifyFail(
                        ShareHelper.CODE_NOT_INSTALLED,
                        null
                    )
                    return
                }
            }

            SharePlatform.PLATFORM_DOUYIN_PUBLISH,
            SharePlatform.PLATFORM_DOUYIN_FRIEND -> {
                if (com.socialplay.gpark.util.InstallUtil.isInstalledDouyinFamilies4ShareSdk(context).first) {
                    cls = DouYinShareCallbackActivity::class.java
                } else {
                    shareData.notifyFail(
                        ShareHelper.CODE_NOT_INSTALLED,
                        null
                    )
                    return
                }
            }

            SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
            SharePlatform.PLATFORM_KUAISHOU_FRIEND -> {
                if (com.socialplay.gpark.util.InstallUtil.isInstalledKuaishouFamilies(context).first) {
                    cls = KuaishouShareCallbackActivity::class.java
                } else {
                    shareData.notifyFail(
                        ShareHelper.CODE_NOT_INSTALLED,
                        null
                    )
                    return
                }
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                if (com.socialplay.gpark.util.InstallUtil.isInstalledXhs(context)) {
                    cls = XhsShareCallbackActivity::class.java
                } else {
                    shareData.notifyFail(
                        ShareHelper.CODE_NOT_INSTALLED,
                        null
                    )
                    return
                }
            }

            SharePlatform.PLATFORM_SYSTEM -> {
                when (shareData.mode) {
                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        val videoPath = shareData.videos?.firstOrNull()
                        if (videoPath.isNullOrEmpty()) {
                            shareData.notifyFail(
                                ShareHelper.CODE_INVALID_PARAMS,
                                null
                            )
                            return
                        }
                        SystemShare.shareVideoPathBySystem(
                            context,
                            videoPath
                        )
                    }

                    ShareHelper.MODE_SINGLE_IMAGE,
                    ShareHelper.MODE_MULTI_IMAGES -> {
                        if (shareData.images.isNullOrEmpty()) {
                            shareData.notifyFail(
                                ShareHelper.CODE_INVALID_PARAMS,
                                null
                            )
                            return
                        }
                        SystemShare.shareImagePathsBySystem(
                            context,
                            shareData.images
                        )
                    }

                    else -> {
                        shareData.notifyFail(
                            ShareHelper.CODE_UNSUPPORTED_MODE,
                            null
                        )
                        return
                    }
                }
                return
            }

            else -> {
                shareData.notifyFail(
                    ShareHelper.CODE_UNSUPPORTED_PLATFORM,
                    null
                )
                return
            }
        }
        val intent = Intent(context, cls)
            .putExtra(THIRD_PARAMS, shareData.toThirdShareParams())
            .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    }

    fun sharePlatformToTypeId(platform: String): String? {
        return when (platform) {
//            SharePlatform.PLATFORM_TIKTOK -> "1"
//            SharePlatform.PLATFORM_YOUTUBE -> "2"
//            SharePlatform.PLATFORM_SNAPCHAT -> "3"
            else -> null
        }
    }

    fun checkInstallation(context: Context, platform: String): Boolean {
        return when (platform) {
//            SharePlatform.PLATFORM_YOUTUBE -> {
//                InstallUtil.isInstalledYoutube(context)
//            }
//
//            SharePlatform.PLATFORM_TIKTOK -> {
//                InstallUtil.isInstalledTiktokFamilies(context)
//            }
//
//            SharePlatform.PLATFORM_SNAPCHAT -> {
//                InstallUtil.isInstalledSnapchat(context)
//            }

            else -> {
                true
            }
        }
    }

    //后端定好的 分享到SNAPCHAT的请求参数
    const val TYPE_SNAPCHAT = "SNAPCHAT"

    fun mgsShare(shareChannel: String, activity: Activity?, shareInfo: MgsGameShareResult?, metaApp: Context): Boolean {
        if (shareInfo == null) {
            ToastUtil.gameShowShort(metaApp.getString(R.string.mgs_room_un_create))
            return false
        }
        when (shareChannel) {
            TYPE_SNAPCHAT -> {
                mgsShareBySnapchat(activity, shareInfo, metaApp)
            }

            else -> {
                return false
            }
        }
        return true
    }

    fun mgsShareBySnapchat(
        activity: Activity?,
        shareInfo: MgsGameShareResult,
        metaApp: Context
    ) {
        activity?.let {
            val sendIntent: Intent = Intent().apply {
                `package` = "com.snapchat.android"
                action = Intent.ACTION_SEND
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, shareInfo.jumpUrl)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            if (sendIntent.resolveActivity(activity.packageManager) != null) {
                activity.startActivity(sendIntent)
            } else {
                ToastUtil.gameShowShort(metaApp.getString(R.string.msg_invite_snapchat_not_installed))
            }
        }

    }

    fun getMgsShareAnalyticsEvent(shareType: String): Event? {
        return when (shareType) {
            TYPE_SNAPCHAT -> {
                EventConstantsWrapper.EVENT_INVITE_SNAPCHAT_CLICK
            }

            else -> {
                null
            }
        }
    }



    fun kwaiGenerateFileUriPath(activity: Activity, file: File, req: BaseReq, kwaiOpenAPI: IKwaiOpenAPI): String? {
        var filePath: String? = null
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
                && kwaiOpenAPI.isAppSupportUri(activity.applicationContext, req)
            ) {
                val providerName: String = activity.packageName + ".fileprovider"
                val fileUri = FileProvider.getUriForFile(
                    activity.applicationContext,
                    providerName, file
                )
                activity.grantUriPermission(
                    KwaiPlatformUtil.getPackageNameByReq(activity, req), fileUri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )
                filePath = fileUri.toString()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return filePath
    }

    fun douyinGenerateFileUriPath(activity: Activity, file: File, douyinOpenAPI: DouYinOpenApi): String? {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
                && douyinOpenAPI.isShareSupportFileProvider
            ) {
                val providerName: String = activity.packageName + ".fileprovider"
                val fileUri = FileProvider.getUriForFile(
                    activity.applicationContext,
                    providerName, file
                )
                activity.grantUriPermission(
                    PACKAGE_DOUYIN,
                    fileUri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )
                activity.grantUriPermission(
                    PACKAGE_DOUYIN_LITE,
                    fileUri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )
                return fileUri.toString()
            } else if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
                return file.absolutePath
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    fun mapScene2ConfigScene(scene: String): String {
        return when (scene) {
            ShareHelper.SCENE_PROFILE -> {
                "personalPage"
            }

            ShareHelper.SCENE_PGC_DETAIL -> {
                "detailPgcTs"
            }

            ShareHelper.SCENE_UGC_DETAIL -> {
                "detailUgc"
            }

            ShareHelper.SCENE_POST_DETAIL -> {
                "communityPost"
            }

            ShareHelper.SCENE_SCREENSHOT -> {
                "screenShot"
            }

            ShareHelper.SCENE_OC_MOMENT -> {
                "moments"
            }

            ShareHelper.SCENE_AVATAR_SCREENSHOT -> {
                "avatar"
            }

            else -> {
                scene
            }
        }
    }

    suspend fun webShare(
        activity: Activity,
        nativeParams: WebShareNativeParams,
        webShareParam: com.meta.lib.web.core.model.WebShareParam
    ) {
        val imageUrl = webShareParam.imgUrl ?: ""
        val gameId = if (webShareParam.needBackGame) nativeParams.gameId else null

        withContext(Dispatchers.Main) {
            webShareParam.let {
                val shareData = when (it.shareType) {
                    1 -> {
                        ShareData.wechatFriendWebCard(
                            "",
                            ShareData.Sticker(
                                icon = imageUrl,
                                title = it.title,
                                desc = it.desc,
                                url = it.targetUrl
                            ),
                            gameId = gameId
                        )
                    }

                    2 -> {
                        ShareData.wechatMomentsWebCard(
                            "",
                            ShareData.Sticker(
                                icon = imageUrl,
                                title = it.title,
                                desc = it.desc,
                                url = it.targetUrl
                            ),
                            gameId = gameId
                        )
                    }

                    3 -> {
                        ShareData.qqFriendWebCard(
                            "",
                            ShareData.Sticker(
                                icon = imageUrl,
                                title = it.title,
                                desc = it.desc,
                                url = it.targetUrl
                            ),
                            gameId = gameId
                        )
                    }

                    4 -> {
                        ShareData.qqZoneWebCard(
                            "",
                            ShareData.Sticker(
                                icon = imageUrl,
                                title = it.title,
                                desc = it.desc,
                                url = it.targetUrl
                            ),
                            gameId = gameId
                        )
                    }

                    5 -> null

                    else -> null
                }
                shareData?.let {
                    MetaShare.share(activity, shareData)
                }
            }
        }
    }
}
