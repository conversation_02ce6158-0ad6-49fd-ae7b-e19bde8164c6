import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.function.auth.oauth.platform.LeYuanOAuthPlatform
import com.socialplay.gpark.function.auth.oauth.platform.OAuthPlatform
import com.socialplay.gpark.function.auth.oauth.platform.QQOAuthPlatform
import com.socialplay.gpark.function.auth.oauth.platform.WechatOAuthPlatform
import timber.log.Timber

/**
 * @author: ning.wang
 * @date: 2021-09-23 8:16 下午
 * @desc:
 */
class OAuthPlatformProvide {

    private val platformMap = mutableMapOf<String, OAuthPlatform>()

    fun getOAuthPlatform(loginWay: LoginWay): OAuthPlatform? {
        if (platformMap.containsKey(loginWay.way)) return platformMap.getValue(loginWay.way)

        val loginPlatform = when (loginWay) {
            LoginWay.Wechat -> WechatOAuthPlatform()
            LoginWay.QQ -> QQOAuthPlatform()
            LoginWay.Leyuan -> LeYuanOAuthPlatform()
            else -> {
                Timber.e("unsupported sign in method, type:${loginWay.way}")
                null
            }
        }
//        loginPlatform?.let {
//            platformMap[loginWay.way] = loginPlatform
//        }
        return loginPlatform
    }
}