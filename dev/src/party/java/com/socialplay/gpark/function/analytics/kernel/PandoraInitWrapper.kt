package com.socialplay.gpark.function.analytics.kernel

import android.annotation.SuppressLint
import android.app.Application
import com.meta.pandora.PandoraConfig
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.interactor.DeviceInteractor
import com.socialplay.gpark.di.CommonParamsProvider
import org.koin.core.context.GlobalContext

object PandoraInitWrapper {
    @SuppressLint("StaticFieldLeak")
    private val deviceInteractor = GlobalContext.get().get<DeviceInteractor>()

    fun getABPublicParams(it: CommonParamsProvider): MutableMap<String, Any> {
        val params = HashMap<String, Any>()
        params.put("imei", deviceInteractor.imei)
//        params.put("superGameId", it.superGameId)
//        params.put("superGamePackage", it.superGamePackage)
        params.put("oaId", deviceInteractor.oaid)
        params.put("channelId", deviceInteractor.channelId)
        params.put("deviceTime", it.deviceTime)
        params.put("smid", deviceInteractor.smid)
        params.put("refactor_version", 1)

//        params.put("userStatus", it.userStatus)
        params.put("apkChannelId", deviceInteractor.apkChannelId)
//        params.put("isLockLocation", it.isLockLocation)
//        params.put("kernel_version", it.kernelVersion)
//        params.put("linuxKernelVersion", deviceInteractor.linuxKernelVersion)
        params.put("tracking", deviceInteractor.metaTracking)
//        params.put("ug_link_id", deviceInteractor.ugLinkId)
//        params.put("ug_plan_id", deviceInteractor.ugPlanId)
//        params.put("device_risk", deviceInteractor.deviceRisk)
//        if (PandoraToggle.isBoutique()) {
//            var boutiqueParams = PandoraToggle.getBoutiqueParams()
//            if(!it.adult.isNullOrEmpty()){
//                boutiqueParams += "_${it.adult}"
//            }
//            params.put("app_style", boutiqueParams)
//        }else if (!it.adult.isNullOrEmpty()){
//            params.put("app_style", it.adult)
//        }
        return params
    }

    fun getPublicParams(it: CommonParamsProvider): MutableMap<String, Any> {
        val params = HashMap<String, Any>()

        params.put("imei", deviceInteractor.imei)
//        params.put("superGameId", it.superGameId)
//        params.put("superGamePackage", it.superGamePackage)
        params.put("oaId", deviceInteractor.oaid)
        params.put("channelId", deviceInteractor.channelId)
        params.put("apk_channel_id", deviceInteractor.apkChannelId)
        params.put("deviceTime", it.deviceTime)
        params.put("smid", deviceInteractor.smid)
        params.put("iosAndroid", it.iosAndroid)
        params.put("refactor_version", 1)
        params.put("tracking", deviceInteractor.metaTracking)
//        params.put("ug_link_id", deviceInteractor.ugLinkId)
//        params.put("ug_plan_id", deviceInteractor.ugPlanId)
//        params.put("device_risk", deviceInteractor.deviceRisk)
        return params
    }

    fun getServer(): PandoraConfig.Server {
        return PandoraConfig.Server.CHINA
    }

    fun init(context: Application) {
    }
}

fun PandoraConfig.Builder.setFlavorConfig(): PandoraConfig.Builder {
    return this.appVersion(BuildConfig.VERSION_NAME)
        .enableIPv6Reporter()
        .enableDynamicDomain()
}