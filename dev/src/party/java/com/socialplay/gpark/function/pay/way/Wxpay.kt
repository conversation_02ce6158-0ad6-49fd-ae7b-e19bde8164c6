package com.socialplay.gpark.function.pay.way

import android.app.Activity
import android.app.Application
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.socialplay.gpark.BuildConfig
import org.koin.core.context.GlobalContext


object Wxpay {
    const val KEY_PARTNER_ID = "partnerId"
    const val KEY_PREPAY_ID = "prepayId"
    const val KEY_NONCE_STR = "nonceStr"
    const val KEY_TIMESTAMP = "timeStamp"
    const val KEY_SIGN = "sign"
    const val KEY_EXT_DATA = "extData"
    const val KEY_GAME_PKG_NAME = "gamePkgName"

    fun startPay(activity: Activity?,partnerId: String?, prepayId: String?, nonceStr: String?, timeStamp: String?, sign: String?, extData: String?) {
        // 微信支付callback在 com.meta.xyx.wxapi.WXPayEntryActivity.onResp方法中
        activity?.startActivity(
            Intent().apply {
                val bundle = Bundle()
                    .apply {
                        putString(KEY_PARTNER_ID, partnerId)
                        putString(KEY_PREPAY_ID, prepayId)
                        putString(KEY_NONCE_STR, nonceStr)
                        putString(KEY_TIMESTAMP, timeStamp)
                        putString(KEY_SIGN, sign)
                        putString(KEY_EXT_DATA, extData)
                        if (!activity.packageName.equals(BuildConfig.APPLICATION_ID)) {
                            putString(KEY_GAME_PKG_NAME, activity.packageName)
                        }
                    }
                putExtras(bundle)
                val metaApp: Context = GlobalContext.get().get() as Application
                component = ComponentName(metaApp.packageName,
                    StartWeChatPayActivity::class.java.name)
                setPackage(metaApp.packageName)
            })
    }
}