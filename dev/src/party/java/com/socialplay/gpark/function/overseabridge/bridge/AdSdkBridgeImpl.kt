package com.socialplay.gpark.function.overseabridge.bridge

import android.app.Activity
import android.app.Application
import android.view.ViewGroup
import androidx.lifecycle.Lifecycle
import com.socialplay.gpark.function.ad.AdToggleCtrl
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.ui.ad.AdType
import com.socialplay.gpark.ui.ad.TestRewardedAdActivity

class AdSdkBridgeImpl : IAdSdkBridge {

    override fun isEnable(): Boolean {
        return false
    }

    private val gameRewardAdId: String
        get() {
            return AdToggleCtrl.getAdTtaiConfig()?.gameRewardId ?: ""
        }

    override fun preInit(application: Application, enableLog: Boolean) {
    }

    override fun init() {
    }

    override fun addInitCallback(runnable: Runnable?) {
    }

    override fun preLoadAds(gameId: String?, gamePkg: String?, activity: Activity) {
    }


    override fun isRewardedAdReady(): Boolean {
        return true
    }

    override fun showRewardedAd(
        activity: Activity,
        container: ViewGroup,
        lifecycle: Lifecycle,
        placement: String,
        gameId: String?,
        gamePkg: String?,
        callback: IAdSdkBridge.VideoAdCallback?
    ) {
        val intent = TestRewardedAdActivity.createIntent(
            activity,
            StartupProcessType.H.desc,
            AdType.REWARDED,
            gameId = gameId,
            gamePkg = gamePkg,
            hashMapOf(),
            callback
        )
        activity.startActivity(intent)
    }

    override fun showMediationDebugger(activity: Activity) {
    }
}