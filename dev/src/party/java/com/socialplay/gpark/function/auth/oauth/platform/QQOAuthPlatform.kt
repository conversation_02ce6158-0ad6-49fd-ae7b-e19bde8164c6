package com.socialplay.gpark.function.auth.oauth.platform

import android.app.Activity
import android.app.Application
import android.content.Intent
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.function.auth.oauth.OAuthManager
import com.socialplay.gpark.function.auth.oauth.QQCallbackActivity
import com.socialplay.gpark.util.InstallUtil
import org.koin.core.component.KoinComponent
import org.koin.core.context.GlobalContext

/**
 * @author: ning.wang
 * @date: 2021-09-23 8:21 下午
 * @desc:
 */
class QQOAuthPlatform : OAuthPlatform, KoinComponent {

    companion object {
        private const val TAG = "QQOAuthPlatform"
    }

    override fun login(activity: Activity?, source: String?, loginType: LoginType?) {

        val context = activity ?: (GlobalContext.get().get() as Application)

        if (!InstallUtil.isInstalledQQ(context)) {
            OAuthManager.callbacks.dispatchOnMainThread {
                this.onFailed(LoginWay.QQ, context.getString(R.string.withdraw_qq_not_install), -1)
            }
            return
        }
        val intent = Intent(context, QQCallbackActivity::class.java)
        intent.putExtra("auth_source", source)
        intent.putExtra("auth_type", loginType?.type)
        context.startActivity(intent)
    }

    override fun logout() {
        val context = (GlobalContext.get().get() as Application)
    }

}