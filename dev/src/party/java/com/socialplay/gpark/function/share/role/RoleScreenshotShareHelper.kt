package com.socialplay.gpark.function.share.role

import androidx.fragment.app.FragmentActivity
import com.socialplay.gpark.data.model.share.ShareConfig
import com.socialplay.gpark.data.model.share.ShareData
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.function.pandora.PandoraToggleWrapper
import com.socialplay.gpark.function.share.MetaShare
import com.socialplay.gpark.function.share.ShareWrapper

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/22
 *     desc   :
 * </pre>
 */
object RoleScreenshotShareHelper {

    fun initThirdPartyPlatforms(list: MutableList<SharePlatform>) {
        list.add(SharePlatform.kuaishou())
        list.add(SharePlatform.xiaohongshu())
        list.add(SharePlatform.douyin())
        list.add(SharePlatform.save())
        list.add(SharePlatform.system())
    }

    fun share(
        activity: FragmentActivity,
        platform: String,
        images: List<String>,
        config: ShareConfig?,
        requestId: String,
    ) {
        when (platform) {
            SharePlatform.PLATFORM_DOUYIN_PUBLISH -> {
                MetaShare.share(
                    activity,
                    ShareData.douyinPublishMultiImages(
                        requestId,
                        images,
                        config
                    )
                )
            }

            SharePlatform.PLATFORM_KUAISHOU_PUBLISH -> {
                MetaShare.share(
                    activity,
                    ShareData.kuaishouPublishMultiImages(
                        requestId,
                        images,
                        config
                    )
                )
            }

            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                MetaShare.share(
                    activity,
                    ShareData.xiaohongshuPublishMultiImages(
                        requestId,
                        images,
                        config
                    )
                )
            }
        }
    }
}