package com.socialplay.gpark.function.share

import com.socialplay.gpark.data.model.share.SharePlatform

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/01/22
 *     desc   :
 * </pre>
 */
object GlobalShareTrackHelper {

    fun trackResultParam(
        platform: String?,
        map: HashMap<String, Any>,
        extra: String?
    ) {
        when (platform) {
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            SharePlatform.PLATFORM_WECHAT_MOMENT,
            SharePlatform.PLATFORM_QQ_FRIEND,
            SharePlatform.PLATFORM_QQ_ZONE,
            SharePlatform.PLATFORM_DOUYIN_PUBLISH,
            SharePlatform.PLATFORM_DOUYIN_FRIEND,
            SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
            SharePlatform.PLATFORM_KUAISHOU_FRIEND,
            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                if (extra != null) {
                    map["extra"] = platform
                }

            }
        }
    }

    fun trackAvatarShareResult(platform: String): <PERSON><PERSON><PERSON> {
        return when (platform) {
            SharePlatform.PLATFORM_LINK,
            SharePlatform.PLATFORM_WECHAT_FRIEND,
            SharePlatform.PLATFORM_WECHAT_MOMENT,
            SharePlatform.PLATFORM_QQ_FRIEND,
            SharePlatform.PLATFORM_QQ_ZONE,
            SharePlatform.PLATFORM_DOUYIN_PUBLISH,
            SharePlatform.PLATFORM_DOUYIN_FRIEND,
            SharePlatform.PLATFORM_KUAISHOU_PUBLISH,
            SharePlatform.PLATFORM_KUAISHOU_FRIEND,
            SharePlatform.PLATFORM_XIAOHONGSHU_PUBLISH -> {
                true
            }

            else -> false
        }
    }
}