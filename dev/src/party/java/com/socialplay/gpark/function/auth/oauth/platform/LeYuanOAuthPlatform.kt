package com.socialplay.gpark.function.auth.oauth.platform

import android.app.Activity
import android.app.Application
import com.meta.sdk.open.OpenApi
import com.meta.sdk.open.model.AuthReq
import com.meta.sdk.open.model.SdkErrorCode
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.function.auth.oauth.OAuthManager
import com.socialplay.gpark.util.InstallUtil
import com.tencent.mm.opensdk.openapi.IWXAPI
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * @author: ning.wang
 * @date: 2021-09-23 8:21 下午
 * @desc:
 */
class LeYuanOAuthPlatform : OAuthPlatform, KoinComponent {
    private val wxApi: IWXAPI by inject()

    companion object {
        private const val TAG = "WechatOAuthPlatform"
    }


    override fun login(activity: Activity?, source: String?, loginType: LoginType?) {
        val context = activity ?: (GlobalContext.get().get() as Application)
//        // 这里不拦截，因为SDK自己会跳转网页登录
//        if (!InstallUtil.isInstalledLeYuan(context)) {
//            OAuthManager.callbacks.dispatchOnMainThread {
//                this.onFailed(LoginWay.Leyuan, context.getString(R.string.withdraw_leyuan_not_install), -1)
//            }
//            return
//        }
        val authRequest = AuthReq().apply {
            state = "party_auth"
            needVisitor = false
        }
        OpenApi.getInstance().authorize(activity, authRequest) { resp ->
            if (resp == null) {
                onFailed(context.getString(R.string.oauth_leyuan_failed_complete))
                return@authorize
            }
            Timber.d("233 authorize resp: ${resp.errorCode} ${resp.errorMessage} ")
            if (resp.isOk) {
                onSuccess(resp.accessToken, source, loginType)
            } else {
                if (resp.errorCode == SdkErrorCode.CODE_CANCEL_BY_USER) {
                    OAuthManager.callbacks.dispatchOnMainThread {
                        this.onCancel(LoginWay.Leyuan)
                    }
                } else {
                    onFailed(resp.errorMessage ?: context.getString(R.string.oauth_leyuan_failed_complete))
                }
            }
        }
    }

    override fun logout() {
        val context = (GlobalContext.get().get() as Application)
    }

    private fun onFailed(message: String) {
        OAuthManager.callbacks.dispatchOnMainThread {
            this.onFailed(LoginWay.Leyuan, message, 0)
        }
    }

    private fun onSuccess(authCode: String, sourceStr: String?, mloginType: LoginType?) {
        OAuthManager.callbacks.dispatchOnMainThread {
            this.onSuccess(OAuthResponse(LoginWay.Leyuan, authCode, "").apply {
                source = sourceStr
                loginType = mloginType?.type
            })
        }
    }
}