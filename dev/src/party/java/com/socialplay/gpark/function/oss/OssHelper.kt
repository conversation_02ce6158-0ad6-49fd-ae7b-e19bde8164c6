package com.socialplay.gpark.function.oss

import com.meta.aliyun.AliyunUpload
import com.meta.upload.core.MetaUpload
import com.tencent.cos.xml.BuildConfig
import org.koin.core.context.GlobalContext

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2025/02/05
 *     desc   :
 * </pre>
 */
object OssHelper {

    fun init() {
        // 可选多个供应商
        MetaUpload.init(
            GlobalContext.get().get(),
            listOf(AliyunUpload()),
            false,
            BuildConfig.DEBUG
        )
    }
}