package com.socialplay.gpark.function.analytics

import com.meta.pandora.data.entity.Event

object EventConstantsWrapper {

    @EventDesc("邀请好友_snapchat_点击")
    val EVENT_INVITE_SNAPCHAT_CLICK = Event("c_invite_snapchat_click")

    @EventDesc("策略触发")
    val UPDATE_START = Event("update_start")

    @EventDesc("开始下载")
    val UPDATE_START_DOWNLOAD = Event("update_start_download")

    @EventDesc("下载完成")
    val UPDATE_DOWNLOAD_FINISH = Event("update_download_finish")

    @EventDesc("更新流程结果")
    val UPDATE_DO_RESULT = Event("update_do_result")

    @EventDesc("更新弹窗弹出")
    val UPDATE_DIALOG_SHOW = Event("update_dialog_show")

    @EventDesc("更新弹窗点击安装")
    val UPDATE_DIALOG_CLICK_INSTALL = Event("update_dialog_click_install")

    @EventDesc("更新弹窗点击退出")
    val UPDATE_DIALOG_CLICK_DISMISS = Event("update_dialog_click_dismiss")

    @EventDesc("更新弹窗点击忽略")
    val UPDATE_DIALOG_CLICK_IGNORE = Event("update_dialog_click_ignore")

    @EventDesc("更新弹窗点击关闭")
    val UPDATE_DIALOG_CLICK_CLOSE = Event("update_dialog_click_close")

    @EventDesc("检查更新_点击")
    val UPDATE_CHECK_CLICK = Event("update_check_click")
}