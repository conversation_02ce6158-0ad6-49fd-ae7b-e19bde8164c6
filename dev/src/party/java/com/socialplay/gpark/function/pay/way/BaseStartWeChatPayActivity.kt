package com.meta.box.ui.pay

import android.content.Intent
import android.os.Bundle
import androidx.annotation.CallSuper
import com.bin.cpbus.CpEventBus
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.model.pay.AgentPayType
import com.socialplay.gpark.data.model.pay.WechatPayFinish
import com.socialplay.gpark.databinding.ActivityPayAlipayBinding
import com.socialplay.gpark.function.pay.way.GamePayResultEvent
import com.socialplay.gpark.function.pay.way.Wxpay
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.property.viewBinding
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.openapi.IWXAPI
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.koin.java.KoinJavaComponent
import timber.log.Timber

open class BaseStartWeChatPayActivity : BaseActivity() {
    override var canLandscape = true
    override val binding by viewBinding(ActivityPayAlipayBinding::inflate)
    private val api: IWXAPI by KoinJavaComponent.inject(IWXAPI::class.java)
    private var needFinish: Boolean = false
    protected var gamePkgName: String? = null
        private set

    companion object {
        const val TAG = "StartWeChatPay"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Timber.tag(TAG).d("WXPay-----onCreate")
        EventBus.getDefault().register(this)
        handleIntents(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        Timber.tag(TAG).d("WXPay-----onNewIntent")
    }

    private fun handleIntents(intent: Intent) {
        val data = intent.extras
        if (data == null) {
            Timber.tag(TAG).e("handleIntents data is null")
            finish()
            return
        }
        gamePkgName = data.getString(Wxpay.KEY_GAME_PKG_NAME)
        Timber.tag(TAG).i("WXPay-----handleIntents-${gamePkgName}")
        //拉起微信支付
        val request = PayReq().apply {
            appId = BuildConfig.WECHAT_APP_ID
            packageValue = "Sign=WXPay"
            partnerId = data.getString(Wxpay.KEY_PARTNER_ID)
            prepayId = data.getString(Wxpay.KEY_PREPAY_ID)
            nonceStr = data.getString(Wxpay.KEY_NONCE_STR)
            timeStamp = data.getString(Wxpay.KEY_TIMESTAMP)
            sign = data.getString(Wxpay.KEY_SIGN)
            extData = data.getString(Wxpay.KEY_EXT_DATA)
        }
        api.sendReq(request)
    }

    @Subscribe
    fun onEvent(event: WechatPayFinish) {
        Timber.tag(TAG).i("WXPay-----onEvent-${event}")
        onHandleEvent(event)
    }

    @CallSuper
    protected open fun onHandleEvent(event: WechatPayFinish) {
        //支付完成通知对应的进程
        CpEventBus.post(GamePayResultEvent(event.errCode, event.extData, AgentPayType.PAY_TYPE_WX))
    }


    override fun onPause() {
        super.onPause()
        Timber.tag(TAG).d("WXPay-----onPause")
        needFinish = true
    }

    override fun onResume() {
        super.onResume()
        Timber.tag(TAG).d("WXPay-----onResume")
        if (needFinish) finish()
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
        Timber.tag(TAG).d("WXPay-----onDestroy")
    }

    override fun finish() {
        Timber.tag(TAG).i("WXPay-----finish-${gamePkgName}")
        super.finish()
    }

}