package com.socialplay.gpark.function.pay.way

import android.content.Context
import android.os.Bundle
import com.meta.box.ui.pay.BaseStartWeChatPayActivity
import com.socialplay.gpark.data.model.pay.WechatPayFinish
import com.socialplay.gpark.util.extension.activityManager
import org.greenrobot.eventbus.EventBus
import org.koin.core.context.GlobalContext
import timber.log.Timber


/**
 * 单纯为了ab优化后的支付流程的数据
 */
class StartWeChatPayActivity : BaseStartWeChatPayActivity() {

    companion object {
        private var curTaskId : Int? = null
        private var extData: String? = null

        fun resumeActivity() {
            try {
                Timber.tag(TAG).d("moveTaskToFront $curTaskId")
                val taskId = curTaskId ?: return
                GlobalContext.get().get<Context>().activityManager().moveTaskToFront(taskId, 0)
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "resumeActivity moveTaskToFront error $curTaskId")
            }
        }

        /**
         * 处理微信WXPayEntryActivity收不到支付成功回调，但是可以收到失败回调
         */
        fun trackPaySucceed(gamePkg: String, orderCode: String) {
            Timber.tag(TAG).d("trackPaySucceed $extData $gamePkg $orderCode")
            if (extData == orderCode){
                resumeActivity()
                Timber.tag(TAG).d("trackPaySucceed")
                EventBus.getDefault().post(WechatPayFinish(0, orderCode))
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        curTaskId = taskId
        extData = intent.extras?.getString(Wxpay.KEY_EXT_DATA)
        Timber.tag(TAG).d("onCreate curTaskId:$curTaskId")
    }

    override fun onDestroy() {
        super.onDestroy()
        curTaskId = null
        extData = null
    }
}