package com.socialplay.gpark.function.pay.way

import android.app.Activity
import android.app.Application
import android.content.ComponentName
import android.content.Intent
import android.os.Bundle
import com.socialplay.gpark.BuildConfig
import org.koin.core.context.GlobalContext

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/06/17
 *     desc   : 支付宝支付
 *
 */
object AliPay {
    // 支付宝支付成功的状态码，不能随意修改
    const val ALIPAY_PAY_SUCCESS = "9000"
    const val INFO ="orderInfo"
    const val ORDER_ID ="orderId"
    const val GAME_PACKAGE_NAME ="gamePackageName"
    fun startPay(activity: Activity?, orderInfo: String?, orderId: String?) {

        activity?.let {
            val intent = Intent()
            val bundle = Bundle()
            bundle.putString(INFO, orderInfo)
            bundle.putString(ORDER_ID, orderId)
            if (!it.packageName.equals(BuildConfig.APPLICATION_ID)) {
                bundle.putString(GAME_PACKAGE_NAME, it.packageName)
            }
            intent.putExtras(bundle)
            val metaApp = GlobalContext.get().get() as Application
            intent.component = ComponentName(metaApp.packageName,
                StartAliPayActivity::class.java.name)
            intent.setPackage(metaApp.packageName)
            it.startActivity(intent)
        }


    }
}