package com.socialplay.gpark.function.share.callback

import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import com.meta.share.MetaShare
import com.meta.share.util.InstallUtil
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.share.SharePlatform
import com.socialplay.gpark.data.model.share.ShareResult
import com.socialplay.gpark.data.model.share.ThirdShareParams
import com.socialplay.gpark.databinding.ActivityQqCallbackBinding
import com.socialplay.gpark.function.share.ShareWrapper
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.util.FileProviderUtil
import com.socialplay.gpark.util.extension.resumeGameById
import com.socialplay.gpark.util.ifNullOrEmpty
import com.socialplay.gpark.util.property.viewBinding
import com.tencent.connect.common.Constants
import com.tencent.tauth.DefaultUiListener
import com.tencent.tauth.Tencent
import com.tencent.tauth.UiError
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import timber.log.Timber


/**
 * 游戏内qq分享
 */
class QQShareCallbackActivity : BaseActivity() {
    override var canLandscape = true

    private var needBackGame: Int = NEED_BACK_GAME_INIT

    private var mFinishJob: Job? = null
    private var mPauseTs: Long = -1L

    private lateinit var args: ThirdShareParams

    override val binding by viewBinding(ActivityQqCallbackBinding::inflate)

    companion object {
        private const val NEED_BACK_GAME_INIT = 0
        private const val NEED_BACK_GAME_WAIT_ON_PAUSE = 1
        private const val NEED_BACK_GAME_WAIT_ON_RESUME = 2
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val params = intent.getParcelableExtra<ThirdShareParams>(ShareWrapper.THIRD_PARAMS)
        if (params == null) {
            invalidParams()
            return
        }
        args = params

        shareQQ()
    }

    private fun shareQQ() {
        when (args.platform) {
            SharePlatform.PLATFORM_QQ_FRIEND -> {
                when (args.mode) {
                    ShareHelper.MODE_SINGLE_WEB_CARD -> {
                        val webCard = args.sticker
                        if (webCard == null) {
                            invalidParams()
                            return
                        }
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.qqCard(
                                0,
                                webCard.title,
                                webCard.desc,
                                webCard.url,
                                webCard.icon
                            ),
                            ::shareResultCallbackV2
                        )
                    }

                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        val path = args.path
                        if (path.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.qqImage(
                                0,
                                path
                            ),
                            ::shareResultCallbackV2
                        )
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        val paths = args.paths
                        if (paths.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        needBackGame = NEED_BACK_GAME_WAIT_ON_PAUSE
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.qqMultiImages(
                                0,
                                paths.mapNotNull(::getValidPath),
                            ),
                            ::shareResultCallbackV2
                        )
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        val path = args.path
                        if (path.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        needBackGame = NEED_BACK_GAME_WAIT_ON_PAUSE
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.qqVideo(
                                0,
                                getValidPath(path).orEmpty(),
                            ),
                            ::shareResultCallbackV2
                        )
                    }

                    else -> {
                        unsupported()
                        return
                    }
                }
            }

            SharePlatform.PLATFORM_QQ_ZONE -> {
                when (args.mode) {
                    ShareHelper.MODE_SINGLE_WEB_CARD -> {
                        val webCard = args.sticker
                        if (webCard == null) {
                            invalidParams()
                            return
                        }
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.qzoneCard(
                                0,
                                webCard.title,
                                webCard.desc,
                                webCard.url,
                                webCard.icon
                            ),
                            ::shareResultCallbackV2
                        )
                    }

                    ShareHelper.MODE_SINGLE_IMAGE -> {
                        val path = args.path
                        if (path.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.qzoneImage(
                                0,
                                args.content,
                                path
                            ),
                            ::shareResultCallbackV2
                        )
                    }

                    ShareHelper.MODE_MULTI_IMAGES -> {
                        val paths = args.paths
                        if (paths.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.qzoneMultiImages(
                                0,
                                args.content,
                                paths,
                            ),
                            ::shareResultCallbackV2
                        )
                    }

                    ShareHelper.MODE_SINGLE_VIDEO -> {
                        val path = args.path
                        if (path.isNullOrEmpty()) {
                            invalidParams()
                            return
                        }
                        needBackGame = NEED_BACK_GAME_WAIT_ON_PAUSE
                        MetaShare.shareV2(
                            this,
                            MetaShare.ShareData.qzoneVideo(
                                0,
                                args.content,
                                path,
                            ),
                            ::shareResultCallbackV2
                        )
                    }

                    else -> {
                        unsupported()
                        return
                    }
                }
            }

            else -> {
                unsupported()
                return
            }
        }
    }

    private fun getValidPath(path: String?): String? {
        return FileProviderUtil.getValidPathV2(
            this,
            path,
            InstallUtil.PACKAGE_QQ,
            InstallUtil.PACKAGE_TIM,
            InstallUtil.PACKAGE_QQ_HD,
            InstallUtil.PACKAGE_QQ_LITE,
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        Timber.d("onActivityResult ${requestCode}  ${resultCode}  ${data}")
        if (requestCode == Constants.REQUEST_QQ_SHARE || requestCode == Constants.REQUEST_QZONE_SHARE) {
            Tencent.onActivityResultData(requestCode, resultCode, data, shareResultListener())
            finish()
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun finish() {
        args.gameId?.let {
            resumeGameById(it)
        }
        super.finish()
    }

    private fun dispatchShareResultEvent(success: Boolean, err: Any?) {
        if (success) {
            if (err == null || (err as? JSONObject)?.length() == 0) {
                ShareResult.notifyFail(args.reqId, args.platform, -1, getString(R.string.share_fail))
            } else {
                ShareResult.notifySuccess(args.reqId, args.platform)
            }
        } else if (err is UiError) {
            ShareResult.notifyFail(args.reqId, args.platform, err.errorCode, err.errorMessage.ifNullOrEmpty { getString(R.string.share_fail) }, sdkCode1 = err.errorCode)
        } else if (needBackGame == NEED_BACK_GAME_WAIT_ON_PAUSE) {
            ShareResult.notifyFail(args.reqId, args.platform, -1, getString(R.string.share_fail))
            finish()
        } else if (err is String) {
            ShareResult.notifyFail(args.reqId, args.platform, -1, getString(R.string.share_fail))
        } else {
            ShareResult.notifyCancel(args.reqId, args.platform)
        }
    }

    private fun shareResultCallbackV2(
        ts: Long,
        success: Boolean,
        errorMessage: String?
    ) {
        dispatchShareResultEvent(success, errorMessage)
    }

    private fun shareResultListener() = object : DefaultUiListener() {
        override fun onCancel() {
            dispatchShareResultEvent(false, null)
        }

        override fun onComplete(p0: Any?) {
            dispatchShareResultEvent(true, p0)
        }

        override fun onError(p0: UiError?) {
            dispatchShareResultEvent(false, p0)
        }
    }

    private fun unsupported() {
        ShareResult.notifyFail(
            args.reqId,
            args.platform,
            ShareHelper.CODE_UNSUPPORTED_PLATFORM,
            null
        )
        finish()
    }

    private fun invalidParams() {
        ShareResult.notifyFail(
            args.reqId,
            args.platform,
            ShareHelper.CODE_INVALID_PARAMS,
            null
        )
        finish()
    }

    override fun onResume() {
        super.onResume()
        if (needBackGame == NEED_BACK_GAME_WAIT_ON_RESUME) {
            if (SystemClock.elapsedRealtime() - mPauseTs > 200) {
                finish()
            } else {
                mFinishJob?.cancel()
                mFinishJob = lifecycleScope.launch {
                    delay(1_000)
                    if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                        finish()
                    }
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        if (needBackGame == NEED_BACK_GAME_WAIT_ON_PAUSE) {
            needBackGame = NEED_BACK_GAME_WAIT_ON_RESUME
        }
        mPauseTs = SystemClock.elapsedRealtime()
        mFinishJob?.cancel()
        mFinishJob = null
    }
}