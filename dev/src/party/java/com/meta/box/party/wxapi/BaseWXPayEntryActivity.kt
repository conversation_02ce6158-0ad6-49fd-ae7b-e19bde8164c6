package com.meta.box.party.wxapi

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.model.pay.WechatPayFinish
import com.socialplay.gpark.function.pay.way.StartWeChatPayActivity
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelpay.PayResp
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import org.greenrobot.eventbus.EventBus
import timber.log.Timber

/**
 *
 *     author : yi.zhang
 *     e-mail : <EMAIL>
 *     time   : 2021/05/30
 *     desc   : 微信支付结果
 *
 */
abstract class BaseWXPayEntryActivity : AppCompatActivity(), IWXAPIEventHandler {

    private val TAG = "IWXAPIEventHandler"

    private var wxapi: IWXAPI ?=null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Timber.tag(TAG).d("onCreate")
        wxapi = WXAPIFactory.createWXAPI(this, BuildConfig.WECHAT_APP_ID)
        wxapi?.registerApp(BuildConfig.WECHAT_APP_ID)
        wxapi?.handleIntent(intent, this)
    }

    override fun onResume() {
        super.onResume()
        Timber.tag(TAG).d("onResume")
    }

    override fun onPause() {
        super.onPause()
        Timber.tag(TAG).d("onPause")
    }

    override fun onDestroy() {
        wxapi?.detach()
        super.onDestroy()
        Timber.tag(TAG).d("onDestroy")
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        Timber.tag(TAG).d("onNewIntent")
        setIntent(intent)
        wxapi?.handleIntent(intent, this)
    }

    override fun onReq(req: BaseReq?) {
        Timber.tag(TAG).d("onReq")
    }

    override fun onResp(resp: BaseResp) {
        Timber.tag(TAG).d("onResp")
        val errCode = resp.errCode
        if (resp is PayResp) {
            EventBus.getDefault().post(WechatPayFinish(errCode, resp.extData))

            StartWeChatPayActivity.resumeActivity()

          //  Wxpay.sendBroadcast(errCode, payOrderId)
            Timber.tag(TAG).d("MOD_PAY微信支付返回code:$errCode")
        }
        finish()
    }
}