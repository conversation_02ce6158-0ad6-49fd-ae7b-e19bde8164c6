package com.meta.box.party.wxapi

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.mgs.WXShareFinishEvent
import com.socialplay.gpark.data.model.user.OAuthResponse
import com.socialplay.gpark.data.model.user.OauthTransInfo
import com.socialplay.gpark.data.model.user.WXAuthResult
import com.socialplay.gpark.function.auth.oauth.OAuthManager
import com.socialplay.gpark.function.ipc.provider.host.HostIntentStarter
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.util.GsonUtil
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX
import com.tencent.mm.opensdk.modelmsg.ShowMessageFromWX
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.koin.android.ext.android.inject
import timber.log.Timber


/**
 * @author: ning.wang
 * @date: 2021-05-25 2:57 下午
 * @desc:
 */
abstract class BaseWXEntryActivity : AppCompatActivity(), IWXAPIEventHandler {
//    private val uniGameStatusInteractor by inject<UniGameStatusInteractor>()

    companion object {
        private const val TAG = "LeoWnnn_WXEntryActivity"
    }

    private val wxApi: IWXAPI by inject()
//    private val oauthManager: OauthManager by inject()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        wxApi.handleIntent(intent, this)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        wxApi.handleIntent(intent, this)
    }

    /**
     * 微信主动请求我们
     */
    override fun onReq(req: BaseReq) { // 处理公众号拉起233
        if (req.type == ConstantsAPI.COMMAND_SHOWMESSAGE_FROM_WX && req is ShowMessageFromWX.Req) {
            val mediaMsg = req.message
            val extInfo = mediaMsg.messageExt
            Timber.d("$TAG: msg : $mediaMsg, extInfo : $extInfo")

            val uri = Uri.parse(extInfo)
            val oauthTransInfo = getOauthTransInfo(req.transaction)
            MetaRouter.Main.dispatchUrl(this, uri, oauthTransInfo?.loginSource ?: "")
        }
        finish()
    }

    private fun getErrorStr(code: Int): String {
        return when (code) {
            BaseResp.ErrCode.ERR_USER_CANCEL -> getString(R.string.auth_cancelled)
            BaseResp.ErrCode.ERR_SENT_FAILED -> getString(R.string.auth_failed)
            BaseResp.ErrCode.ERR_AUTH_DENIED -> getString(R.string.auth_denied)
            BaseResp.ErrCode.ERR_UNSUPPORT -> getString(R.string.auth_unsupported)
            BaseResp.ErrCode.ERR_BAN -> getString(R.string.auth_banned)
            else -> getString(R.string.auth_failed_code, code)
        }
    }

    /**
     * 请求微信的相应码
     */
    override fun onResp(resp: BaseResp) {
        Timber.d("wechat-onResp-%s-%s-%s", resp.type, resp.errCode, resp)
        lifecycleScope.launch {
            when (resp.type) {
                ConstantsAPI.COMMAND_SENDAUTH -> {
                    val result = when (resp.errCode) {
                        BaseResp.ErrCode.ERR_OK -> {
                            val resp1 = resp as SendAuth.Resp
                            WXAuthResult.authOk(resp1.code)
                        }

                        BaseResp.ErrCode.ERR_USER_CANCEL -> {
                            WXAuthResult.authCancel()
                        }

                        else -> {
                            WXAuthResult.authError(resp.errStr ?: getErrorStr(resp.errCode))
                        }
                    }
                    notifyAuthResult(result, resp, resp.transaction)
                }

                ConstantsAPI.COMMAND_SENDMESSAGE_TO_WX -> {
                    if (resp is SendMessageToWX.Resp) {
                        // TODO 这里不确定效果对不对，要改代码的
//                        resp.transaction?.let {
//                            uniGameStatusInteractor.resumeOrLaunch(it.toLongOrNull(), null, true)
//                        }
                        notifyWxShareResultToMiniSDK(resp)
                    }
                    EventBus.getDefault().post(WXShareFinishEvent())
                }

                ConstantsAPI.COMMAND_LAUNCH_WX_MINIPROGRAM -> {
                    val launchMiniProResp = resp as WXLaunchMiniProgram.Resp
                    val extraData = launchMiniProResp.extMsg
                }
            }
            finish()
        }
    }

    /**
     * 是否是第三方app授权登录(联运sdk）
     */
    private fun isLoginFromThirdApp(transition: String?): Boolean {
        if (transition.isNullOrEmpty()) return false
        return getOauthTransInfo(transition)?.loginSource == LoginSource.ThirdAppAuthorize.source
    }

    /**
     * 根据transition获取传递的信息
     */
    private fun getOauthTransInfo(transaction: String?) = GsonUtil.gsonSafeParse<OauthTransInfo>(transaction)

    /**
     * 授权结果回调
     */
    private fun notifyAuthResult(result: WXAuthResult, resp: BaseResp, transition: String?) {
        Timber.d("notifyAuthResult-%s-%s", result, transition)
        if (isLoginFromThirdApp(transition)) {
//            GparkEntryActivity.mTaskId?.let {
//                val activityManager =
//                    (getSystemService(Context.ACTIVITY_SERVICE) as? ActivityManager) ?: return
//                activityManager.moveTaskToFront(
//                    it, ActivityManager.MOVE_TASK_WITH_HOME
//                )
//            }
        }
        OAuthManager.callbacks.dispatchOnMainThread {
            if (result.isSucceed()) {
                val authResp = resp as SendAuth.Resp
                this.onSuccess(
                    OAuthResponse(
                        LoginWay.Wechat,
                        authResp.code,
                        "",
                        0,
                        "",
                        "",
                        authResp.url
                    ).apply {
                        val transaction = resp.transaction
                        val oauthTransInfo = getOauthTransInfo(transaction)
                        source = oauthTransInfo?.loginSource
                        this.loginType = oauthTransInfo?.loginType
                    }
                )
            } else if (result.isCancel()) {
                this.onCancel(LoginWay.Wechat)
            } else {
                this.onFailed(LoginWay.Wechat, resp.errStr ?: getErrorStr(resp.errCode), resp.errCode, resp.transaction)
            }
        }
    }

    private fun notifyWxShareResultToMiniSDK(resp: BaseResp) {
//        WXEntryActivityHelper.handleShareResult(this.applicationContext, resp)
    }

    override fun onDestroy() {
        super.onDestroy()
        wxApi.detach()
    }
}