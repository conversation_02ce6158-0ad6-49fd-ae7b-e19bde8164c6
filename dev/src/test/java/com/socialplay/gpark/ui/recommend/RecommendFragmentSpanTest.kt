package com.socialplay.gpark.ui.recommend

import android.content.res.Configuration
import androidx.recyclerview.widget.GridLayoutManager
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.socialplay.gpark.util.PadAdapterUtil
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert.*
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 测试 RecommendFragment 横竖屏切换时的 span 配置
 * 确保不会出现 "Item requires X spans but GridLayoutManager has only Y spans" 的错误
 */
@RunWith(AndroidJUnit4::class)
class RecommendFragmentSpanTest {

    @Test
    fun testSpanConfigurationConsistency() {
        // 测试不同设备和方向下的 span 配置
        val testCases = listOf(
            // 手机竖屏
            TestCase(isPad = false, orientation = Configuration.ORIENTATION_PORTRAIT, expectedSpan = 2),
            // 手机横屏
            TestCase(isPad = false, orientation = Configuration.ORIENTATION_LANDSCAPE, expectedSpan = 2),
            // 平板竖屏
            TestCase(isPad = true, orientation = Configuration.ORIENTATION_PORTRAIT, expectedSpan = 4),
            // 平板横屏
            TestCase(isPad = true, orientation = Configuration.ORIENTATION_LANDSCAPE, expectedSpan = 6)
        )

        testCases.forEach { testCase ->
            val context = mockk<android.content.Context>()
            val resources = mockk<android.content.res.Resources>()
            val configuration = mockk<Configuration>()

            every { context.resources } returns resources
            every { resources.configuration } returns configuration
            every { configuration.orientation } returns testCase.orientation

            // 模拟 isPad 扩展属性
            every { context.resources.configuration.screenLayout and Configuration.SCREENLAYOUT_SIZE_MASK } returns 
                if (testCase.isPad) Configuration.SCREENLAYOUT_SIZE_XLARGE else Configuration.SCREENLAYOUT_SIZE_NORMAL

            val actualSpan = PadAdapterUtil.getItemsPerRow(context, 2)
            
            assertEquals(
                "设备类型: ${if (testCase.isPad) "平板" else "手机"}, 方向: ${if (testCase.orientation == Configuration.ORIENTATION_LANDSCAPE) "横屏" else "竖屏"}",
                testCase.expectedSpan,
                actualSpan
            )
        }
    }

    @Test
    fun testSpanSizeOverrideWithCoerceAtMost() {
        // 测试 coerceAtMost 防护措施
        val testCases = listOf(
            // 正常情况：spanSize <= totalSpanCount
            SpanTestCase(spanSize = 2, totalSpanCount = 4, expected = 2),
            SpanTestCase(spanSize = 4, totalSpanCount = 4, expected = 4),
            // 异常情况：spanSize > totalSpanCount，应该被限制
            SpanTestCase(spanSize = 6, totalSpanCount = 4, expected = 4),
            SpanTestCase(spanSize = 8, totalSpanCount = 2, expected = 2),
            // 边界情况
            SpanTestCase(spanSize = 1, totalSpanCount = 1, expected = 1),
            SpanTestCase(spanSize = 0, totalSpanCount = 4, expected = 0)
        )

        testCases.forEach { testCase ->
            val result = testCase.spanSize.coerceAtMost(testCase.totalSpanCount)
            assertEquals(
                "spanSize: ${testCase.spanSize}, totalSpanCount: ${testCase.totalSpanCount}",
                testCase.expected,
                result
            )
        }
    }

    @Test
    fun testGridLayoutManagerSpanCountUpdate() {
        // 测试 GridLayoutManager spanCount 更新逻辑
        val gridLayoutManager = GridLayoutManager(mockk(), 2)
        
        // 初始状态
        assertEquals(2, gridLayoutManager.spanCount)
        
        // 更新到 4
        gridLayoutManager.spanCount = 4
        assertEquals(4, gridLayoutManager.spanCount)
        
        // 更新到 6
        gridLayoutManager.spanCount = 6
        assertEquals(6, gridLayoutManager.spanCount)
        
        // 回到 2
        gridLayoutManager.spanCount = 2
        assertEquals(2, gridLayoutManager.spanCount)
    }

    private data class TestCase(
        val isPad: Boolean,
        val orientation: Int,
        val expectedSpan: Int
    )

    private data class SpanTestCase(
        val spanSize: Int,
        val totalSpanCount: Int,
        val expected: Int
    )
}
