<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/bottomContainer"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:paddingBottom="@dimen/dp_24"
        android:background="@drawable/bg_white_top_round_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        >

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp_14"
            android:gravity="center"
            android:text="@string/settings_confirm_logout_title"
            android:textStyle="bold"
            android:textColor="@color/color_1A1A1A"
            android:paddingVertical="@dimen/dp_12"
            app:layout_constraintTop_toTopOf="parent"
            />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_height="@dimen/dp_0"
            android:layout_width="wrap_content"
            android:layout_marginEnd="@dimen/dp_16"
            app:layout_constraintTop_toTopOf="@id/tv_title"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintEnd_toEndOf="@id/tv_title"
            android:src="@drawable/close_icon"
            />

        <View
            android:id="@+id/v_divider"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:background="@color/color_F0F0F0"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            />

        <ImageView
            android:id="@+id/ivLogoutRole"
            android:layout_width="@dimen/dp_130"
            android:layout_height="@dimen/dp_130"
            android:layout_marginTop="@dimen/dp_12"
            android:src="@drawable/ic_account_logout_role"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/v_divider" />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvDesc"
            android:textSize="@dimen/sp_14"
            android:layout_width="@dimen/dp_0"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginHorizontal="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_8"
            android:text="@string/settings_confirm_logout_content"
            android:textColor="@color/color_333333"
            app:layout_constraintTop_toBottomOf="@id/ivLogoutRole"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvId"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_8"
            android:text="@string/logout_uuid_tips"
            android:drawableEnd="@drawable/ic_login_help_info_copy"
            android:drawablePadding="@dimen/dp_8"
            app:layout_constraintTop_toBottomOf="@id/tvDesc"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvConfirmBtn"
            style="@style/Button.S16.PoppinsMedium500"
            android:layout_width="@dimen/dp_303"
            android:layout_height="@dimen/dp_48"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@drawable/shape_f6f6f6_round"
            android:text="@string/settings_confirm_logout"
            android:textStyle="bold"
            android:textColor="@color/color_1A1A1A"
            android:layout_marginVertical="@dimen/dp_24"
            app:layout_constraintTop_toBottomOf="@id/tvId"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>