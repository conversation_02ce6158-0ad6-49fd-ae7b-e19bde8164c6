<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/templateCardRoot"
    android:layout_width="196dp"
    android:layout_height="194dp"
    android:layout_marginEnd="8dp">

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_game_icon"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_135"
        android:layout_marginTop="5.8dp"
        android:scaleType="centerCrop"
        app:cornerRadii="@dimen/dp_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@color/default_shadow_color" />

    <!-- 顶部Template标签和星级 -->
    <LinearLayout
        android:id="@+id/layout_template_tag"
        android:layout_width="wrap_content"
        android:layout_height="23dp"
        android:layout_marginStart="-1dp"
        android:background="@drawable/bg_home_template_child_item"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingEnd="@dimen/dp_5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_template"
            style="@style/MetaTextViewMapTag"
            android:layout_width="wrap_content"
            android:layout_height="14dp"
            android:layout_marginStart="@dimen/dp_6"
            android:layout_marginEnd="@dimen/dp_4"
            android:gravity="center_vertical"
            android:text="@string/game_template"
            android:textColor="@color/white"
            tools:text="玩法严选榜单" />

        <ImageView
            android:id="@+id/iv_star1"
            android:layout_width="@dimen/dp_11"
            android:layout_height="@dimen/dp_11"
            android:src="@drawable/ic_star_white"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/iv_star2"
            android:layout_width="@dimen/dp_11"
            android:layout_height="@dimen/dp_11"
            android:layout_marginStart="@dimen/dp_2"
            android:src="@drawable/ic_star_white"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/iv_star3"
            android:layout_width="@dimen/dp_11"
            android:layout_height="@dimen/dp_11"
            android:layout_marginStart="@dimen/dp_2"
            android:alpha="0.3"
            android:src="@drawable/ic_star_white"
            android:visibility="visible" />
    </LinearLayout>

    <!-- 点赞数 -->
    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/like_view"
        android:layout_width="wrap_content"
        android:layout_height="26dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_game_icon"
        app:layout_constraintStart_toStartOf="@id/iv_game_icon"
        app:likeText="22.23k" />
    <!-- 游玩数 -->
    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/players_view"
        android:layout_width="wrap_content"
        android:layout_height="26dp"
        android:layout_marginStart="-4dp"
        app:layout_constraintBottom_toBottomOf="@id/like_view"
        app:layout_constraintLeft_toRightOf="@id/like_view"
        app:layout_constraintTop_toTopOf="@id/like_view"
        app:likeIcon="@drawable/icon_item_player"
        app:likeText="22.23k played" />

    <!-- 标题 -->
    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_title"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginTop="@dimen/dp_6"
        android:ellipsize="end"
        android:gravity="start"
        android:lines="1"
        android:maxLines="1"
        android:textColor="@color/Gray_1000"
        app:layout_constraintStart_toStartOf="@id/iv_game_icon"
        app:layout_constraintTop_toBottomOf="@id/iv_game_icon"
        tools:text="Enchilada Casserole" />

    <!-- 标签区 -->
    <com.socialplay.gpark.ui.view.TagContainerView
        android:id="@+id/tag_container"
        android:layout_width="wrap_content"
        android:layout_height="22dp"
        android:layout_marginTop="@dimen/dp_4"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_game_title"
        tools:visibility="visible" />


    <!-- Create按钮 -->
    <Button
        android:id="@+id/btn_create"
        style="@style/Button.S12.PoppinsBold600"
        android:layout_width="@dimen/dp_66"
        android:layout_height="@dimen/dp_30"
        android:layout_marginTop="@dimen/dp_4"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp_12"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_12"
        android:text="@string/recommend_create"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tag_container"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible" />


    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/siv_creator_avatar"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:layout_marginTop="@dimen/dp_4"
        android:foreground="@drawable/avatar_inner_stroke_border"
        android:scaleType="centerCrop"
        android:src="@color/color_8792B2"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@+id/tv_game_title"
        app:layout_constraintTop_toBottomOf="@id/tv_game_title"
        app:shapeAppearance="@style/circleStyle"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_creator_nickname"
        style="@style/MetaTextView.S12.PoppinsRegular400.White"
        android:layout_width="0dp"
        android:layout_height="16dp"
        android:layout_marginLeft="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:singleLine="true"
        android:textColor="@color/color_757575"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/siv_creator_avatar"
        app:layout_constraintLeft_toRightOf="@+id/siv_creator_avatar"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/siv_creator_avatar"
        tools:text="Nickname"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout> 