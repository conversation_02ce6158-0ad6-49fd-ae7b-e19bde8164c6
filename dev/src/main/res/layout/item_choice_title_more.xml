<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="24dp"
    android:layout_marginBottom="8dp"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_6"
    app:layout_optimizationLevel="standard|chains|dimensions|ratio|cache_measures">

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_card_title"
        style="@style/MetaTextView.S15.PoppinsMedium600"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:textColor="@color/textColorPrimary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Live rooms" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_card_more"
        style="@style/MetaTextView.S12.PoppinsRegular400.CenterVertical"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:gravity="center"
        android:paddingVertical="@dimen/dp_2"
        android:paddingStart="@dimen/dp_5"
        android:text="@string/profile_map_tab_all"
        android:textColor="@color/textColorPrimaryLight"
        app:layout_constraintBottom_toBottomOf="@id/tv_card_title"
        app:layout_constraintRight_toLeftOf="@id/iv_arrow_right"
        app:layout_constraintTop_toTopOf="@id/tv_card_title" />

    <ImageView
        android:id="@+id/iv_arrow_right"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:layout_marginEnd="@dimen/dp_6"
        android:scaleType="fitXY"
        android:src="@drawable/icon_more_right_arrow"
        app:layout_constraintBottom_toBottomOf="@id/tv_card_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_card_title" />

    <View
        android:id="@+id/vClick"
        android:layout_width="@dimen/dp_100"
        android:layout_height="24dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_red_dot"
        android:layout_width="@dimen/dp_8"
        android:layout_height="@dimen/dp_8"
        android:layout_marginTop="2dp"
        android:background="@drawable/sp_red_dot"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/tv_card_title"
        app:layout_constraintTop_toTopOf="@id/tv_card_title"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>