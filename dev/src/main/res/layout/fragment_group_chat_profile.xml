<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivTitleBack"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/title_bar_height"
        android:background="?attr/actionBarItemBackground"
        android:paddingHorizontal="@dimen/dp_16"
        android:src="@drawable/icon_back_array_bold_black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusBar" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintTop_toBottomOf="@id/ivTitleBack"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/ivGroupAvatar"
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_90"
                android:layout_marginStart="@dimen/dp_13"
                android:layout_marginTop="@dimen/dp_21"
                android:scaleType="centerCrop"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:shapeAppearance="@style/shapeRound45Style"
                tools:src="@drawable/icon_item_group_chat_avatar" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvGroupName"
                style="@style/MetaTextView.S24.PoppinsBold700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/color_17191C"
                android:textSize="@dimen/sp_24"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toTopOf="@id/tvGroupId"
                app:layout_constraintEnd_toStartOf="@id/ivGroupNameEdit"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/ivGroupAvatar"
                app:layout_constraintTop_toTopOf="@id/ivGroupAvatar"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_goneMarginEnd="@dimen/dp_16"
                tools:text="Jhon Martin" />

            <ImageView
                android:id="@+id/ivGroupNameEdit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_marginEnd="@dimen/dp_16"
                android:src="@drawable/icon_group_chat_profile_name_edit"
                app:layout_constraintBottom_toBottomOf="@id/tvGroupName"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvGroupName"
                app:layout_constraintTop_toTopOf="@id/tvGroupName"
                tools:visibility="visible" />

            <View
                android:id="@+id/viewGroupNameClick"
                android:layout_width="@dimen/dp_0"
                android:layout_height="@dimen/dp_0"
                app:layout_constraintBottom_toBottomOf="@id/tvGroupName"
                app:layout_constraintEnd_toEndOf="@id/ivGroupNameEdit"
                app:layout_constraintStart_toStartOf="@id/tvGroupName"
                app:layout_constraintTop_toTopOf="@id/tvGroupName" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvGroupId"
                style="@style/MetaTextView.S12.PoppinsLight300"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/group_chat_profile_page_group_id"
                android:textColor="@color/color_B3B3B3"
                android:textSize="@dimen/sp_12"
                app:layout_constraintBottom_toBottomOf="@id/ivGroupAvatar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="@id/tvGroupName"
                app:layout_constraintTop_toBottomOf="@id/tvGroupName" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutMembers"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_69"
                android:layout_marginTop="@dimen/dp_9"
                app:layout_constraintTop_toBottomOf="@id/ivGroupAvatar">

                <com.socialplay.gpark.ui.view.MetaTextView
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/group_chat_profile_page_members_title"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvMembersCount"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|end"
                    android:layout_marginEnd="@dimen/dp_8"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/color_B3B3B3"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ivMembersArrow"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="23" />

                <ImageView
                    android:id="@+id/ivMembersArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:src="@drawable/icon_chat_group_profile_arrow"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_05"
                    android:background="@color/color_F0F0F0"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutDesc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/layoutMembers">

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvDescTitle"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_24"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/group_chat_profile_page_desc_title"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/ivDescDetail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:src="@drawable/icon_chat_group_profile_arrow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvDescTitle" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvDescContent"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="@dimen/dp_16"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="@color/color_666666"
                    android:textSize="@dimen/sp_14"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="@id/tvDescTitle"
                    app:layout_constraintTop_toBottomOf="@id/tvDescTitle"
                    tools:text="This is a group announcement. This is a group announcement.This is a group announcement.This is a group announcement." />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_05"
                    android:layout_marginTop="@dimen/dp_24"
                    android:background="@color/color_F0F0F0"
                    app:layout_constraintTop_toBottomOf="@id/tvDescContent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutManagementInvitation"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_76"
                app:layout_constraintTop_toBottomOf="@id/layoutDesc">

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvManagementInvitationTitle"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/group_chat_profile_page_management_invitation_title"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintBottom_toTopOf="@id/tvManagementInvitationContent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvManagementInvitationContent"
                    style="@style/MetaTextView.S12.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_4"
                    android:layout_marginEnd="@dimen/dp_8"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/color_B3B3B3"
                    android:textSize="@dimen/sp_12"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ivManagementInvitationArrow"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvManagementInvitationTitle"
                    app:layout_goneMarginEnd="@dimen/dp_16"
                    tools:text="Everyone can join the group, Anyone can invite-----------------------" />

                <ImageView
                    android:id="@+id/ivManagementInvitationArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:src="@drawable/icon_chat_group_profile_arrow"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_05"
                    android:background="@color/color_F0F0F0"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutNotifications"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_76"
                app:layout_constraintTop_toBottomOf="@id/layoutManagementInvitation">

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvNotificationsTitle"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/group_chat_profile_page_notifications_title"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <com.socialplay.gpark.ui.view.MetaSwitchCompat
                    android:id="@+id/switchNotifications"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:checked="false"
                    android:thumb="@drawable/bg_setting_thumb"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:switchMinWidth="@dimen/dp_48"
                    app:track="@drawable/green_switch_track_selector" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_05"
                    android:background="@color/color_F0F0F0"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutAgreement"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_69"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/layoutNotifications"
                tools:visibility="visible">

                <com.socialplay.gpark.ui.view.MetaTextView
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/group_chat_profile_page_agreement_title"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:src="@drawable/icon_chat_group_profile_arrow"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_05"
                    android:background="@color/color_F0F0F0"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutReport"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_69"
                app:layout_constraintTop_toBottomOf="@id/layoutAgreement">

                <com.socialplay.gpark.ui.view.MetaTextView
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/group_chat_profile_page_report_title"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:src="@drawable/icon_chat_group_profile_arrow"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_05"
                    android:background="@color/color_F0F0F0"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutLeaveGroup"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_69"
                app:layout_constraintTop_toBottomOf="@id/layoutReport">

                <com.socialplay.gpark.ui.view.MetaTextView
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dp_16"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/group_chat_profile_page_leave_group_title"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_14"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:src="@drawable/icon_chat_group_profile_arrow"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_05"
                    android:background="@color/color_F0F0F0"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <Space
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_58"
                app:layout_constraintTop_toBottomOf="@id/layoutLeaveGroup" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loadingView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivTitleBack"
        tools:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>