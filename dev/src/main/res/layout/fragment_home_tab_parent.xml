<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:speed_monitor="true">

    <!-- 为了让背景色成为一体,所以 fragment_home_maps 布局中将ViewPager的显示区域显示延伸到了顶部状态栏  -->
    <!-- 所以当前fragment需要将顶部空间留出来  -->
    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/holder"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/spaceTitle"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_44"
        app:layout_constraintTop_toBottomOf="@id/holder"/>

    <ImageView
        android:id="@+id/ivBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:scaleType="fitStart"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_constraintDimensionRatio="1125:2436"
        tools:src="@drawable/bg_welfare_tasks"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/ll_top_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_312"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/spaceTitle">

        <View
            android:id="@+id/view_top_bg"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_136" />

        <View
            android:id="@+id/view_top_bg_gradient"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/fl_rec_top_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/spaceTitle">

<!--        <View-->
<!--            android:id="@+id/v_rec_top_bg"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"-->
<!--            tools:background="#6A88BE" />-->

<!--        <View-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"-->
<!--            android:background="@color/white" />-->
    </FrameLayout>


    <android.widget.Space
        android:id="@+id/space_top"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/spaceTitle"/>

    <FrameLayout
        android:id="@+id/fl_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/space_top" />

    <RelativeLayout
        android:id="@+id/rl_h5_top_place_holder"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="visible"
        app:layout_constraintTop_toBottomOf="@id/spaceTitle"/>

</androidx.constraintlayout.widget.ConstraintLayout>