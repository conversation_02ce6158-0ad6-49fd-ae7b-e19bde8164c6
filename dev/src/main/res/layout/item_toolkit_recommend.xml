<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dp_16">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivToolkit"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:layout_marginHorizontal="@dimen/dp_4"
        android:background="@drawable/bg_d0d9f0_corner_12"
        android:scaleType="centerCrop"
        app:layout_constraintDimensionRatio="164:124"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_12dp"
        tools:src="@color/color_999999" />

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/mlvPlayers"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/ivToolkit"
        app:layout_constraintStart_toStartOf="@id/ivToolkit"
        app:showIcon="true" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvToolkitTitle"
        style="@style/MetaTextView.S13.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintEnd_toEndOf="@id/ivToolkit"
        app:layout_constraintStart_toStartOf="@id/ivToolkit"
        app:layout_constraintTop_toBottomOf="@id/ivToolkit"
        tools:text="This is a name" />

    <com.socialplay.gpark.ui.view.TagContainerView
        android:id="@+id/tagsAssets"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_4"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/ivToolkitPrice"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/ivToolkit"
        app:layout_constraintTop_toBottomOf="@id/tvToolkitTitle"
        tools:background="@color/color_999999"
        tools:layout_height="@dimen/dp_22"
        tools:layout_width="@dimen/dp_84"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivToolkitPrice"
        android:layout_width="@dimen/dp_14"
        android:layout_height="@dimen/dp_14"
        android:layout_marginEnd="@dimen/dp_2"
        android:scaleType="centerCrop"
        android:src="@drawable/icon_g_coin_size_14"
        app:layout_constraintBottom_toBottomOf="@id/tvToolkitPrice"
        app:layout_constraintEnd_toStartOf="@id/tvToolkitPrice"
        app:layout_constraintTop_toTopOf="@id/tvToolkitPrice" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvToolkitPrice"
        style="@style/MetaTextView.S12.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textColor="@color/color_1A1A1A"
        app:layout_constraintEnd_toEndOf="@id/ivToolkit"
        app:layout_constraintTop_toBottomOf="@id/tvToolkitTitle"
        tools:text="10,000" />

</androidx.constraintlayout.widget.ConstraintLayout> 