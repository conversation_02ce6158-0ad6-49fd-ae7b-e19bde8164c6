<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivTitleBack"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/title_bar_height"
        android:background="?attr/actionBarItemBackground"
        android:paddingHorizontal="@dimen/dp_16"
        android:src="@drawable/icon_back_array_bold_black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/llBarInfo"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@id/tvFriendStatus"
        app:layout_constraintEnd_toStartOf="@id/ivTitleSetting"
        app:layout_constraintStart_toEndOf="@id/ivTitleBack"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed">

        <TextView
            android:id="@+id/tvChatName"
            style="@style/MetaTextView.S15.PoppinsMedium500.CenterVertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_4"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:singleLine="true"
            tools:text="YangtuoYtuo" />

        <com.socialplay.gpark.ui.view.UserLabelView
            android:id="@+id/labelGroup"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </LinearLayout>

    <View
        android:id="@+id/ivOnline"
        android:layout_width="@dimen/dp_6"
        android:layout_height="@dimen/dp_6"
        android:background="@drawable/sp_online_dot"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tvFriendStatus"
        app:layout_constraintEnd_toStartOf="@id/tvFriendStatus"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@id/llBarInfo"
        app:layout_constraintTop_toTopOf="@id/tvFriendStatus"
        tools:visibility="visible" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tvFriendStatus"
        style="@style/MetaTextView.S12.PoppinsMedium500.Secondary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_2"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/llBarInfo"
        app:layout_constraintStart_toEndOf="@id/ivOnline"
        app:layout_constraintTop_toBottomOf="@id/llBarInfo"
        tools:text="amazing g wtg wtfa"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivTitleSetting"
        android:layout_width="@dimen/dp_44"
        android:layout_height="@dimen/title_bar_height"
        android:layout_marginEnd="@dimen/dp_6"
        android:background="?attr/actionBarItemBackground"
        android:padding="@dimen/dp_10"
        android:scaleType="fitCenter"
        android:src="@drawable/icon_group_chat_title_setting"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_05"
        android:background="@color/color_F0F2F4"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>