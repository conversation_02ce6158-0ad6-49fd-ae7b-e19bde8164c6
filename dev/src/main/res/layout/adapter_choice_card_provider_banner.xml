<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_16"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <com.socialplay.gpark.ui.view.WrapEpoxyBanner
        android:id="@+id/banner"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_142"
        app:layout_constraintDimensionRatio="343:142"
        android:maxWidth="343dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/colorPrimary" />

    <com.zhpan.indicator.IndicatorView
        android:id="@+id/indicator"
        android:layout_width="wrap_content"
        android:layout_height="3dp"
        android:layout_marginTop="@dimen/dp_8"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/banner"
        app:vpi_orientation="horizontal"
        app:vpi_slide_mode="smooth"
        app:vpi_slider_checked_color="@color/color_F49D0C"
        app:vpi_slider_normal_color="@color/color_FDE58A"
        app:vpi_style="round_rect" />

</androidx.constraintlayout.widget.ConstraintLayout>