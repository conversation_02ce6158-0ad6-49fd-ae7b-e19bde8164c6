<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/smallCardRoot"
    android:layout_width="105dp"
    android:layout_height="119dp"
    android:layout_marginEnd="8dp"
    app:layout_optimizationLevel="standard|chains|dimensions|ratio|cache_measures">

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_game_icon"
        android:layout_width="105dp"
        android:layout_height="90dp"
        android:scaleType="centerCrop"
        app:bottomLeftRadius="@dimen/dp_0"
        app:bottomRightRadius="@dimen/dp_0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:topLeftRadius="@dimen/dp_12"
        app:topRightRadius="@dimen/dp_12"
        tools:src="@drawable/placeholder_corner" />

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv_name_bg"
        android:layout_width="105dp"
        android:layout_height="@dimen/dp_29"
        android:layout_marginTop="-1dp"
        android:src="@color/color_BF98FE"
        app:bottomLeftRadius="@dimen/dp_12"
        app:bottomRightRadius="@dimen/dp_12"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_game_icon"
        app:topLeftRadius="@dimen/dp_0"
        app:topRightRadius="@dimen/dp_0" />

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/metaLikeView"
        android:layout_width="wrap_content"
        android:layout_height="26dp"
        app:layout_constraintBottom_toTopOf="@id/iv_name_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:likeIcon="@drawable/recommend_iconvideo_like"
        app:likeText="1234574733735" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_game_title"
        style="@style/MetaTextView.S11.PoppinsRegular400"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginTop="@dimen/dp_6"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:lines="1"
        android:maxLines="1"
        android:minLines="1"
        android:paddingHorizontal="@dimen/dp_6"
        android:textColor="@color/white"
        app:layout_constraintTop_toBottomOf="@+id/iv_game_icon"
        tools:text="斗罗大陆里边有个兔子" />

</androidx.constraintlayout.widget.ConstraintLayout>