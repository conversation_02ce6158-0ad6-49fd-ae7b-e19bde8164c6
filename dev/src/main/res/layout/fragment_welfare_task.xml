<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/ivBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        android:scaleType="fitStart"
        android:src="@drawable/bg_welfare_tasks"
        app:layout_constraintDimensionRatio="1125:2436"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusBar"
        app:title_text="@string/activity_page_title"
        app:title_text_color="@color/color_1A1A1A" />

    <com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ViewStub
                android:id="@+id/viewStubBalance"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inflatedId="@+id/viewStubBalance"
                android:layout="@layout/layout_welfare_task_balance"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <androidx.core.widget.NestedScrollView
                android:id="@+id/scrollView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_0"
                android:layout_marginTop="@dimen/dp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/viewStubBalance">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipChildren="false">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layoutDailyCheckIn"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:background="@drawable/shape_vertical_white80_to_white_corner_16"
                        android:paddingBottom="@dimen/dp_12"
                        android:visibility="gone"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:visibility="visible">

                        <ImageView
                            android:id="@+id/ivDailyCheckInBottomLine"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="-8dp"
                            android:src="@drawable/bottom_line_welfare_task_title"
                            app:layout_constraintStart_toStartOf="@id/tvDailyCheckIn"
                            app:layout_constraintTop_toBottomOf="@id/tvDailyCheckIn" />

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvDailyCheckIn"
                            style="@style/MetaTextView.S16.PoppinsSemiBold600"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_16"
                            android:layout_marginTop="@dimen/dp_12"
                            android:ellipsize="end"
                            android:gravity="start"
                            app:layout_constrainedWidth="true"
                            app:layout_constraintEnd_toStartOf="@id/ivCheckInHelp"
                            app:layout_constraintHorizontal_bias="0"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:uiLineHeight="@dimen/dp_20"
                            tools:text="Daily check-in" />

                        <ImageView
                            android:id="@+id/ivCheckInHelp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="-2dp"
                            android:layout_marginEnd="@dimen/dp_16"
                            android:paddingHorizontal="@dimen/dp_6"
                            android:paddingVertical="@dimen/dp_3"
                            android:src="@drawable/ic_welfare_task_sub_title_help"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/tvDailyCheckIn"
                            app:layout_constraintTop_toTopOf="@id/tvDailyCheckIn" />

                        <com.airbnb.epoxy.EpoxyRecyclerView
                            android:id="@+id/rvCheckInTasks"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/dp_8"
                            android:layout_marginTop="@dimen/dp_12"
                            android:orientation="horizontal"
                            android:overScrollMode="never"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvDailyCheckIn"
                            tools:listitem="@layout/item_welfare_task_check_in" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layoutLimitedTime"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16"
                        android:background="@drawable/shape_vertical_white85_to_white_corner_16"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/layoutDailyCheckIn"
                        tools:visibility="visible">

                        <ImageView
                            android:id="@+id/ivLimitedTimeBottomLine"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="-8dp"
                            android:src="@drawable/bottom_line_welfare_task_title"
                            app:layout_constraintStart_toStartOf="@id/tvLimitedTime"
                            app:layout_constraintTop_toBottomOf="@id/tvLimitedTime" />

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvLimitedTime"
                            style="@style/MetaTextView.S16.PoppinsSemiBold600"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_16"
                            android:layout_marginTop="@dimen/dp_16"
                            android:gravity="start"
                            app:layout_constrainedWidth="true"
                            app:layout_constraintEnd_toStartOf="@id/ivLimitedTimeHelp"
                            app:layout_constraintHorizontal_bias="0"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="Limited-time benefits" />

                        <ImageView
                            android:id="@+id/ivLimitedTimeHelp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="-2dp"
                            android:layout_marginEnd="@dimen/dp_16"
                            android:paddingHorizontal="@dimen/dp_6"
                            android:paddingVertical="@dimen/dp_3"
                            android:src="@drawable/ic_welfare_task_sub_title_help"
                            app:layout_constrainedWidth="true"
                            app:layout_constraintEnd_toStartOf="@id/layoutLeftTime"
                            app:layout_constraintStart_toEndOf="@id/tvLimitedTime"
                            app:layout_constraintTop_toTopOf="@id/tvLimitedTime"
                            app:layout_goneMarginEnd="@dimen/dp_8" />

                        <com.airbnb.epoxy.EpoxyRecyclerView
                            android:id="@+id/rvLimitedTimeTasks"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:orientation="vertical"
                            android:overScrollMode="never"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="1.0"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvLimitedTime"
                            tools:layout_height="@dimen/dp_100"
                            tools:listitem="@layout/item_welfare_task" />

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/layoutLeftTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp_22"
                            android:background="@drawable/shape_vertical_fef3c7_to_fde793_corner_12"
                            android:paddingHorizontal="@dimen/dp_11"
                            android:paddingTop="@dimen/dp_7"
                            android:paddingBottom="@dimen/dp_4"
                            android:translationY="-8dp"
                            android:visibility="visible"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_bias="1"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintWidth_min="@dimen/dp_102"
                            tools:visibility="visible">

                            <com.socialplay.gpark.ui.view.MetaTextView
                                android:id="@+id/tvLeftTime"
                                style="@style/MetaTextView.S10.PoppinsMedium500"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/color_923F0E"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                tools:text="14 days 15:30:52" />

                            <com.socialplay.gpark.ui.view.MetaTextView
                                android:id="@+id/tvEarnDesc"
                                style="@style/MetaTextView.S10.PoppinsSemiBold600"
                                android:layout_width="wrap_content"
                                android:layout_height="@dimen/dp_22"
                                android:gravity="center"
                                android:textColor="@color/black"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/tvLeftTime"
                                tools:text="Earn easily" />

                            <ImageView
                                android:id="@+id/ivEarnValue"
                                android:layout_width="@dimen/dp_22"
                                android:layout_height="@dimen/dp_22"
                                android:layout_marginStart="@dimen/dp_2"
                                android:src="@drawable/icon_gcoins_3d_size_150"
                                app:layout_constraintBottom_toBottomOf="@id/tvEarnDesc"
                                app:layout_constraintStart_toEndOf="@id/tvEarnDesc"
                                app:layout_constraintTop_toTopOf="@id/tvEarnDesc" />

                            <com.socialplay.gpark.ui.view.MetaTextView
                                android:id="@+id/tvEarnValue"
                                style="@style/MetaTextView.S12.PoppinsSemiBold600"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_2"
                                android:gravity="center_vertical"
                                app:layout_constraintBottom_toBottomOf="@id/tvEarnDesc"
                                app:layout_constraintStart_toEndOf="@id/ivEarnValue"
                                app:layout_constraintTop_toTopOf="@id/tvEarnDesc"
                                tools:text="4000" />
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <ImageView
                            android:id="@+id/ivLeftTimeBottomArrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_10"
                            android:src="@drawable/ic_left_time_bottom_arrow"
                            android:translationY="-9dp"
                            android:visibility="visible"
                            app:layout_constraintStart_toStartOf="@id/layoutLeftTime"
                            app:layout_constraintTop_toBottomOf="@id/layoutLeftTime"
                            tools:visibility="visible" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layoutDailyTasks"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp_16"
                        android:layout_marginTop="@dimen/dp_16"
                        android:background="@drawable/shape_vertical_white90_to_white_corner_16"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/layoutLimitedTime"
                        tools:visibility="visible">

                        <ImageView
                            android:id="@+id/ivDailyTasksBottomLine"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="-8dp"
                            android:src="@drawable/bottom_line_welfare_task_title"
                            app:layout_constraintStart_toStartOf="@id/tvDailyTasks"
                            app:layout_constraintTop_toBottomOf="@id/tvDailyTasks" />

                        <com.socialplay.gpark.ui.view.MetaTextView
                            android:id="@+id/tvDailyTasks"
                            style="@style/MetaTextView.S16.PoppinsSemiBold600"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_16"
                            android:layout_marginTop="@dimen/dp_16"
                            android:gravity="start"
                            app:layout_constrainedWidth="true"
                            app:layout_constraintEnd_toStartOf="@id/ivDailyTasksHelp"
                            app:layout_constraintHorizontal_bias="0"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="Daily Tasks" />

                        <ImageView
                            android:id="@+id/ivDailyTasksHelp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="-2dp"
                            android:layout_marginEnd="@dimen/dp_16"
                            android:paddingHorizontal="@dimen/dp_6"
                            android:paddingVertical="@dimen/dp_3"
                            android:src="@drawable/ic_welfare_task_sub_title_help"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/tvDailyTasks"
                            app:layout_constraintTop_toTopOf="@id/tvDailyTasks"
                            app:layout_goneMarginEnd="@dimen/dp_8" />

                        <com.airbnb.epoxy.EpoxyRecyclerView
                            android:id="@+id/rvDailyTasks"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:overScrollMode="never"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvDailyTasks"
                            tools:layout_height="@dimen/dp_100"
                            tools:listitem="@layout/item_welfare_task" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <com.socialplay.gpark.ui.view.MetaTextView
                        android:id="@+id/tvBottomDesc"
                        style="@style/MetaTextView.S12.PoppinsRegular400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_12"
                        android:textColor="@color/color_999999"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/layoutDailyTasks"
                        tools:text="@string/acctivity_feedback"
                        tools:visibility="visible" />

                    <Space
                        android:id="@+id/spaceBottom"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_24"
                        app:layout_constraintTop_toBottomOf="@id/tvBottomDesc" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.core.widget.NestedScrollView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.socialplay.gpark.ui.view.refresh.MetaRefreshLayout>

    <com.socialplay.gpark.ui.view.LoadingView
        android:id="@+id/loadingView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>