<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="300dp"
    android:layout_height="192dp"
    android:layout_marginEnd="8dp"
    app:layout_optimizationLevel="standard|chains|dimensions|ratio|cache_measures">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_icon"
        android:layout_width="300dp"
        android:layout_height="192dp"
        android:scaleType="centerCrop"
        android:src="@drawable/placeholder_corner_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/round_corner_12dp" />

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/ivBottomBackground"
        android:layout_width="300dp"
        android:layout_height="@dimen/dp_60"
        android:alpha="0.95"
        android:background="@color/black_20"
        android:src="@drawable/shape_gradient_bottom_trans_black20"
        app:bottomLeftRadius="12dp"
        app:bottomRightRadius="12dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintLeft_toLeftOf="@id/iv_icon"
        app:topLeftRadius="0dp"
        app:topRightRadius="0dp" />

    <!--    <View-->
    <!--        android:id="@+id/v_shadow_mask"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="@dimen/dp_34"-->
    <!--        android:background="@drawable/bg_bottom_icon_shadow_mask_s8"-->
    <!--        app:layout_constraintBottom_toTopOf="@id/ivBottomBackground"-->
    <!--        app:layout_constraintEnd_toEndOf="@id/iv_icon"-->
    <!--        app:layout_constraintStart_toStartOf="@id/iv_icon" />-->

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/tv_heat"
        style="@style/MetaTextView.S9.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_26"
        android:gravity="center"
        app:layout_constraintBottom_toTopOf="@id/ivBottomBackground"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:likeIcon="@drawable/icon_item_player"
        tools:likeText="22.22k Plays" />

    <TextView
        android:id="@+id/tv_name"
        style="@style/MetaTextView.S13.PoppinsMedium500"
        android:layout_width="0dp"
        android:layout_height="20dp"
        android:layout_marginHorizontal="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_6"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/white"
        app:layout_constraintBottom_toTopOf="@id/iv_author_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        tools:text="Lightning Max, with lightning bolt..." />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_author_avatar"
        android:layout_width="@dimen/dp_18"
        android:layout_height="@dimen/dp_18"
        android:layout_marginStart="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_8"
        android:foreground="@drawable/avatar_inner_stroke_border"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:shapeAppearance="@style/circleStyle"
        tools:src="@tools:sample/avatars" />

    <TextView
        android:id="@+id/tv_author_name"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="18dp"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginEnd="@dimen/dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/white_80"
        app:layout_constraintBottom_toBottomOf="@id/iv_author_avatar"
        app:layout_constraintEnd_toEndOf="@id/iv_icon"
        app:layout_constraintStart_toEndOf="@id/iv_author_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_author_avatar"
        tools:text="SEThomson12" />

</androidx.constraintlayout.widget.ConstraintLayout>