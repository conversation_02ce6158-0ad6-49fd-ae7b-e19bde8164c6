<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_6"
    android:layout_marginBottom="@dimen/dp_12"
    app:layout_optimizationLevel="standard|chains|dimensions|ratio|cache_measures">

    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/iv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        app:bottomLeftRadius="12dp"
        app:bottomRightRadius="12dp"
        app:layout_constraintDimensionRatio="164:123"
        app:layout_constraintTop_toTopOf="parent"
        app:topLeftRadius="12dp"
        app:topRightRadius="12dp"
        tools:background="@drawable/placeholder_default" />

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/mlvLike"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_26"
        app:layout_constraintBottom_toBottomOf="@id/iv"
        app:layout_constraintLeft_toLeftOf="@id/iv"
        app:likeIcon="@drawable/recommend_iconvideo_like"
        app:likeText="233" />

    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/mlvPlayerNum"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_26"
        android:layout_marginStart="-4dp"
        app:layout_constraintBottom_toBottomOf="@id/iv"
        app:layout_constraintLeft_toRightOf="@id/mlvLike"
        app:likeIcon="@drawable/icon_item_player"
        app:likeText="233" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/MetaTextView.S13.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_20"
        android:layout_marginTop="@dimen/dp_4"
        android:gravity="center_vertical"
        android:singleLine="true"
        app:layout_constraintLeft_toLeftOf="@id/iv"
        app:layout_constraintRight_toRightOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/iv"
        tools:text="Copycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware Smoothie" />

    <!-- 标签区 -->
    <com.socialplay.gpark.ui.view.TagContainerView
        android:id="@+id/layout_tags"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_22"
        android:layout_marginTop="@dimen/dp_6"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_marginTop="@dimen/dp_4"
        android:foreground="@drawable/shape_transparent_round"
        app:layout_constraintLeft_toLeftOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/layout_tags" />

    <TextView
        android:id="@+id/tvName"
        style="@style/MetaTextView.S12.PoppinsRegular400"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_17"
        android:layout_marginStart="4dp"
        android:layout_marginTop="@dimen/dp_4"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:textColor="#666"
        app:layout_constraintLeft_toRightOf="@id/ivIcon"
        app:layout_constraintRight_toRightOf="@id/iv"
        app:layout_constraintTop_toBottomOf="@id/layout_tags"
        tools:text="Copycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware SmoothieCopycat ware Smoothie" />

</androidx.constraintlayout.widget.ConstraintLayout>