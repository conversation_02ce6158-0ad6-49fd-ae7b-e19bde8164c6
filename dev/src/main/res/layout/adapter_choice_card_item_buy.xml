<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="212dp"
    android:layout_height="235dp"
    android:layout_marginEnd="8dp"
    app:layout_optimizationLevel="standard|chains|dimensions|ratio|cache_measures">

    <!-- 顶部图片区，顶部圆角，底部直角 -->
    <com.socialplay.gpark.ui.view.RoundImageView
        android:id="@+id/ivGameIcon"
        android:layout_width="212dp"
        android:layout_height="@dimen/dp_135"
        android:background="@color/color_8792B2"
        android:scaleType="centerCrop"
        app:bottomLeftRadius="0dp"
        app:bottomRightRadius="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:topLeftRadius="12dp"
        app:topRightRadius="12dp"
        tools:src="@color/default_shadow_color" />

    <!-- 点赞数，左下角 -->
    <com.socialplay.gpark.ui.view.MetaLikeView
        android:id="@+id/likeView"
        android:layout_width="wrap_content"
        android:layout_height="26dp"
        app:layout_constraintBottom_toBottomOf="@id/ivGameIcon"
        app:layout_constraintStart_toStartOf="@id/ivGameIcon"
        app:likeIcon="@drawable/recommend_iconvideo_like"
        app:likeText="22.2k" />

    <!-- 底部描述区，底部圆角，顶部直角 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutDesc"
        android:layout_width="212dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivGameIcon">

        <com.socialplay.gpark.ui.view.RoundImageView
            android:layout_width="212dp"
            android:layout_height="100dp"
            android:layout_marginBottom="-8dp"
            android:src="@color/Gray_100"
            app:bottomLeftRadius="12dp"
            app:bottomRightRadius="12dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:topLeftRadius="0dp"
            app:topRightRadius="0dp"

            />

        <!-- 资源名称 -->
        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tvGameTitle"
            style="@style/MetaTextView.S13.PoppinsMedium500"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_marginLeft="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_6"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/Gray_1000"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Enchilada Casser" />

        <!-- 标签区和价格区同一行 -->
        <com.socialplay.gpark.ui.view.TagContainerView
            android:id="@+id/tag_container"
            android:layout_width="0dp"
            android:layout_height="26dp"
            android:layout_marginTop="@dimen/dp_6"
            app:layout_constraintEnd_toStartOf="@id/layoutPrice"
            app:layout_constraintStart_toStartOf="@id/tvGameTitle"
            app:layout_constraintTop_toBottomOf="@id/tvGameTitle"
            app:tagBackgroundColor="@color/color_FFEAE0"
            app:tagStrokeColor="@color/transparent"
            app:tagTextColor="@color/color_FF7210" />

        <LinearLayout
            android:id="@+id/layoutPrice"
            android:layout_width="wrap_content"
            android:layout_height="22dp"
            android:layout_marginTop="@dimen/dp_6"
            android:layout_marginEnd="@dimen/dp_2"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="@id/btnBuy"
            app:layout_constraintTop_toBottomOf="@id/tvGameTitle">

            <ImageView
                android:id="@+id/ivCoin"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_marginEnd="2dp"
                android:src="@drawable/icon_item_buy_coins_g" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvPrice"
                style="@style/MetaTextView.S14.PoppinsSemiBold600"
                android:layout_width="wrap_content"
                android:layout_height="22dp"
                android:textColor="@color/Gray_1000"
                tools:text="999,999" />
        </LinearLayout>

        <!-- 购买按钮 -->
        <Button
            android:id="@+id/btnBuy"
            style="@style/Button.S12.PoppinsBold600"
            android:layout_width="196dp"
            android:layout_height="@dimen/dp_30"
            android:layout_marginHorizontal="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_8"
            android:text="@string/buy_now"
            android:textColor="@color/Gray_1000"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layoutPrice" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 