<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_fff7de_to_fffaeb">

    <com.socialplay.gpark.ui.view.StatusBarPlaceHolderView
        android:id="@+id/sbphv_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.TitleBarLayout
        android:id="@+id/tbl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:back_icon="@drawable/ic_back_arrow_v2"
        app:background_color="@color/transparent"
        app:isDividerVisible="false"
        app:layout_constraintTop_toBottomOf="@+id/sbphv_placeholder"
        app:rightText="@string/login_skip" />

    <ImageView
        android:id="@+id/tvLoginHelp"
        android:layout_height="@dimen/dp_24"
        android:layout_width="@dimen/dp_24"
        android:layout_marginEnd="@dimen/dp_24"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintEnd_toStartOf="@id/iv_age_restriction"
        app:layout_constraintTop_toTopOf="@id/tbl"
        android:src="@drawable/ic_login_question"
        />

    <ImageView
        android:id="@+id/iv_age_restriction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp_20"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/tbl"
        app:layout_constraintRight_toRightOf="@id/tbl"
        app:layout_constraintTop_toTopOf="@id/tbl"
        tools:visibility="visible" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/rl_content_area"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/dp_16"
        android:layout_marginBottom="@dimen/dp_10"
        app:layout_constraintBottom_toTopOf="@+id/cl_agreement"
        app:layout_constraintTop_toBottomOf="@id/tbl">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupLogo"
                android:layout_height="@dimen/dp_0"
                android:layout_width="@dimen/dp_0"
                app:constraint_referenced_ids="ivLogo, ivLoginAccountTip"
                />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupContinueLogin"
                android:layout_height="@dimen/dp_0"
                android:layout_width="@dimen/dp_0"
                android:visibility="gone"
                app:constraint_referenced_ids="ivContinueLogin, tvContinueLogin, vAccountBlock"
                />

            <ImageView
                android:id="@+id/ivLogo"
                android:layout_height="@dimen/dp_34"
                android:layout_width="@dimen/dp_120"
                android:src="@drawable/icon_logo"
                android:layout_marginTop="@dimen/dp_26"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/ivLoginAccountTip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/MetaTextView.S20.PoppinsMedium500"
                android:layout_marginTop="@dimen/dp_18"
                android:text="@string/guide_interest_welcome"
                android:textColor="@color/color_1A1A1A"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ivLogo" />

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/ivContinueLogin"
                android:layout_width="@dimen/dp_54"
                android:layout_height="@dimen/dp_54"
                android:src="@drawable/placeholder_corner_360"
                android:visibility="gone"
                android:layout_marginTop="@dimen/dp_20"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ivLoginAccountTip"
                app:shapeAppearance="@style/circleStyle" />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvContinueLogin"
                style="@style/MetaTextView.S14.PoppinsBold700"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_12"
                android:gravity="center"
                android:paddingBottom="@dimen/dp_4"
                android:textColor="@color/neutral_color_1"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/ivContinueLogin"
                tools:text="fdhsjkfgabhjlasdfhklsa" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clLoginByPhone"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/dp_20"
                app:layout_constraintTop_toBottomOf="@+id/tvContinueLogin"
                >
                <View
                    android:id="@+id/v_phone_login_view"
                    style="@style/Button.S18.PoppinsBlack900.Height46"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_48"
                    android:layout_marginHorizontal="@dimen/dp_24"
                    app:layout_constraintBottom_toTopOf="@id/tvOr"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <ImageView
                    android:id="@+id/ivPhoneIcon"
                    android:layout_height="@dimen/dp_20"
                    android:layout_width="@dimen/dp_20"
                    android:src="@drawable/ic_mobile_phone_1a1a1a"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintBottom_toBottomOf="@id/tv_phone_login"
                    app:layout_constraintTop_toTopOf="@id/tv_phone_login"
                    app:layout_constraintStart_toStartOf="@id/v_phone_login_view"
                    app:layout_constraintEnd_toStartOf="@id/tv_phone_login"
                    />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_phone_login"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:layout_marginStart="@dimen/dp_4"
                    android:text="@string/login_by_phone"
                    style="@style/MetaTextView.S16.PoppinsSemiBold600"
                    android:textColor="@color/color_1A1A1A"
                    app:iconSrc="@drawable/ic_mobile_phone"
                    app:layout_constraintBottom_toBottomOf="@id/v_phone_login_view"
                    app:layout_constraintEnd_toEndOf="@id/v_phone_login_view"
                    app:layout_constraintStart_toEndOf="@id/ivPhoneIcon"
                    app:layout_constraintTop_toTopOf="@id/v_phone_login_view" />


                <View
                    android:id="@+id/vLineOrL"
                    android:layout_width="@dimen/dp_125"
                    android:layout_height="@dimen/dp_1"
                    android:background="@drawable/ic_gradient_line_left"
                    android:layout_marginEnd="@dimen/dp_19"
                    app:layout_constraintBottom_toBottomOf="@id/tvOr"
                    app:layout_constraintEnd_toStartOf="@id/tvOr"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tvOr" />

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tvOr"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_24"
                    android:paddingHorizontal="@dimen/dp_8"
                    android:text="@string/intl_or_all_cap"
                    android:textColor="@color/black_50"
                    app:layout_constraintEnd_toStartOf="@id/vLineOrR"
                    app:layout_constraintStart_toEndOf="@id/vLineOrL"
                    app:layout_constraintTop_toBottomOf="@id/v_phone_login_view"
                    app:layout_constraintVertical_chainStyle="packed" />

                <View
                    android:id="@+id/vLineOrR"
                    android:layout_width="@dimen/dp_125"
                    android:layout_height="@dimen/dp_1"
                    android:layout_marginStart="@dimen/dp_19"
                    android:background="@drawable/ic_gradient_line_right"
                    app:layout_constraintBottom_toBottomOf="@id/tvOr"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tvOr"
                    app:layout_constraintTop_toTopOf="@id/tvOr" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_account"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginTop="@dimen/dp_24"
                android:layout_marginHorizontal="@dimen/dp_24"
                android:background="@drawable/bg_white_round_12"
                android:hint="@string/intl_enter_account_or_email"
                android:textColorHint="@color/color_B3B3B3"
                app:boxCollapsedPaddingTop="@dimen/dp_10"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/color_B3B3B3"
                app:layout_constraintTop_toBottomOf="@id/clLoginByPhone">

                <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/et_account"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#00FFFFFF"
                    android:fontFamily="@font/poppins_regular_400"
                    android:maxLines="1"
                    android:paddingBottom="@dimen/dp_6"
                    android:singleLine="true"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_13" />
            </com.google.android.material.textfield.TextInputLayout>

            <View
                android:id="@+id/vAccountBlock"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clickable="true"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/input_account"
                app:layout_constraintEnd_toEndOf="@id/input_account"
                app:layout_constraintStart_toStartOf="@id/input_account"
                app:layout_constraintTop_toTopOf="@id/input_account" />

            <ImageView
                android:id="@+id/ivClearAccount"
                android:layout_width="@dimen/dp_40"
                android:layout_height="0dp"
                android:layout_marginEnd="@dimen/dp_8"
                android:paddingHorizontal="@dimen/dp_8"
                android:visibility="gone"
                tools:visibility="visible"
                android:src="@drawable/ic_clear_input"
                app:layout_constraintBottom_toBottomOf="@id/input_account"
                app:layout_constraintEnd_toEndOf="@id/input_account"
                app:layout_constraintTop_toTopOf="@id/input_account" />


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/input_password"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginHorizontal="@dimen/dp_24"
                android:layout_marginTop="@dimen/dp_12"
                android:background="@drawable/bg_white_round_12"
                android:hint="@string/please_enter_password"
                android:textColorHint="@color/color_B3B3B3"
                app:boxCollapsedPaddingTop="@dimen/dp_10"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                app:hintAnimationEnabled="true"
                app:hintTextAppearance="@style/hintAppearence"
                app:hintTextColor="@color/color_B3B3B3"
                app:layout_constraintTop_toBottomOf="@id/input_account">

                <com.socialplay.gpark.ui.view.AutoFillTextInputEditText
                    android:id="@+id/et_password"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#00FFFFFF"
                    android:fontFamily="@font/poppins_regular_400"
                    android:maxLines="1"
                    android:paddingBottom="@dimen/dp_6"
                    android:singleLine="true"
                    android:textColor="@color/color_1A1A1A"
                    android:textSize="@dimen/sp_13"
                    app:boxStrokeColor="@color/color_input_background" />
            </com.google.android.material.textfield.TextInputLayout>

            <ImageView
                android:id="@+id/ivClearPassword"
                android:layout_width="@dimen/dp_40"
                android:layout_height="0dp"
                android:paddingHorizontal="@dimen/dp_8"
                android:src="@drawable/ic_clear_input"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/input_password"
                app:layout_constraintEnd_toStartOf="@id/iv_password_visibility"
                app:layout_constraintTop_toTopOf="@id/input_password"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_password_visibility"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:paddingLeft="@dimen/dp_8"
                android:paddingEnd="@dimen/dp_16"
                android:src="@drawable/icon_login_visible_password"
                app:layout_constraintBottom_toBottomOf="@+id/input_password"
                app:layout_constraintEnd_toEndOf="@+id/input_password"
                app:layout_constraintTop_toTopOf="@+id/input_password" />

            <ImageView
                android:id="@+id/ivSwitchLoginWay"
                android:layout_height="@dimen/dp_0"
                android:layout_width="wrap_content"
                android:src="@drawable/ic_mobile_phone"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="@id/tvSwitchLoginWay"
                app:layout_constraintBottom_toBottomOf="@id/tvSwitchLoginWay"
                app:layout_constraintStart_toStartOf="@id/input_password"
                />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tvSwitchLoginWay"
                style="@style/MetaTextView.S12.PoppinsRegular400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_4"
                android:layout_marginTop="@dimen/dp_12"
                android:text="@string/login_by_gpark_id"
                android:visibility="gone"
                android:textColor="@color/color_666666"
                app:layout_constraintStart_toEndOf="@id/ivSwitchLoginWay"
                app:layout_constraintTop_toBottomOf="@id/input_password" />

            <View
                android:id="@+id/vSwitchLoginWay"
                android:layout_height="@dimen/dp_0"
                android:layout_width="@dimen/dp_0"
                app:layout_constraintStart_toStartOf="@id/ivSwitchLoginWay"
                app:layout_constraintEnd_toEndOf="@id/tvSwitchLoginWay"
                app:layout_constraintTop_toTopOf="@id/tvSwitchLoginWay"
                app:layout_constraintBottom_toBottomOf="@id/tvSwitchLoginWay"
                />

            <com.socialplay.gpark.ui.view.MetaTextView
                android:id="@+id/tv_login"
                style="@style/Button.S16.PoppinsSemibold600"
                android:layout_width="0dp"
                android:layout_height="@dimen/login_button_height"
                android:layout_marginTop="@dimen/dp_24"
                android:text="@string/intl_log_in"
                android:textColor="@color/neutral_color_1"
                app:layout_constraintEnd_toEndOf="@id/input_password"
                app:layout_constraintStart_toStartOf="@id/input_password"
                app:layout_constraintTop_toBottomOf="@id/tvSwitchLoginWay"
                app:layout_goneMarginTop="@dimen/dp_24" />

            <LinearLayout
                android:id="@+id/ll_forget_password"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_12"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_login">

                <com.socialplay.gpark.ui.view.MetaTextView
                    android:id="@+id/tv_forgot_password"
                    style="@style/MetaTextView.S14.PoppinsRegular400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/intl_forgot_questionmark"
                    android:textColor="@color/neutral_color_3"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_login" />
            </LinearLayout>

            <Space
                android:id="@+id/space"
                android:layout_height="@dimen/dp_30"
                android:layout_width="match_parent"
                app:layout_constraintTop_toBottomOf="@id/ll_forget_password"
                />

            <TextView
                android:id="@+id/tvThirdLoginHint"
                style="@style/MetaTextView.S11"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/third_party_login_method"
                android:textColor="@color/black_40"
                app:layout_constraintTop_toBottomOf="@id/space"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

            <View
                android:layout_width="@dimen/dp_125"
                android:layout_height="@dimen/dp_1"
                android:layout_marginEnd="@dimen/dp_19"
                android:background="@drawable/ic_gradient_line_left"
                app:layout_constraintBottom_toBottomOf="@id/tvThirdLoginHint"
                app:layout_constraintRight_toLeftOf="@id/tvThirdLoginHint"
                app:layout_constraintTop_toTopOf="@id/tvThirdLoginHint" />

            <View
                android:layout_width="@dimen/dp_125"
                android:layout_height="@dimen/dp_1"
                android:layout_marginStart="@dimen/dp_19"
                android:background="@drawable/ic_gradient_line_right"
                app:layout_constraintBottom_toBottomOf="@id/tvThirdLoginHint"
                app:layout_constraintLeft_toRightOf="@id/tvThirdLoginHint"
                app:layout_constraintTop_toTopOf="@id/tvThirdLoginHint" />

            <LinearLayout
                android:id="@+id/ll_login_by_other_sdk"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_44"
                android:layout_marginTop="@dimen/dp_15"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/tvThirdLoginHint"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/cl_agreement"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_32"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp_24"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">


        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cb_agree"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_3"
            android:background="@drawable/s_agree_check"
            android:button="@null"
            android:src="@drawable/icon_unchecked"
            />

        <com.socialplay.gpark.ui.view.MetaTextView
            android:id="@+id/tv_agreement"
            style="@style/MetaTextView.S12.PoppinsRegular400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:gravity="start"
            android:textColor="#53535E"
            android:textSize="@dimen/sp_12"
            app:uiLineHeight="@dimen/dp_18"
            android:breakStrategy="simple"
            android:hyphenationFrequency="normal"
            tools:ignore="RtlSymmetry"
            tools:text="I have read and agreed to the user agreement and privacy policy" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <View
        android:id="@+id/v_hot_check"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_60"
        android:layout_marginLeft="-12dp"
        android:layout_marginTop="-22dp"
        android:clickable="true"
        app:layout_constraintLeft_toLeftOf="@id/cl_agreement"
        app:layout_constraintTop_toTopOf="@id/cl_agreement" />
</androidx.constraintlayout.widget.ConstraintLayout>