package com.socialplay.gpark.util

import android.content.Context
import android.content.res.Configuration
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.socialplay.gpark.ui.view.MStaggeredLayoutManager
import com.airbnb.epoxy.EpoxyController
import com.socialplay.gpark.util.extension.isPad
import timber.log.Timber

/**
 * Pad横屏适配工具类
 *
 * 功能：
 * 1. 判断当前设备是否是pad横屏状态
 * 2. 提供每行Item数量的配置
 *
 * 使用方式：
 * 1. 获取当前状态：PadLandscapeUtil.getItemsPerRow(context)
 * 2. 判断是否是pad横屏：PadLandscapeUtil.isPadLandscape(context)
 * 3. 在Fragment中直接使用：fragment.itemsPerRow 或 fragment.isPadLandscape
 */
object PadAdapterUtil {

    // 配置常量(默认2列的情况)
    private const val ITEMS_PER_ROW_PHONE = 2  // 手机竖屏
    private const val ITEMS_PER_ROW_PAD = 4  // pad竖屏
    private const val ITEMS_PER_ROW_PAD_LANDSCAPE = 6  // pad横屏

    // 配置常量(默认3列的情况)
    private const val ITEMS_PER_ROW_PHONE_3 = 3  // 手机竖屏
    private const val ITEMS_PER_ROW_PAD_3 = 5  // pad竖屏
    private const val ITEMS_PER_ROW_PAD_LANDSCAPE_3 = 6  // pad横屏

    // 配置常量(默认4列的情况)
    private const val ITEMS_PER_ROW_PHONE_4 = 4  // 手机竖屏
    private const val ITEMS_PER_ROW_PAD_4 = 6  // pad竖屏
    private const val ITEMS_PER_ROW_PAD_LANDSCAPE_4 = 8  // pad横屏

    /**
     * 获取当前每行Item数量
     */
    fun getItemsPerRow(context: Context, defaultSize: Int? = 2): Int {
        return if (isPadLandscape(context)) {
            when (defaultSize) {
                2 -> ITEMS_PER_ROW_PAD_LANDSCAPE
                3 -> ITEMS_PER_ROW_PAD_LANDSCAPE_3
                4 -> ITEMS_PER_ROW_PAD_LANDSCAPE_4
                else -> throw IllegalArgumentException("Unsupported defaultSize: $defaultSize")
            }
        } else if (context.isPad) {
            when (defaultSize) {
                2 -> ITEMS_PER_ROW_PAD
                3 -> ITEMS_PER_ROW_PAD_3
                4 -> ITEMS_PER_ROW_PAD_4
                else -> throw IllegalArgumentException("Unsupported defaultSize: $defaultSize")
            }

        } else {
            when (defaultSize) {
                2 -> ITEMS_PER_ROW_PHONE
                3 -> ITEMS_PER_ROW_PHONE_3
                4 -> ITEMS_PER_ROW_PHONE_4
                else -> throw IllegalArgumentException("Unsupported defaultSize: $defaultSize")
            }
        }
    }

    /**
     * 判断当前是否是pad横屏
     */
    fun isPadLandscape(context: Context): Boolean {
        val isPad = context.isPad
        val isLandscape =
            context.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
        return isPad && isLandscape
    }

    /**
     * callback为空时，自动执行requestModelBuild。若不为空，则执行callback
     */
    fun configChangedByGrid(rv: RecyclerView?, itemsPerRow: Int, recommendController: EpoxyController?, isStaggeredGrid: Boolean = false, callback: (() -> Unit)? = null) {
        try {
            // 处理屏幕旋转，GridLayoutManager的spanCount
            if (isStaggeredGrid) {
                val currentLayoutManager = rv?.layoutManager
                when (currentLayoutManager) {
                    is MStaggeredLayoutManager -> {
                        if (currentLayoutManager.spanCount == itemsPerRow) {
                            return
                        }
                        Timber.tag("PadLandscapeUtil").d("屏幕旋转 - MStaggeredLayoutManager spanCount更新: ${currentLayoutManager.spanCount} -> $itemsPerRow")
                        currentLayoutManager.spanCount = itemsPerRow
                    }
                    is StaggeredGridLayoutManager -> {
                        if (currentLayoutManager.spanCount == itemsPerRow) {
                            return
                        }
                        Timber.tag("PadLandscapeUtil").d("屏幕旋转 - StaggeredGridLayoutManager spanCount更新: ${currentLayoutManager.spanCount} -> $itemsPerRow")
                        currentLayoutManager.spanCount = itemsPerRow
                    }
                }
            } else {
                val currentLayoutManager = rv?.layoutManager as? GridLayoutManager
                if (currentLayoutManager?.spanCount == itemsPerRow) {
                    return
                }
                Timber.tag("PadLandscapeUtil").d("屏幕旋转 - GridLayoutManager spanCount更新: ${currentLayoutManager?.spanCount} -> $itemsPerRow")
                currentLayoutManager?.spanCount = itemsPerRow
            }

            if (callback == null) {
                // 延迟重新构建EpoxyController，确保布局稳定后再进行重建
                rv?.post {
                    try {
                        recommendController?.requestModelBuild()
                    } catch (e: Exception) {
                        Timber.tag("PadLandscapeUtil")
                            .e("屏幕旋转 - requestModelBuild异常: ${e.message}")
                    }
                }
            } else {
                callback()
            }
        } catch (e: Exception) {
            // 主要是处理屏幕旋转时，该页面已经被回收的情况
            Timber.tag("PadLandscapeUtil").d("屏幕旋转 - 异常: ${e.message}")
        }
    }
} 