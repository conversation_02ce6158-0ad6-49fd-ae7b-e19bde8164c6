package com.socialplay.gpark.util.bitmap;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.media.ExifInterface;
import android.view.View;

import androidx.annotation.Nullable;

import com.socialplay.gpark.util.ScreenUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import timber.log.Timber;

public class BitmapUtil {

    public static Bitmap getBitmap(View view) {
        if (view == null) {
            return null;
        }
        Bitmap bmp = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.RGB_565);
        Canvas canvas = new Canvas(bmp);
        // 这里一定要设置canvas画布为白色，因为bitmap config是RGB_565,不透明的
        // 如果插入有透明属性的View，而且不设置画布为白色，就会出现黑边。
        canvas.drawColor(Color.WHITE);
        view.draw(canvas);
        return bmp;
    }

    public static Bitmap getBitmapNoBackground(View view) {
        if (view == null) {
            return null;
        }
        Bitmap bmp = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.RGB_565);
        Canvas canvas = new Canvas(bmp);
        view.draw(canvas);
        return bmp;
    }

    public static Bitmap getBitmapNoBackground(View view, int removeTopHeight) {
        if (view == null) {
            return null;
        }
        int width = view.getWidth();
        int height = view.getHeight();
        int screenHeight = ScreenUtil.INSTANCE.getScreenHeight(view.getContext());
        Timber.tag("dhl").d("screenHeight:"+screenHeight+",view.height:"+height);
        int bottomHeight = height - removeTopHeight;
        Bitmap bmp = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
        Rect srcRect = new Rect(0, removeTopHeight, width, height);
        Rect rect = new Rect(0, 0, width, bottomHeight);
        Canvas canvas = new Canvas(bmp);
        canvas.translate(0, -removeTopHeight);
        canvas.drawBitmap(bmp, srcRect, rect, null);
        view.draw(canvas);
        canvas.setBitmap(null);
        return bmp;
    }

    public static Bitmap get8888Bitmap(View view) {
        if (view == null) {
            return null;
        }
        Bitmap bmp = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bmp);
        view.draw(canvas);
        return bmp;
    }

    public static int readPictureDegree(String path) {
        int degree = 0;
        try {
            ExifInterface exifInterface = new ExifInterface(path);
            int orientation = exifInterface.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    degree = 90;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    degree = 180;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    degree = 270;
                    break;
                default:
                    degree = 0;
                    break;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return degree;
    }

    public static void saveToLocal(Bitmap bitmap, File file, int quality, Bitmap.CompressFormat format) {
        try {
            if (format == null){
                format = Bitmap.CompressFormat.PNG;
            }
            file.mkdirs();
            if (file.exists()) {
                file.delete();
            }
            FileOutputStream out;
            out = new FileOutputStream(file);
            if (bitmap.compress(format, quality, out)) {
                out.flush();
                out.close();
            }
        } catch (Exception e) {
            Timber.e("saveToLocal: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 对图片进行压缩，主要是为了解决控件显示过大图片占用内存造成OOM问题,一般压缩后的图片大小应该和用来展示它的控件大小相近.
     *
     * @param reqWidth 期望压缩的宽度
     * @return 压缩后的图片
     */
    public static @Nullable
    Bitmap compressBitmapForWidth(String pathName, int reqWidth) {
        try {
            final BitmapFactory.Options options = new BitmapFactory.Options();
            /*
             * 第一次解析时，inJustDecodeBounds设置为true，
             * 禁止为bitmap分配内存，虽然bitmap返回值为空，但可以获取图片大小
             */
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(pathName, options);
            int width = options.outWidth;
            int inSampleSize = 1;
            int degree = readPictureDegree(pathName);
            if (is90And270(degree)) {
                width = options.outHeight;
            }
            if (width > reqWidth) {
                int widthRatio = (int) Math.floor((float) width / (float) reqWidth);
                inSampleSize = widthRatio;
            }
            options.inSampleSize = inSampleSize;
            // 使用计算得到的inSampleSize值再次解析图片
            options.inJustDecodeBounds = false;

            Bitmap bitmap = BitmapFactory.decodeFile(pathName, options);
            if (bitmap == null) {
                return null;
            }
            return scaleBitmapForWidth(bitmap, reqWidth, degree);
        } catch (Exception e) {
            Timber.e(e);
            return null;
        }
    }


    private static Bitmap scaleBitmapForWidth(Bitmap bitmap, int newWidth, int degree) {
        int bWidth = bitmap.getWidth();
        int bHeight = bitmap.getHeight();
        if (is90And270(degree)) {
            //如果图片出现旋转，则调换宽高换算缩放率
            bWidth = bitmap.getHeight();
            bHeight = bitmap.getWidth();
        }
        int minLength = Math.min(bHeight, bWidth);
        float initScale = newWidth * 1.0f / minLength;
        return createBitmapForMatrix(bitmap, initScale, degree);
    }

    private static boolean is90And270(int degree) {
        return degree > 0 && (degree == 90 || degree % 270 == 0);
    }

    private static Bitmap createBitmapForMatrix(Bitmap bitmap, float initScale, int degree) {
        Matrix matrix = new Matrix();
        matrix.postRotate(degree);
        matrix.postScale(initScale, initScale);
        try {
            return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
        } catch (OutOfMemoryError e) {
            Timber.e(e);
            return null;
        }
    }

    /**
     * 按照指定的宽高比例,对源Bitmap进行裁剪
     * 注意，输出的Bitmap只是宽高比与指定宽高比相同，大小未必相同
     *
     * @param srcBitmap 源图片对应的Bitmap
     * @param desWidth  目标图片宽度
     * @param desHeight 目标图片高度
     * @return Bitmap
     */
    public static Bitmap centerCrop(Bitmap srcBitmap, int desWidth, int desHeight) {
        int srcWidth = srcBitmap.getWidth();
        int srcHeight = srcBitmap.getHeight();
        int newWidth = srcWidth;
        int newHeight = srcHeight;
        float srcRate = (float) srcWidth / srcHeight;
        float desRate = (float) desWidth / desHeight;
        int dx = 0, dy = 0;
        if (srcRate == desRate) {
            return srcBitmap;
        } else if (srcRate > desRate) {
            newWidth = (int) (srcHeight * desRate);
            dx = (srcWidth - newWidth) / 2;
        } else {
            newHeight = (int) (srcWidth / desRate);
            dy = (srcHeight - newHeight) / 2;
        }
        //创建目标Bitmap，并用选取的区域来绘制
        return Bitmap.createBitmap(srcBitmap, dx, dy, newWidth, newHeight);
    }
}
