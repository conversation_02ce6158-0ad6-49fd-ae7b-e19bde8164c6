package com.socialplay.gpark.function.router

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Process
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.clearFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import androidx.navigation.NavOptionsBuilder
import androidx.navigation.Navigator
import androidx.navigation.fragment.findNavController
import androidx.navigation.navOptions
import com.airbnb.mvrx.asMavericksArgs
import com.meta.box.biz.friend.model.LabelInfo
import com.meta.box.biz.h5config.model.H5PageConfigItem
import com.meta.lib.mwbiz.MWBizBridge
import com.meta.verse.lib.MetaVerseCore
import com.meta.web.WebRouter
import com.mw.develop.MWDevelopRouter
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.contract.web.extra
import com.socialplay.gpark.contract.web.gameId
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.PublishPostInteractor
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.SsoLoginRequest
import com.socialplay.gpark.data.model.account.EmailScene
import com.socialplay.gpark.data.model.account.ProfileLinkInfo
import com.socialplay.gpark.data.model.aibot.BotLabelInfo
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.creator.label.CommonLabelInfo
import com.socialplay.gpark.data.model.creator.label.KolCreatorLabel
import com.socialplay.gpark.data.model.editor.DefaultRoleInfo
import com.socialplay.gpark.data.model.gift.SendGiftConditionsInfo
import com.socialplay.gpark.data.model.groupchat.GroupChatApplyInfo
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.data.model.groupchat.GroupChatInfo
import com.socialplay.gpark.data.model.guide.GuideScene
import com.socialplay.gpark.data.model.login.LoginCompleteEvent
import com.socialplay.gpark.data.model.outfit.UgcAssetMutable
import com.socialplay.gpark.data.model.post.FeedJumpTabRelayData
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostMomentCard
import com.socialplay.gpark.data.model.post.PostPublish
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PostUgcDesignCard
import com.socialplay.gpark.data.model.post.PublishPostLinkBundle
import com.socialplay.gpark.data.model.qrcode.ScanEntry
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.data.model.share.ShareContent
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.data.model.user.SimpleUserLabelInfo
import com.socialplay.gpark.data.model.videofeed.RecommendVideoFeedArgs
import com.socialplay.gpark.data.model.videofeed.RecommendVideoListArgs
import com.socialplay.gpark.data.model.videofeed.VideoFeedApiResult
import com.socialplay.gpark.data.repository.EditorRepository
import com.socialplay.gpark.function.MetaRouterWrapper
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.analytics.resid.ResIdUtils
import com.socialplay.gpark.function.deeplink.JumpPublishPostUtil
import com.socialplay.gpark.function.deeplink.LinkData
import com.socialplay.gpark.function.deeplink.MetaDeepLink
import com.socialplay.gpark.function.deeplink.MetaDeepLink.PARAM_SOURCE_FROM
import com.socialplay.gpark.function.deeplink.ShareActiveHelper
import com.socialplay.gpark.function.deeplink.linkhandler.LoginHandler
import com.socialplay.gpark.function.im.RongImHelper
import com.socialplay.gpark.function.navigation.KeepViewNavigator
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.function.startup.core.StartupContext
import com.socialplay.gpark.ui.account.AccPwdV7Dialog
import com.socialplay.gpark.ui.account.AccPwdV7DialogArgs
import com.socialplay.gpark.ui.account.AccountAndPasswordBindFragmentArgs
import com.socialplay.gpark.ui.account.AccountCancellationFragmentArgs
import com.socialplay.gpark.ui.account.CityResetFragmentArgs
import com.socialplay.gpark.ui.account.InformationResetFragmentArgs
import com.socialplay.gpark.ui.account.NickNameResetFragmentArgs
import com.socialplay.gpark.ui.account.PasswordChangeFragmentArgs
import com.socialplay.gpark.ui.account.PasswordSetFragmentArgs
import com.socialplay.gpark.ui.account.guide.GuideLoginFragmentArgs
import com.socialplay.gpark.ui.account.profile.EditProfileFragment.Companion.KEY_EDIT_PROFILE_RESULT
import com.socialplay.gpark.ui.account.profile.EditProfileFragment.Companion.PARAM_EDIT_PROFILE_RESULT
import com.socialplay.gpark.ui.account.profile.EditProfileFragmentArgs
import com.socialplay.gpark.ui.account.setting.SettingFragmentArgs
import com.socialplay.gpark.ui.account.setting.account.AccountSettingFragment.Companion.KEY_DITOUT_VERIFY_CODE
import com.socialplay.gpark.ui.account.setting.account.AccountSettingFragmentArgs
import com.socialplay.gpark.ui.account.startup.CreateAvatarFragmentArgs
import com.socialplay.gpark.ui.account.startup.EnterNameFragmentArgs
import com.socialplay.gpark.ui.account.startup.SelectBirthdayFragmentArgs
import com.socialplay.gpark.ui.account.verify.EmailVerifyFragmentArgs
import com.socialplay.gpark.ui.aibot.AiBotConversationFragmentArgs
import com.socialplay.gpark.ui.aibot.detail.AiBotDetailFragmentArgs
import com.socialplay.gpark.ui.aibot.tag.AiBotTagDialog
import com.socialplay.gpark.ui.aibot.tag.AiBotTagDialogArgs
import com.socialplay.gpark.ui.aibot.ugc.AIBotUGCCreateFragmentArgs
import com.socialplay.gpark.ui.aibot.ugc.AIBotUGCGenderDialog
import com.socialplay.gpark.ui.aibot.ugc.clip.AIBotUGCImageClipFragment
import com.socialplay.gpark.ui.aibot.ugc.create.AIBotUGCCreateResultFragmentArgs
import com.socialplay.gpark.ui.aibot.ugc.generate.AIBotUGCGenerateFragmentArgs
import com.socialplay.gpark.ui.aibot.ugc.select.AIBotUGCSelectFragmentArgs
import com.socialplay.gpark.ui.asset.AssetListFragmentArgs
import com.socialplay.gpark.ui.auth.LoginConfirmFragmentArgs
import com.socialplay.gpark.ui.base.RootNavHostFragmentActivity
import com.socialplay.gpark.ui.developer.dialog.SimpleSelectTxtDialogFragment
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.LoadingDialogFragment
import com.socialplay.gpark.ui.editor.backups.UgcBackupFragmentArgs
import com.socialplay.gpark.ui.editor.create.EditorCreateFragmentArgs
import com.socialplay.gpark.ui.editor.create.EditorCreateV2FragmentArgs
import com.socialplay.gpark.ui.editor.legecy.RenameLocalDialogArgs
import com.socialplay.gpark.ui.editor.module.UgcModuleTabArgs
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivity
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.editorschoice.ugc.UgcAllFragmentArgs
import com.socialplay.gpark.ui.exoplayer.VideoPlayerFullScreenFragmentArgs
import com.socialplay.gpark.ui.feedback.FeedbackFragmentArgs
import com.socialplay.gpark.ui.friend.contacts.ContactListFragment
import com.socialplay.gpark.ui.friend.contacts.ContactListFragmentArgs
import com.socialplay.gpark.ui.friend.friendapply.FriendApplyFragmentArgs
import com.socialplay.gpark.ui.gamedetail.sendflower.CustomizeFlowerCountDialog
import com.socialplay.gpark.ui.gamedetail.sendflower.SendFlowerConditionDialog
import com.socialplay.gpark.ui.gamedetail.sendflower.SendFlowerDialog
import com.socialplay.gpark.ui.gamedetail.unify.PgcGameDetailFragmentArgs
import com.socialplay.gpark.ui.gamedetail.unify.UgcGameDetailFragmentArgs
import com.socialplay.gpark.ui.gamepay.GamePayLifecycle
import com.socialplay.gpark.ui.gamepay.PayInfo
import com.socialplay.gpark.ui.gamereview.ReviewListFragmentArgs
import com.socialplay.gpark.ui.gamereview.dialog.GameReviewDialogArgs
import com.socialplay.gpark.ui.gamereview.edit.EditGameReviewFragmentArgs
import com.socialplay.gpark.ui.im.ConversationListFragmentArgs
import com.socialplay.gpark.ui.im.activities.SysDetailListFragmentArgs
import com.socialplay.gpark.ui.im.conversation.BaseChatFragment
import com.socialplay.gpark.ui.im.conversation.EditGroupChatJoinInviteRuleDialog
import com.socialplay.gpark.ui.im.conversation.EditGroupChatJoinInviteRuleDialogArgs
import com.socialplay.gpark.ui.im.conversation.EditGroupChatNameDialog
import com.socialplay.gpark.ui.im.conversation.EditGroupChatNameDialogArgs
import com.socialplay.gpark.ui.im.conversation.EditGroupDescFragment
import com.socialplay.gpark.ui.im.conversation.EditGroupDescFragmentArgs
import com.socialplay.gpark.ui.im.conversation.GroupChatFragmentArgs
import com.socialplay.gpark.ui.im.conversation.GroupChatProfileFragment
import com.socialplay.gpark.ui.im.conversation.GroupChatProfileFragmentArgs
import com.socialplay.gpark.ui.im.conversation.GroupChatProfileGuestFragment
import com.socialplay.gpark.ui.im.conversation.GroupChatProfileGuestFragmentArgs
import com.socialplay.gpark.ui.im.conversation.PrivateChatFragmentArgs
import com.socialplay.gpark.ui.im.conversation.RequestJoinGroupFragment
import com.socialplay.gpark.ui.im.conversation.RequestJoinGroupFragmentArgs
import com.socialplay.gpark.ui.im.groupchat.GroupChatConfig
import com.socialplay.gpark.ui.im.groupchat.GroupChatMembersFragment
import com.socialplay.gpark.ui.im.groupchat.GroupChatMembersFragmentArgs
import com.socialplay.gpark.ui.im.groupchat.GroupJoinRequestApprovalDialog
import com.socialplay.gpark.ui.im.groupchat.GroupJoinRequestApprovalDialogArgs
import com.socialplay.gpark.ui.im.groupchat.HeGroupsListFragmentArgs
import com.socialplay.gpark.ui.im.groupchat.MyGroupsParentFragmentArgs
import com.socialplay.gpark.ui.im.setting.ChatSettingFragment.Companion.KEY_CHATSETTING_RESULT_CLEAR_MSG
import com.socialplay.gpark.ui.im.setting.ChatSettingFragment.Companion.KEY_CHATSETTING_RESULT_DELETE_FRIEND
import com.socialplay.gpark.ui.im.setting.ChatSettingFragment.Companion.KEY_CHATSETTING_RESULT_REMARK
import com.socialplay.gpark.ui.im.setting.ChatSettingFragmentArgs
import com.socialplay.gpark.ui.im.setting.RemarkAlertFragment.Companion.KEY_REMARK_RESULT
import com.socialplay.gpark.ui.im.setting.RemarkAlertFragmentArgs
import com.socialplay.gpark.ui.kol.creator.label.LabelKolCreatorFragmentArgs
import com.socialplay.gpark.ui.kol.creator.type.TypeKoCreatorParentFragment
import com.socialplay.gpark.ui.kol.creator.type.TypeKoCreatorParentFragmentArgs
import com.socialplay.gpark.ui.kol.game.KolMoreUgcFragment
import com.socialplay.gpark.ui.kol.game.KolMoreUgcFragmentArgs
import com.socialplay.gpark.ui.kol.popup.KolGameLabelDialog
import com.socialplay.gpark.ui.kol.popup.KolGameLabelDialogArgs
import com.socialplay.gpark.ui.locale.LanguageRecreateActivity
import com.socialplay.gpark.ui.login.LoginActivity
import com.socialplay.gpark.ui.login.LoginFragmentArgs
import com.socialplay.gpark.ui.main.MainActivity
import com.socialplay.gpark.ui.main.MainBottomNavigationItem
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.main.task.WelfareTaskFragmentArgs
import com.socialplay.gpark.ui.moments.MomentsFragmentArgs
import com.socialplay.gpark.ui.moments.list.MomentsTypeListFragmentArgs
import com.socialplay.gpark.ui.outfit.UgcAssetListArgs
import com.socialplay.gpark.ui.outfit.UgcAssetListState
import com.socialplay.gpark.ui.outfit.UgcDesignDetailArgs
import com.socialplay.gpark.ui.outfit.UgcDesignEditFragmentArgs
import com.socialplay.gpark.ui.pay.BuyCoinsFragmentArgs
import com.socialplay.gpark.ui.permission.Permission
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.ui.plot.chooseimage.DuplicateImageActivity
import com.socialplay.gpark.ui.plot.chooseimage.DuplicateImageActivityArgs
import com.socialplay.gpark.ui.plot.chooseimage.LocalPictureSelectorActivity
import com.socialplay.gpark.ui.plot.chooseimage.LocalPictureSelectorActivityArgs
import com.socialplay.gpark.ui.plot.chooseimage.PlotChooseImageActivity
import com.socialplay.gpark.ui.plot.chooseimage.PlotClipImageActivityArgs
import com.socialplay.gpark.ui.plot.chooseimage.PlotClipImageFragmentArgs
import com.socialplay.gpark.ui.post.card.AddCardFragmentDialogArgs
import com.socialplay.gpark.ui.post.card.AddGameCardFrameFragmentDialog
import com.socialplay.gpark.ui.post.topic.detail.TopicDetailTabFragmentArgs
import com.socialplay.gpark.ui.post.v2.AddTagDialog
import com.socialplay.gpark.ui.post.v2.PostDetailFragmentArgs
import com.socialplay.gpark.ui.post.v2.PostRuleDialog
import com.socialplay.gpark.ui.post.v2.PublishPostFragmentArgs
import com.socialplay.gpark.ui.profile.MeProfileFragment
import com.socialplay.gpark.ui.profile.fans.UserFansTabFragmentDialogArgs
import com.socialplay.gpark.ui.profile.home.ProfileArgs
import com.socialplay.gpark.ui.profile.link.AddLinkDialog
import com.socialplay.gpark.ui.profile.link.EditLinkFragmentArgs
import com.socialplay.gpark.ui.qrcode.CameraPermissionDialog
import com.socialplay.gpark.ui.qrcode.CameraPermissionDialogArgs
import com.socialplay.gpark.ui.qrcode.QRCodeScanFragmentArgs
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialogArgs
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.reportBlock.ViolateRulesDialog
import com.socialplay.gpark.ui.reportBlock.ViolateRulesDialogArgs
import com.socialplay.gpark.ui.reportBlock.user.AppReportUserFragment
import com.socialplay.gpark.ui.reportBlock.user.AppReportUserFragmentArgs
import com.socialplay.gpark.ui.share.CommunityShareDialogArgs
import com.socialplay.gpark.ui.template.TemplateListDetailFragmentArgs
import com.socialplay.gpark.ui.toolkit.ToolkitRecommendFragmentArgs
import com.socialplay.gpark.ui.videofeed.publish.VideoPublishRuleDialog
import com.socialplay.gpark.ui.web.WebActivity
import com.socialplay.gpark.ui.web.WebActivityArgs
import com.socialplay.gpark.ui.web.WebFragmentArgs
import com.socialplay.gpark.ui.web.WebViewDialog
import com.socialplay.gpark.ui.web.WebViewDialogArgs
import com.socialplay.gpark.util.GsonUtil
import com.socialplay.gpark.util.ProcessUtil
import com.socialplay.gpark.util.extension.canShowDialog
import com.socialplay.gpark.util.extension.clearFragmentResultAndListenerByActivity
import com.socialplay.gpark.util.extension.findNavControllerOrNull
import com.socialplay.gpark.util.extension.popBackStack
import com.socialplay.gpark.util.extension.resumeGameById
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.getStringByGlobal
import com.socialplay.gpark.util.toJSON
import org.greenrobot.eventbus.EventBus
import org.koin.androidx.viewmodel.ext.android.getSharedViewModel
import org.koin.core.context.GlobalContext
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/10
 * desc   :
 * </pre>
 */


class MetaRouter {

    object Control {

        fun getCommonNavOptionsBuilder(): NavOptions.Builder {
            return NavOptions.Builder().setPopUpTo(-1, false).setEnterAnim(R.anim.base_page_enter).setExitAnim(R.anim.base_page_exit).setPopEnterAnim(R.anim.base_page_pop_enter).setPopExitAnim(R.anim.base_page_pop_exit)
        }

        fun getCommonFadeNavOptionsBuilder(): NavOptions.Builder {
            return NavOptions.Builder().setPopUpTo(-1, false).setEnterAnim(R.anim.base_page_enter).setExitAnim(R.anim.base_page_exit).setPopEnterAnim(R.anim.base_page_pop_enter).setPopExitAnim(R.anim.base_page_pop_exit)
        }

        fun navigate(
            fragment: Fragment, graphDestId: Int, navData: Bundle? = null, navOptions: NavOptions? = null, navExtras: Navigator.Extras? = null, isPrePop: Boolean = false
        ) {
//            if (isPrePop) {
//                fragment.findNavController().popBackStack()
//            }
            val options = navOptions ?: getCommonNavOptionsBuilder().build()
            if (fragment.isAdded && fragment.fragmentManager != null) {
                fragment.findNavController().navigate(graphDestId, navData, options, navExtras)
            }
        }

        fun navigate(
            navigator: NavController, graphDestId: Int, navData: Bundle? = null, navOptions: NavOptions? = null, navExtras: Navigator.Extras? = null
        ) {
            val options = navOptions ?: getCommonNavOptionsBuilder().build()
            navigator.navigate(graphDestId, navData, options, navExtras)
        }
    }

    object Main {

        fun navigate(fragment: Fragment, optionsBlock: (NavOptionsBuilder.() -> Unit)? = null) {
            val backStackEntry = kotlin.runCatching { fragment.findNavController().getBackStackEntry(R.id.main) }.getOrNull()
            if (backStackEntry == null) {
                val navOptions = FadeNavOptions.getFadeNavOptions(true)
                Control.navigate(fragment, R.id.main, bundleOf(KeepViewNavigator.ARG_KEEP to true), optionsBlock?.let { navOptions { it() } } ?: navOptions)
            } else {
                fragment.findNavController().popBackStack(R.id.main, false)
            }
        }

        /**
         * @param [source] 来源 [com.socialplay.gpark.function.deeplink.LinkData]
         */
        fun dispatchUrl(activity: Activity, uri: Uri?, source: String) {
            activity.startActivity(mainActivityIntent(activity, source).also { it.data = uri })
        }

        /**
         * @param [source] 来源 [com.socialplay.gpark.function.deeplink.LinkData]
         */
        fun dispatchUrl(context: Context, uri: Uri?, source: String) {
            context.startActivity(mainActivityIntent(context, source).also { it.data = uri })
        }

        fun floatBallGameBack(context: Context, gameId: String?) {
            val intent = mainActivityIntent(context, LinkData.SOURCE_LOCAL, true)
            gameId?.let {
                intent.data = buildLocalJumpUri(
                    Bundle().apply {
                        putString(MetaDeepLink.PARAM_FROM_GAME_ID, gameId)
                    }, MetaDeepLink.ACTION_POP_UP_GAME_REVIEW, MetaDeepLink.ACTION_POP_UP_GAME_REVIEW
                )
            }
            context.startActivity(intent)
        }

        fun gameBack(context: Context) {
            context.startActivity(mainActivityIntent(context, LinkData.SOURCE_LOCAL))
        }

        fun tabHome(preFragment: Fragment?, context: Context) {
            // 其实最好在这里发个事件，登录相关的页面全部都pop
//            EventBus.getDefault().post(LoginCompleteEvent())
//            preFragment?.popBackStack()
            jumTab(context, MainBottomNavigationItem.PARTY.itemId, true)
        }

        /**
         * @param feedJumpTabRelayData 跳转携带数据
         */
        fun tabFeed(context: Context, feedJumpTabRelayData: FeedJumpTabRelayData?) {
            jumTab(
                context, MainBottomNavigationItem.FEED.itemId, true, data = feedJumpTabRelayData?.let {
                    bundleOf(
                        FeedJumpTabRelayData.BUNDLE_KEY to GsonUtil.safeToJson(
                            feedJumpTabRelayData
                        )
                    )
                })
        }


        fun toTab(context: Context, tab: MainBottomNavigationItem, data: Bundle? = null) {
            jumTab(context, tab.itemId, true, data = data)
        }


        private fun jumTab(context: Context, itemId: Int, local: Boolean, data: Bundle? = null) {
            ShareActiveHelper.jumpMain = true
            val intent = mainActivityIntent(context, LinkData.SOURCE_LOCAL)
            val bundle = Bundle().apply {
                putInt(MetaDeepLink.PARAM_TAB_ID, itemId)
                putBundle(MetaDeepLink.PARAM_BOTTOM_TAB_PENDING_DATA, data)
            }
            intent.data = if (local) {
                buildLocalJumpUri(
                    bundle, MetaDeepLink.ACTION_JUMP_TAB, MetaDeepLink.ACTION_JUMP_TAB
                )
            } else {
                buildJumpUri(
                    bundle, MetaDeepLink.ACTION_JUMP_TAB, MetaDeepLink.ACTION_JUMP_TAB
                )
            }
            context.startActivity(intent)
        }

        private fun buildLocalJumpUri(bundle: Bundle, action: String, dest: String): Uri {
            return MetaRouterWrapper.Main.buildLocalJumpUri(bundle, action, dest)
        }

        fun buildLocalJumpUri(map: Map<String, Any?>, action: String, dest: String): Uri {
            return MetaRouterWrapper.Main.buildLocalJumpUri(map, action, dest)
        }

        /**
         * 需要翻墙的deeplink
         */
        private fun buildJumpUri(bundle: Bundle, action: String, dest: String): Uri {
            return MetaRouterWrapper.Main.buildJumpUri(bundle, action, dest)
        }


        fun buildLocalJumpUri(action: String, data: Map<String, Any?>): Uri {
            return MetaRouterWrapper.Main.buildLocalJumpUri(action, data)
        }

        /**
         * 需要翻墙的deeplink
         */
        fun buildJumpUri(action: String, data: Map<String, Any?>): Uri {
            return MetaRouterWrapper.Main.buildJumpUri(action, data)
        }


        /**
         * 不需要翻墙的deeplink
         */
        private fun buildLocalJumpUri(json: String, action: String, dest: String): Uri {
            return MetaRouterWrapper.Main.buildLocalJumpUri(json, action, dest)
        }

        /**
         * 需要翻墙的deeplink
         */
        private fun buildJumpUri(json: String, action: String, dest: String): Uri {
            return MetaRouterWrapper.Main.buildJumpUri(json, action, dest)
        }

        /**
         * @param [source] 来源 [com.socialplay.gpark.function.deeplink.LinkData]
         */
        fun mainActivityIntent(
            context: Context, source: String, isExitGame: Boolean = false
        ): Intent {
            val intent = Intent(context, MainActivity::class.java)
            if (!isExitGame && ProcessUtil.isMProcess(context)) {
                intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            } else {
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            intent.putExtra(
                PARAM_SOURCE_FROM, if (source.isNullOrEmpty()) LinkData.SOURCE_UNKNOWN else source
            )
            return intent
        }

        /**
         * 根据json获取跳转Intent
         *
         * @param clickIntentJson 点击后操作
         */
        fun getPushIntent(context: Context, clickIntentJson: String?): Intent {
            val intent = mainActivityIntent(context, LinkData.SOURCE_LOCAL)
            if (!clickIntentJson.isNullOrEmpty()) {
                intent.putExtra("push_json", clickIntentJson)
            }
            return intent
        }

        fun openUGCGameDetailFromGame(
            context: Context, ugcId: String, parentId: String?, resIdBean: ResIdBean
        ) {
            runCatching {
                if (ugcId.isNullOrBlank()) {
                    context.toast(context.getString(R.string.invalid_ugcid))
                } else {
                    val intent = mainActivityIntent(context, LinkData.SOURCE_LOCAL)
                    intent.data = buildLocalJumpUri(
                        Bundle().apply {
                            putBundle(
                                MetaDeepLink.PARAM_EXTRA_BUNDLE, UgcGameDetailFragmentArgs(
                                    ugcId = ugcId, parentId = parentId.orEmpty(), resIdBean = resIdBean, targetCommentId = null, targetReplyId = null, source = UgcGameDetailFragmentArgs.DEFAULT_SOURCE
                                ).asMavericksArgs()
                            )
                        }, MetaDeepLink.ACTION_JUMP_UGC_GAME_DETAIL_FROM_GAME, MetaDeepLink.ACTION_JUMP_UGC_GAME_DETAIL_FROM_GAME
                    )
                    context.startActivity(intent)
                }
            }
        }

        /**
         * @param completeMedias 与 briefMedias选其一
         * @param briefMedias 路径列表，与 completeMedias选其一
         */
        fun openPostPublishFromGame(
            context: Context,
            content: String?,
            briefMedias: ArrayList<String>?,
            completeCardList: ArrayList<PostCardInfo>?,
            tagIdList: ArrayList<String>?,
            clearTopBackFeed: Boolean,
            fromGameId: String,
            fromPkgName: String,
            showCategoryId: Int? = null,
            templateId: Int? = null,
            customCacheKey: String? = null,
            enableOutfitShare: Boolean = false,
            isFromMoment: Boolean = false,
            moments: List<PostMomentCard>? = null
        ) {
            val intent = mainActivityIntent(context, LinkData.SOURCE_LOCAL)
            intent.data = buildLocalJumpUri(
                GsonUtil.safeToJson(
                    PublishPostLinkBundle(
                        content, tagIdList, briefMedias, completeCardList, clearTopBackFeed, fromGameId, fromPkgName, showCategoryId, templateId, customCacheKey, enableOutfitShare, isFromMoment, moments
                    )
                ), MetaDeepLink.ACTION_JUMP_POST_PUBLISH, MetaDeepLink.ACTION_JUMP_POST_PUBLISH
            )
            context.startActivity(intent)
        }

        fun gameToQrCode(
            context: Context, packageName: String, gameId: String
        ) {
            val intent = mainActivityIntent(context, LinkData.SOURCE_LOCAL)
            val isTs = ProcessUtil.getProcessName(context) == "${context.packageName}:m"
            intent.data = buildLocalJumpUri(
                Bundle().apply {
                    putString(MetaDeepLink.PARAM_FROM_GAME_PACKAGE_NAME, packageName)
                    putString(MetaDeepLink.PARAM_FROM_GAME_ID, gameId)
                    putString(MetaDeepLink.PARAM_FROM_GAME_TYPE, if (isTs) "TS" else "APK")
                }, MetaDeepLink.ACTION_SCAN, MetaDeepLink.ACTION_SCAN
            )
            context.startActivity(intent)
        }

        fun gameToCharge(context: Context) {
            val intent = mainActivityIntent(context, LinkData.SOURCE_LOCAL)
            intent.apply {
                putExtra(MetaDeepLink.KEY_JUMP_ACTION, MetaDeepLink.ACTION_CHARGE)
            }
            context.startActivity(intent)
        }

        fun gameToLogin(context: Context, source: LoginSource) {
            val intent = mainActivityIntent(context, source.source)
            val isTs = ProcessUtil.getProcessName(context) == "${context.packageName}:m"
            intent.data = buildLocalJumpUri(
                Bundle().apply {
                    putString(
                        MetaDeepLink.PARAM_FROM_GAME_PACKAGE_NAME, MetaVerseCore.bridge().currentGamePkg()
                    )
                    putString(
                        MetaDeepLink.PARAM_FROM_GAME_ID, MetaVerseCore.bridge().currentGameId()
                    )
                    putString(MetaDeepLink.PARAM_FROM_GAME_TYPE, if (isTs) "TS" else "APK")
                    putString(MetaDeepLink.PARAM_SOURCE_FROM, source.source)
                }, MetaDeepLink.ACTION_LOGIN, MetaDeepLink.ACTION_LOGIN
            )
            context.startActivity(intent)
        }

        fun bindAccountAndPassword(context: Context, source: LoginPageSource, gameId: String?) {
            val intent = mainActivityIntent(context, source.source)
            val isTs = ProcessUtil.getProcessName(context) == "${context.packageName}:m"
            intent.data = buildLocalJumpUri(
                Bundle().apply {
                    putString(MetaDeepLink.PARAM_FROM_GAME_ID, gameId)
                    putString(MetaDeepLink.PARAM_SOURCE_FROM, source.source)
                    putString(MetaDeepLink.PARAM_FROM_GAME_TYPE, if (isTs) "TS" else "APK")
                }, MetaDeepLink.ACTION_BIND_ACCOUNT, MetaDeepLink.ACTION_BIND_ACCOUNT
            )
            context.startActivity(intent)
        }

        fun gameToEditProfile(context: Context, packageName: String, gameId: String) {
            val intent = mainActivityIntent(context, LinkData.SOURCE_LOCAL)
            val isTs = ProcessUtil.getProcessName(context) == "${context.packageName}:m"
            intent.data = buildLocalJumpUri(
                Bundle().apply {
                    putString(MetaDeepLink.PARAM_FROM_GAME_PACKAGE_NAME, packageName)
                    putString(MetaDeepLink.PARAM_FROM_GAME_ID, gameId)
                    putString(MetaDeepLink.PARAM_FROM_GAME_TYPE, if (isTs) "TS" else "APK")
                }, MetaDeepLink.ACTION_EDIT_PROFILE, MetaDeepLink.ACTION_EDIT_PROFILE
            )
            context.startActivity(intent)
        }

        fun jumpScheme(context: Context, url: String, source: String) {
            val uri = Uri.parse(url)
            // 需要判断如果scheme是gpark，则不走ACTION_VIEW的action（处理安装马甲包的手机会拉起选择app弹窗的问题）
            if (uri.scheme == BuildConfig.SCHEME_URI) {
                Main.dispatchUrl(context, uri, source)
            } else {
                context.startActivity(Intent(Intent.ACTION_VIEW, uri))
            }
        }

        fun resumeToRole(activity: Activity) {
            activity.startActivity(Intent(activity, FullScreenEditorActivity::class.java).apply {
                putExtras(FullScreenEditorActivityArgs(categoryId = CategoryId.SCHEME_DEFAULT).toBundle())
                addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            })
        }

        fun anyToFeedback(
            activity: Activity,
            fragment: Fragment?,
            feedbackGameId: String?,
            source: String,
            schemeSource: String,
            defaultSelectType: String?,
        ) {
            // 这里可能存在问题。如果是在当前进程内，web的Fragment可能会没有被隐藏起来，导致跳转到反馈页面后，还能点击后面的Fragment
            if ((activity is MainActivity || activity is RootNavHostFragmentActivity) && StartupContext.isMainProcess && fragment != null && !fragment.isDetached) {
                Feedback.feedback(
                    fragment, feedbackGameId, source, defaultSelectType, needBackRole = false, needBackGame = false, fromGameId = null
                )
                return
            }
            if (activity is FullScreenEditorActivity && StartupContext.isMainProcess) {
                RootNavHostFragmentActivity.start(
                    activity, R.id.feedback, FeedbackFragmentArgs(
                        feedbackGameId, source, defaultSelectType, needBackRole = false, needBackGame = false, fromGameId = null
                    ).asMavericksArgs()
                )
                return
            }
            val intent = mainActivityIntent(activity, LinkData.SOURCE_LOCAL)
            val fromGameId = if (StartupContext.get().processType == StartupProcessType.M) {
                MetaVerseCore.bridge().currentGameId()
            } else {
                null
            }
            val needBackGame = !fromGameId.isNullOrEmpty()
            val needBackRole = activity is FullScreenEditorActivity
            if (needBackRole) {
                intent.flags = intent.flags and Intent.FLAG_ACTIVITY_CLEAR_TOP.inv()
                intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            }
            intent.data = buildLocalJumpUri(
                hashMapOf(
                    MetaDeepLink.PARAM_SOURCE to source,
                    MetaDeepLink.PARAM_SOURCE_FROM to schemeSource,
                    MetaDeepLink.KEY_NEED_BACK_ROLE to needBackRole,
                    MetaDeepLink.KEY_NEED_BACK_GAME to needBackGame,
                ).apply {
                    if (!feedbackGameId.isNullOrEmpty()) {
                        put(MetaDeepLink.PARAM_FEEDBACK_GAME_ID, feedbackGameId)
                    }
                    if (!fromGameId.isNullOrEmpty()) {
                        put(MetaDeepLink.KEY_FROM_GAME_ID, fromGameId)
                    }
                    if (!defaultSelectType.isNullOrEmpty()) {
                        put(MetaDeepLink.PARAM_DEFAULT_TYPE, defaultSelectType)
                    }
                }, MetaDeepLink.ACTION_FEED_BACK, MetaDeepLink.ACTION_FEED_BACK
            )
            activity.startActivity(intent)
        }

        fun anyToContacts(
            activity: Activity,
            fragment: Fragment?,
            shareRequestKey: String? = null,
            shareContent: String? = null,
            shareCallbackByEvent: Boolean = false,
        ) {
            if (activity is MainActivity && StartupContext.isMainProcess && fragment != null && !fragment.isDetached) {
                IM.goContacts(fragment, shareRequestKey, shareContent, shareCallbackByEvent)
                return
            }
            val intent = mainActivityIntent(activity, LinkData.SOURCE_LOCAL)
            val fromGameId = if (StartupContext.get().processType == StartupProcessType.M) {
                MetaVerseCore.bridge().currentGameId()
            } else {
                null
            }
            val needBackGame = !fromGameId.isNullOrEmpty()
            val needBackRole = activity is FullScreenEditorActivity
            if (needBackRole) {
                intent.flags = intent.flags and Intent.FLAG_ACTIVITY_CLEAR_TOP.inv()
                intent.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            }
            intent.data = buildLocalJumpUri(
                hashMapOf(
                    MetaDeepLink.PARAM_SOURCE_FROM to LinkData.SOURCE_LOCAL,
                    MetaDeepLink.KEY_NEED_BACK_ROLE to needBackRole,
                    MetaDeepLink.KEY_NEED_BACK_GAME to needBackGame,
                ).apply {
                    if (!shareRequestKey.isNullOrEmpty()) {
                        put(MetaDeepLink.PARAM_SHARE_REQUEST_KEY, shareRequestKey)
                    }
                    if (!shareContent.isNullOrEmpty()) {
                        put(MetaDeepLink.PARAM_SHARE_CONTENT, shareContent)
                    }
                    if (!fromGameId.isNullOrEmpty()) {
                        put(MetaDeepLink.KEY_FROM_GAME_ID, fromGameId)
                    }
                }, MetaDeepLink.ACTION_CONTACTS, MetaDeepLink.ACTION_CONTACTS
            )
            activity.startActivity(intent)
        }

        fun restartMainActivity(activity: Activity) {
            val intent = activity.packageManager.getLaunchIntentForPackage(activity.packageName)
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                activity.startActivity(intent)
            }
            activity.overridePendingTransition(0, 0);
            activity.finish()
        }

        fun restartProcess(activity: Activity) {
            val intent = activity.packageManager.getLaunchIntentForPackage(activity.packageName)
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                activity.startActivity(intent)
            }
            Process.killProcess(Process.myPid())
        }

        fun chooseImage(
            activity: Activity,
            count: Int,
            mimeType: Int,
            useCamera: Boolean,
            videoMaxSecond: Int?,
        ) {
            activity.startActivity(
                Intent(
                    activity, LocalPictureSelectorActivity::class.java
                ).apply {
                    putExtra(
                        LocalPictureSelectorActivity.EXTRA_KEY_PARAMS_PICTURE_SELECTOR, LocalPictureSelectorActivityArgs(
                            count,
                            mimeType,
                            useCamera,
                            videoMaxSecond,
                        )
                    )
                })
        }
    }

    object GameDetail {

        /**
         * @param isComment （废弃）展开评价页
         * @param expand 进入页面时设置为展开状态
         */
        fun navigate(
            fragment: Fragment,
            gameId: String,
            resIdBean: ResIdBean,
            packageName: String,
            fromGameId: String? = null,
            fromPkgName: String? = null,
            autoDownload: Boolean = false,
            type: String = "unknown",
            isComment: Boolean = false,
            expandPage: Boolean = false,
            targetCommentId: String? = null,
            targetReplyId: String? = null,
        ) {
            Analytics.track(EventConstants.EVENT_ITEM_CLICK) {
                put("gameid", gameId)
                put("packagename", packageName)
                put("game_type", type)
                putAll(
                    ResIdUtils.getAnalyticsMap(
                        resIdBean
                    )
                )
            }
            val args = PgcGameDetailFragmentArgs(
                gId = gameId,
                resIdBean = resIdBean,
                packageName = packageName,
                type = type,
                isComment = isComment,
                expandPage = expandPage,
                targetCommentId = targetCommentId,
                targetReplyId = targetReplyId,
                fromGameId = fromGameId,
                fromPkgName = fromPkgName,
                autoDownloadGame = autoDownload,
            ).asMavericksArgs()

            Control.navigate(fragment, R.id.pgc_game_detail, navData = args)
        }

        fun showSendFlowerConditionDialog(
            fragment: Fragment, gameId: String, gameType: Int, conditionsInfo: SendGiftConditionsInfo? = null, resultCallback: ((SendGiftConditionsInfo?) -> Unit)? = null
        ) {
            fragment.childFragmentManager.setFragmentResultListener(SendFlowerConditionDialog.REQUEST_KEY_SEND_FLOWER_CONDITIONS_INFO, fragment) { _, bundle ->
                bundle.apply {
                    val key = SendFlowerConditionDialog.KEY_SEND_FLOWER_CONDITIONS_INFO
                    resultCallback?.invoke(
                        if (containsKey(key)) {
                            getParcelable(SendFlowerConditionDialog.KEY_SEND_FLOWER_CONDITIONS_INFO)
                        } else {
                            null
                        }
                    )
                }
            }
            SendFlowerConditionDialog.show(
                fragment, gameId, gameType, conditionsInfo
            )
        }

        /**
         * source:
         * rank：排行榜
         * gamedetail：游戏详情页
         */
        fun showSendFlowerDialog(
            fragment: Fragment, gameId: String, gameType: Int, source: String, hasReceivedGift: Boolean, resultCallback: ((Int) -> Unit)? = null
        ) {
            // 由于用的fragment.childFragmentManager显示的Fragment, 所以也需要 fragment.childFragmentManager 监听结果
            fragment.childFragmentManager.setFragmentResultListener(SendFlowerDialog.REQUEST_KEY_SEND_FLOWER_COUNT, fragment) { _, bundle ->
                bundle.apply {
                    resultCallback?.invoke(
                        getInt(SendFlowerDialog.KEY_SEND_FLOWER_COUNT)
                    )
                }
            }
            // 使用 Control.navigate 方式跳转时, 从 SendFlowerDialog 跳转到其它页面, 会自动隐藏 SendFlowerDialog
            // 所以这里直接用fragment.childFragmentManager去show
            SendFlowerDialog.showDialog(fragment, gameId, gameType, source, hasReceivedGift)
        }

        fun showCustomizeFlowerCountDialog(
            fragment: Fragment, resultCallback: ((Int) -> Unit)? = null
        ) {
            CustomizeFlowerCountDialog.show(
                fragment, resultCallback
            )
        }
    }

    object Search {

        fun navigate(fragment: Fragment) {
            Control.navigate(fragment, R.id.search)
        }
    }

    object Dialog {

        fun alert(fragment: Fragment): ConfirmDialog.Builder {
            return ConfirmDialog.Builder(fragment)
        }

        fun alert(): ConfirmDialog.Builder {
            return ConfirmDialog.Builder()
        }

        fun loading(
            fm: FragmentManager, tag: String = "LoadingDialogFragment", msg: String = ""
        ): LoadingDialogFragment {
            val fragment = LoadingDialogFragment()
            fragment.arguments = bundleOf("loadingMsg" to msg)
            fragment.show(fm, tag)
            return fragment
        }

        fun selectTxt(
            fragment: Fragment, selectItems: List<String>
        ): SimpleSelectTxtDialogFragment.Builder {
            return SimpleSelectTxtDialogFragment.Builder(fragment).selectItems(selectItems)
        }
    }

    object Developer {

        fun buildConfig(fragment: Fragment) {
            Control.navigate(fragment, R.id.devBuildConfigFragment)
        }

        fun appParams(fragment: Fragment) {
            Control.navigate(fragment, R.id.devAppParamsFragment)
        }

        fun developer(fragment: Fragment) {
            Control.navigate(fragment, R.id.devEnvFragment)
        }

        fun reviewGame(fragment: Fragment, token: String? = null, env: String? = null) {
            MWDevelopRouter.Review.main(fragment, token ?: "", env ?: "")
//            Control.navigate(fragment, R.id.devReviewGame, DeveloperReviewGameFragmentArgs(token).toBundle())
        }
    }

    object Login {

        /**
         * @param loginSource 来源，埋点用
         * @param gid 来源游戏id，用于登录页跳回游戏
         * @param onlyLogin 是否只登录，不注册
         * @param continueAccountInfo 上次账号信息
         * @param successToMain 成功后跳到app主页
         */
        fun login(
            fragment: Fragment, loginSource: LoginSource, gid: String? = null, onlyLogin: Boolean = false, continueAccountInfo: ContinueAccountInfo? = null, successToMain: Boolean = false
        ) {
            login(
                fragment, loginSource.source, gid, onlyLogin, continueAccountInfo, successToMain
            )
        }

        fun login(
            context: Context, loginSource: String, gid: String? = null, onlyLogin: Boolean = false, continueAccountInfo: ContinueAccountInfo? = null, successToMain: Boolean = false
        ) {
            val intent = Main.mainActivityIntent(context, loginSource)
            val uri = Main.buildLocalJumpUri(
                mapOf(
                    MetaDeepLink.PARAM_SOURCE_FROM to loginSource,
                    MetaDeepLink.PARAM_FROM_GAME_ID to gid,
                    LoginHandler.PARAM_ONLY_LOGIN to onlyLogin,
                    LoginHandler.PARAM_CONTINUE_ACCOUNT_INFO to continueAccountInfo?.toJSON(),
                    LoginHandler.PARAM_SUCCESS_TO_MAIN to successToMain,
                ), MetaDeepLink.ACTION_LOGIN, MetaDeepLink.ACTION_LOGIN
            )

            intent.data = uri
            context.startActivity(intent)
        }

        /**
         * @param loginSource 来源，埋点用
         * @param gid 来源游戏id，用于登录页跳回游戏
         * @param onlyLogin 是否只登录，不注册
         * @param continueAccountInfo 上次账号信息
         * @param successToMain 成功后跳到app主页
         */
        fun login(
            fragment: Fragment, loginSource: String, gid: String? = null, onlyLogin: Boolean = false, continueAccountInfo: ContinueAccountInfo? = null, successToMain: Boolean = false, lastLoginType: String? = null
        ) {

            val activity = fragment.activity ?: return
            if (activity.isFinishing) return
            if (fragment.isDetached) return


            try {
                Control.navigate(
                    fragment, R.id.login, LoginFragmentArgs(
                        source = loginSource, gid = gid, onlyLogin = onlyLogin, continueAccountInfo = continueAccountInfo, successToMain = successToMain, lastLoginType = lastLoginType
                    ).toBundle()
                )
            } catch (e: Exception) {
                fragment.context?.let {
                    val intent = Intent(it, LoginActivity::class.java)
                    intent.putExtras(
                        LoginFragmentArgs(
                            source = loginSource, gid = gid, onlyLogin = onlyLogin, continueAccountInfo = continueAccountInfo, successToMain = successToMain, lastLoginType = lastLoginType
                        ).toBundle()
                    )
                    it.startActivity(intent)
                }
            }
        }

        fun loginByPhone(
            fragment: Fragment, loginSource: String, phoneNum: String? = "", onlyLogin: Boolean = false, successToMain: Boolean = true
        ) {
            MetaRouterWrapper.Account.loginByPhone(
                fragment, loginSource, phoneNum, onlyLogin, successToMain
            )
        }
    }

    object Account {
        fun signUp(fragment: Fragment, loginSource: String) {
            MetaRouterWrapper.Account.signUp(fragment, loginSource)
        }

        /**
         * @param scene 邮箱验证类型
         * @param oldEmailCode 绑定新邮箱时传，旧邮箱验证码 [com.socialplay.gpark.data.model.account.EmailScene.ChangeEmailNew] [com.socialplay.gpark.data.model.account.EmailScene.ChangeParentEmailNew]
         * @param requestKey 结果回调key
         * @param source 来源，埋点用
         * @param gameId 如果从游戏来的，携带的gameId
         */
        private fun emailVerify(
            fragment: Fragment, scene: EmailScene, oldEmailCode: String?, requestKey: String?, source: LoginPageSource, gameId: String?
        ) {
            Control.navigate(
                fragment, R.id.account_email_verify, EmailVerifyFragmentArgs(
                    verifyScene = scene, oldEmailCode = oldEmailCode, requestResultKey = requestKey, source = source.source, gameId = gameId
                ).toBundle()
            )
        }

        fun passwordForget(fragment: Fragment, source: LoginPageSource, gameId: String?) {
            emailVerify(fragment, EmailScene.RetrievePassword, null, null, source, gameId)
        }

        fun bindEmail(
            fragment: Fragment,
            requestKey: String,
            source: LoginPageSource,
            gameId: String?,
            bindSuccessCallback: () -> Unit
        ) {
            fragment.setFragmentResultListener(requestKey) { _, _ ->
                bindSuccessCallback.invoke()
            }
            emailVerify(fragment, EmailScene.BindEmail, null, requestKey, source, gameId)
        }

        fun accountCancellationVerify(
            fragment: Fragment, requestKey: String, source: LoginPageSource, gameId: String?, callback: (String?) -> Unit
        ) {
            fragment.setFragmentResultListener(requestKey) { _, bundle ->
                val code = bundle.getString(KEY_DITOUT_VERIFY_CODE)
                callback.invoke(code)
            }
            emailVerify(fragment, EmailScene.DieOut, null, requestKey, source, gameId)
        }

        fun emailBind(fragment: Fragment, source: LoginPageSource, gameId: String?) {
            emailVerify(fragment, EmailScene.BindEmail, null, null, source, gameId)
        }

        fun parentEmailBind(fragment: Fragment, source: LoginPageSource, gameId: String?) {
            emailVerify(fragment, EmailScene.BindParentEmail, null, null, source, gameId)
        }

        fun emailBindChangeOld(fragment: Fragment, source: LoginPageSource, gameId: String?) {
            emailVerify(fragment, EmailScene.ChangeEmailOld, null, null, source, gameId)
        }

        fun emailBindChangeNew(
            fragment: Fragment, oldEmailCode: String, isParentEmail: Boolean, source: LoginPageSource, gameId: String?
        ) {
            emailVerify(
                fragment, if (isParentEmail) EmailScene.ChangeParentEmailNew else EmailScene.ChangeEmailNew, oldEmailCode, null, source, gameId
            )
        }

        fun passwordSet(
            fragment: Fragment, email: String, code: String, source: LoginPageSource, gameId: String?
        ) {
            Control.navigate(
                fragment, R.id.password_set, PasswordSetFragmentArgs(email, code, source.source, gameId).toBundle()
            )
        }

        fun passwordChange(fragment: Fragment, source: LoginPageSource, gameId: String?) {
            Control.navigate(
                fragment, R.id.password_change, PasswordChangeFragmentArgs(source.source, gameId).toBundle()
            )
        }

        fun nickNameReset(fragment: Fragment, oldNickName: String? = "", onUpdated: () -> Unit = {}) {
            fragment.setFragmentResultListener(KEY_EDIT_PROFILE_RESULT) { _, bundle ->
                val result = bundle.getBoolean(PARAM_EDIT_PROFILE_RESULT, false)
                if (result) {
                    onUpdated.invoke()
                }
            }
            Control.navigate(
                fragment, R.id.nickname_reset, NickNameResetFragmentArgs(oldNickName).toBundle()
            )
        }

        fun cityReset(fragment: Fragment, oldCityName: String? = "", onUpdated: () -> Unit = {}) {
            fragment.setFragmentResultListener(KEY_EDIT_PROFILE_RESULT) { _, bundle ->
                val result = bundle.getBoolean(PARAM_EDIT_PROFILE_RESULT, false)
                if (result) {
                    onUpdated.invoke()
                }
            }
            Control.navigate(
                fragment, R.id.city_reset, CityResetFragmentArgs(oldCityName).toBundle()
            )
        }

        fun informationReset(fragment: Fragment, oldInformation: String?, onUpdated: () -> Unit = {}) {
            fragment.setFragmentResultListener(KEY_EDIT_PROFILE_RESULT) { _, bundle ->
                val result = bundle.getBoolean(PARAM_EDIT_PROFILE_RESULT, false)
                if (result) {
                    onUpdated.invoke()
                }
            }
            Control.navigate(
                fragment, R.id.information_reset, InformationResetFragmentArgs(oldInformation).toBundle()
            )
        }

        fun accountCancellation(
            fragment: Fragment, code: String, source: LoginPageSource, gameId: String?
        ) {
            Control.navigate(
                fragment, R.id.account_cancellation, AccountCancellationFragmentArgs(code, source.source, gameId).toBundle()
            )
        }

        fun editProfile(
            fragment: Fragment, gameId: String? = null, gamePkgName: String? = null
        ) {
            Control.navigate(
                fragment, R.id.editProfile, EditProfileFragmentArgs(gameId, gamePkgName).toBundle()
            )
        }

        fun editLink(fragment: Fragment, data: String?) {
            Control.navigate(
                fragment, R.id.edit_link, navData = EditLinkFragmentArgs(data ?: "").asMavericksArgs()
            )

        }

        fun addLink(fragment: Fragment, requestKey: String, callBack: (ProfileLinkInfo?) -> Unit) {
            fragment.childFragmentManager.setFragmentResultListener(
                requestKey, fragment.viewLifecycleOwner
            ) { _, bundle ->
                val result = bundle.getString(requestKey, null)
                if (result != null) {
                    val info = GsonUtil.gsonSafeParse<ProfileLinkInfo>(result)
                    callBack.invoke(info)
                }
                fragment.childFragmentManager.clearFragmentResultListener(requestKey)
            }

            AddLinkDialog().show(fragment.childFragmentManager, "AddLinkDialog")
        }

        fun connectedAccounts(fragment: Fragment, source: LoginPageSource, gameId: String?) {
            MetaRouterWrapper.Account.connectedAccounts(fragment, source, gameId)
        }

        /**
         * 绑定账密/账密注册
         * @param source 页面来源
         * @param gameId 如果从游戏来的，携带的gameId
         */
        fun bindAccountAndPassword(
            fragment: Fragment,
            source: LoginPageSource,
            gameId: String?,
            requestKey: String? = null,
            bindSuccessCallback: () -> Unit = {}
        ) {
            requestKey?.let {
                fragment.setFragmentResultListener(it) { _, _ ->
                    bindSuccessCallback.invoke()
                }
            }

            Control.navigate(
                fragment, R.id.bind_account_and_password, AccountAndPasswordBindFragmentArgs(
                    source = source.source, gameId = gameId, requestKey
                ).toBundle()
            )
        }

        /**
         * 绑定账密/账密注册
         * @param source 页面来源 com.socialplay.gpark.data.model.LoginPageSource
         * @param gameId 如果从游戏来的，携带的gameId
         */
        fun bindAccountAndPassword(
            fragment: Fragment,
            source: String,
            gameId: String?,
            requestKey: String? = null,
            bindSuccessCallback: () -> Unit = {}
        ) {
            if(requestKey != null) {
                fragment.setFragmentResultListener(requestKey) { _, _ ->
                    bindSuccessCallback.invoke()
                }
            }

            Control.navigate(
                fragment, R.id.bind_account_and_password, AccountAndPasswordBindFragmentArgs(
                    source = source, gameId = gameId, requestKey = requestKey
                ).toBundle()
            )
        }

        fun findId(fragment: Fragment) {
            Control.navigate(
                fragment, R.id.find_id
            )
        }

        /**
         * 账号完善
         */
        fun accountComplete(fragment: Fragment) {
            Control.navigate(
                fragment, R.id.account_complete
            )
        }
    }

    object Web {
        fun navigate(
            fragment: Fragment,
            title: String? = null,
            url: String,
            showTitle: Boolean = true,
            statusBarColor: String? = null,
            isWebOutside: Boolean = false,
            openInsideWhenOutsideFailed: Boolean = true,
            showStatusBar: Boolean = true,
            gameId: String? = null,
            useBabel: Boolean = true,
            useCompatParams: Boolean = true,
        ) {
            // 浏览器打开
            if (isWebOutside) {
                kotlin.runCatching {
                    fragment.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
                }.getOrElse {
                    if (openInsideWhenOutsideFailed) navigate(
                        null, fragment, title, url, showTitle, statusBarColor, showStatusBar, gameId, useBabel = useBabel, useCompatParams = useCompatParams
                    )
                }
            } else {
                navigate(
                    null,
                    fragment,
                    title,
                    url,
                    showTitle,
                    statusBarColor,
                    showStatusBar,
                    gameId,
                    useBabel = useBabel,
                    useCompatParams = useCompatParams
                )
            }
        }

        fun navigate(
            context: Context?,
            fragment: Fragment?,
            title: String? = null,
            url: String,
            showTitle: Boolean = true,
            statusBarColor: String? = null,
            showStatusBar: Boolean = true,
            extra: String? = null,
            gameId: String? = null,
            useBabel: Boolean = true,
            useCompatParams: Boolean = true
        ) {
            if (context == null && fragment == null) {
                Timber.e("context/fragment all null!")
                return
            }
            if (PandoraToggle.isOpenBabelWeb && useBabel) {
                if (fragment != null && fragment.findNavControllerOrNull() != null) {
                    WebRouter.showStandaloneFragment(
                        fragment = fragment,
                        url = url,
                        resIdBean = ResIdBean(),
                    ) {
                        this.title = title
                        this.gameId = gameId
                        this.extra = extra
                        this.showTitle = showTitle
                        this.showStatusBar = showStatusBar
                        this.statusBarColor = statusBarColor
                    }
                } else if (context != null) {
                    context.startActivity(Intent(context, WebActivity::class.java).also {
                        it.putExtras(
                            WebActivityArgs(
                                url = url, title = title, showTitle = showTitle, statusBarColor = statusBarColor, gameId = gameId, showStatusBar = showStatusBar, needWebLifecycle = true, extra = extra
                            ).toBundle()
                        )
                        if (context !is Activity) {
                            it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        }
                    })
                } else {
                    Timber.e("context is null, fragment is null, or fragment cannot find navController")
                }
            } else {
                val uri = MetaRouterWrapper.Web.convertWebUri(url)
                val isTranslucentTop = uri?.getQueryParameter("____isTranslucentTop") == "true"
                val isNativeTitleShow = uri?.getQueryParameter("____isNativeTitleShow") != "false"
                //参数转换
                val args = WebFragmentArgs(
                    title = title, url = url, showTitle = showTitle && (!useCompatParams || isNativeTitleShow), statusBarColor = statusBarColor, showStatusBar = showStatusBar && (!useCompatParams || !isTranslucentTop), extra = extra
                ).toBundle()
                if (fragment != null) {
                    Control.navigate(fragment, R.id.web, args)
                } else if (context != null) {
                    RootNavHostFragmentActivity.start(
                        context, R.id.web, args
                    )
                } else {
                    Timber.e("context or fragment all null!")
                }
            }
        }


        fun navigate(fragment: Fragment, item: H5PageConfigItem) {
            navigate(fragment, item.title, item.url, true)
        }

        fun goRecharge(fragment: Fragment) {
            val url = GlobalContext.get().get<H5PageConfigInteractor>().getH5PageUrl(H5PageConfigInteractor.RECHARGE)
            navigate(fragment, url = url, showTitle = false)
        }

        /**
         * dialog web view
         */
        fun showDialog(
            activity: FragmentActivity, url: String, extra: String? = null
        ): WebViewDialog? {
            val fragmentManager = if (activity is MainActivity) {
                val navHostFragment = activity.findNavHostFragment() ?: return null
                if (!navHostFragment.isAdded) return null
                val f = navHostFragment.childFragmentManager.fragments.firstOrNull { it.isResumed && it.isVisible } ?: navHostFragment.childFragmentManager.fragments.firstOrNull { it.isVisible } ?: navHostFragment
                f.childFragmentManager
            } else {
                activity.supportFragmentManager
            }
            fragmentManager ?: return null
            val web = WebViewDialog()
            web.arguments = WebViewDialogArgs(url, extra).toBundle()
            web.show(fragmentManager, "showDialog")
            return web
        }
    }

    /**
     * 会员相关
     */
    object VipPlus {
        fun navigate(
            fragment: Fragment? = null, context: Context? = null, source: String, gameId: String? = null
        ) {
            // TODO 新客户端会员
//            val url = GlobalContext.get().get<H5PageConfigInteractor>().getH5PageUrl(H5PageConfigInteractor.VIP_PLUS)
//            if (!WebUtil.isHttpOrHttpsScheme(url)) {
//                return
//            }
//
//            val newUrlBuilder = url.toHttpUrl().newBuilder()
//            if (source.isNotEmpty()) {
//                newUrlBuilder.addQueryParameter("source", source)
//            }
//
//            if (!gameId.isNullOrEmpty()) {
//                newUrlBuilder.addQueryParameter("gameid", gameId)
//            }
//
//            Web.navigate(context = context, fragment = fragment, url = newUrlBuilder.build().toString(), showTitle = false)
        }
    }

    object AccountSetting {
        /**
         * 设置页
         * @param source 页面来源
         * @param gameId 如果从游戏来的，携带的gameId
         */
        fun setting(fragment: Fragment, source: LoginPageSource, gameId: String?) {
            Control.navigate(
                fragment, R.id.setting, SettingFragmentArgs(source.source, gameId).toBundle()
            )
        }

        /**
         * 账号设置/安全
         * @param source 页面来源
         * @param gameId 如果从游戏来的，携带的gameId
         */
        fun navigate(fragment: Fragment, source: LoginPageSource, gameId: String?) {
            Control.navigate(
                fragment, R.id.account_setting, AccountSettingFragmentArgs(source.source, gameId).toBundle()
            )
        }
    }

    object IM {
        fun goSys(
            fragment: Fragment, groupId: Long, groupContentType: Int, title: String, unReadCount: Long? = 0, type: Long? = null, gameId: String? = null, tabList: String? = null, source: Long = -1L, avatar: String? = null
        ) {
            Control.navigate(
                fragment, R.id.sys_activities_fragment, SysDetailListFragmentArgs(
                    groupId, groupContentType, title, unReadCount ?: 0, type ?: -1, gameId, tabList, source, avatar
                ).toBundle()
            )
        }

        fun goRemarkAlert(
            fragment: Fragment, userName: String, remark: String, uuid: String, requestKey: String = "default_remark_request", remarkResultCallback: ((result: String) -> Unit)? = null
        ) {
            fragment.setFragmentResultListener(requestKey) { _, bundle ->
                val result = bundle.getString(KEY_REMARK_RESULT, null)
                if (result != null) {
                    remarkResultCallback?.invoke(result)
                }
            }
            Control.navigate(
                fragment, R.id.remarkAlert, RemarkAlertFragmentArgs(userName, remark, uuid, requestKey).toBundle()
            )
        }

        fun goFriendRequestList(fragment: Fragment, source: String? = null) {
            Analytics.track(EventConstants.EVENT_SHOW_FRIEND_REQUEST, "source" to source.toString())
            Control.navigate(fragment, R.id.friend_request_list)
        }

        fun goFriendGroupsRequestList(fragment: Fragment, source: String? = null) {
            Analytics.track(EventConstants.EVENT_SHOW_FRIEND_REQUEST, "source" to source.toString())
            Control.navigate(fragment, R.id.friendsGroupsRequestPage)
        }

        fun goAddFriend(fragment: Fragment) {
            Control.navigate(fragment, R.id.addFriend)
        }

        fun goContacts(
            fragment: Fragment,
            shareRequestKey: String? = null,
            shareContent: String? = null,
            shareCallbackByEvent: Boolean = false,
            needBackRole: Boolean = false,
            needBackGame: Boolean = false,
            fromGameId: String? = null,
            isCreateGroup: Boolean = false,
            isInviteGroupMembers: Boolean = false,
            groupId: Long = -1,
            maxSelectCount: Int = GroupChatConfig.MAX_GROUP_MEMBERS_INVITE_COUNT,
            inviteGroupMembersResult: ((inviteSuccess: Boolean) -> Unit)? = null
        ) {
            if (isInviteGroupMembers && inviteGroupMembersResult != null) {
                fragment.setFragmentResultListener(ContactListFragment.KEY_REQUEST_INVITE_MEMBERS) { _, bundle ->
                    val result = bundle.getBoolean(ContactListFragment.KEY_RESULT_INVITE_MEMBERS_SUCCESS)
                    inviteGroupMembersResult.invoke(result)
                }
            }
            Control.navigate(
                fragment, R.id.contact_list_fragment, ContactListFragmentArgs(
                    shareContent, shareRequestKey, title = if (isCreateGroup) {
                        getStringByGlobal(R.string.new_group_page_title)
                    } else if (isInviteGroupMembers) {
                        getStringByGlobal(R.string.invite_group_members_page_title)
                    } else {
                        getStringByGlobal(R.string.friends_title)
                    }, needBackRole = needBackRole, needBackGame = needBackGame, shareCallbackByEvent = shareCallbackByEvent, fromGameId = fromGameId, isCreateGroup = isCreateGroup, isInviteGroupMembers = isInviteGroupMembers, groupId = groupId, maxSelectCount = maxSelectCount
                ).toBundle()
            )
        }

        fun goSearchFriend(fragment: Fragment) {
            Control.navigate(fragment, R.id.searchFriend)
        }

        fun goMyQRCode(fragment: Fragment) {
            Control.navigate(fragment, R.id.myQRCode)
        }

        fun goAddFriendDialog(
            fragment: Fragment, uuid: String, showChatting: Boolean = true, from: String = "2"
        ) {
            //fix java.lang.IllegalStateException: FragmentManager is already executing transactions
            //            val addFriendDialog = AddFriendDialog()
            //            addFriendDialog.arguments = AddFriendDialogArgs(uuid, showChatting).toBundle()
            //            addFriendDialog.showNow(fragment.childFragmentManager, "AddFriendDialog")

            Profile.other(fragment, uuid, from)
        }

        fun goFriendApply(
            fragment: Fragment, uuid: String, userNumber: String, gamePackageName: String, tagIds: List<Int>? = null, labelInfo: LabelInfo? = null, userName: String? = null, avatar: String? = null
        ) {
            Control.navigate(
                fragment, R.id.friend_apply, FriendApplyFragmentArgs(
                    uuid, userNumber, userName, gamePackageName, avatar, SimpleUserLabelInfo(tagIds, labelInfo)
                ).toBundle()
            )
        }

        fun gotoConversation(
            fragment: Fragment, otherUid: String, title: String? = null, tagIds: List<Int>? = null, labelInfo: LabelInfo? = null, navigatorExtras: Navigator.Extras? = null, source: String? = null, autoConnect: Boolean = false, shareContent: String? = null, shareCallBack: ((Boolean) -> Unit)? = null
        ) {
            goPrivateChat(
                fragment, otherUid, title, tagIds, labelInfo, navigatorExtras, source, autoConnect, shareContent, shareCallBack
            )
//            if (autoConnect) {
//                RongImHelper.needConnect()
//            }
//            Analytics.track(EventConstants.EVENT_SHOW_CONVERSATION, "source" to source.toString())
//            fragment.setFragmentResultListener(ConversationFragment.SHARE_CALLBACK) { _, bundle ->
//                shareCallBack?.invoke(true)
//            }
//            Control.navigate(
//                fragment,
//                R.id.conversation_fragment,
//                ConversationFragmentArgs(
//                    otherUid = otherUid,
//                    title = title,
//                    shareContent = shareContent,
//                    userLabelInfo = SimpleUserLabelInfo(tagIds, labelInfo)
//                ).toBundle(),
//                navExtras = navigatorExtras
//            )
        }

        fun goPrivateChat(
            fragment: Fragment, otherUid: String, title: String? = null, tagIds: List<Int>? = null, labelInfo: LabelInfo? = null, navigatorExtras: Navigator.Extras? = null, source: String? = null, autoConnect: Boolean = false, shareContent: String? = null, shareCallBack: ((Boolean) -> Unit)? = null
        ) {
            if (autoConnect) {
                RongImHelper.needConnect()
            }
            Analytics.track(EventConstants.EVENT_SHOW_CONVERSATION, "source" to source.toString())
            fragment.setFragmentResultListener(BaseChatFragment.SHARE_CALLBACK) { _, bundle ->
                shareCallBack?.invoke(true)
            }
            Control.navigate(
                fragment, R.id.private_chat_fragment, PrivateChatFragmentArgs(
                    otherUid = otherUid, title = title, shareContent = shareContent, userLabelInfo = SimpleUserLabelInfo(tagIds, labelInfo)
                ).toBundle(), navExtras = navigatorExtras
            )
        }

        fun goGroupChat(
            fragment: Fragment, groupChatId: String, imId: String? = null, groupName: String? = null, navigatorExtras: Navigator.Extras? = null, navOptions: NavOptions? = null, source: String? = null, autoConnect: Boolean = false, shareContent: String? = null, shareCallBack: ((Boolean) -> Unit)? = null
        ) {
            if (autoConnect) {
                RongImHelper.needConnect()
            }
            fragment.setFragmentResultListener(BaseChatFragment.SHARE_CALLBACK) { _, bundle ->
                shareCallBack?.invoke(true)
            }
            Control.navigate(
                fragment, R.id.group_chat_fragment, GroupChatFragmentArgs(
                    groupChatId = groupChatId,
                    imId = imId,
                    groupName = groupName,
                    shareContent = shareContent,
                ).toBundle(), navOptions = navOptions, navExtras = navigatorExtras
            )
        }

        fun goChatSetting(
            fragment: Fragment, uuid: String, requestKey: String = "default_chatsetting_request", chatSettingResultCallback: ((msgToTop: Boolean, deleteFriend: Boolean, remark: String) -> Unit)? = null
        ) {
            fragment.setFragmentResultListener(requestKey) { _, bundle ->
                bundle.apply {
                    chatSettingResultCallback?.invoke(
                        getBoolean(KEY_CHATSETTING_RESULT_CLEAR_MSG), getBoolean(KEY_CHATSETTING_RESULT_DELETE_FRIEND), getString(KEY_CHATSETTING_RESULT_REMARK, "")
                    )
                }
            }
            Control.navigate(
                fragment, R.id.chatSetting, ChatSettingFragmentArgs(uuid, requestKey).toBundle()
            )
        }

        fun goQRCodeScan(
            activity: FragmentActivity, fragment: Fragment, requestKey: String, entry: ScanEntry = ScanEntry.Unknown, customData: Bundle = Bundle.EMPTY, packageName: String? = null, gameId: String? = null, gameType: String? = null
        ) {
            PermissionRequest.with(activity).permissions(Permission.CAMERA).denied {
                if (!fragment.isDetached && fragment.activity != null && fragment.activity?.isFinishing != true) {
                    goCameraPermission(
                        fragment, CameraPermissionDialog.REQUEST_PERMISSION_CAMERA, true, packageName, gameId
                    )
                }
            }.granted {
                if (!fragment.isDetached && fragment.activity != null && fragment.activity?.isFinishing != true) {
                    Control.navigate(
                        fragment, R.id.qr_code_scan, QRCodeScanFragmentArgs(
                            requestKey, entry, customData, packageName, gameId, gameType
                        ).toBundle()
                    )
                }
            }.branch(PermissionRequest.SCENE_QR_CODE).request()
        }

        fun goCameraPermission(
            fragment: Fragment, requestKey: String, goSettings: Boolean, packageName: String? = null, gameId: String? = null
        ) {
            Control.navigate(
                fragment, R.id.camera_permission_dialog, CameraPermissionDialogArgs(goSettings, requestKey, packageName, gameId).toBundle()
            )
        }

        fun goChatTabFragment(
            fragment: Fragment, isFromBottom: Boolean = false, source: String = EventParamConstants.SRC_MESSAGE_LIST_ENTRANCE_UNKNOWN
        ) {
            Analytics.track(
                EventConstants.CHAT_ENTANCE_CLICK, "source" to source
            )
            RongImHelper.needConnect()
            Control.navigate(
                fragment, R.id.chat_tab, ConversationListFragmentArgs(isFromBottom = isFromBottom).toBundle()
            )
        }

        fun goHeGroupsListFragment(
            fragment: Fragment,
            targetUid: String,
        ) {
            Control.navigate(
                fragment, R.id.heGroupsPage, HeGroupsListFragmentArgs(
                    targetUid = targetUid
                ).toBundle()
            )
        }

        fun goMyGroupsListFragment(
            fragment: Fragment,
            targetUid: String,
        ) {
            Control.navigate(
                fragment, R.id.myGroupsPage, MyGroupsParentFragmentArgs(
                    targetUid = targetUid
                ).toBundle()
            )
        }

        fun goGroupProfileFragment(
            fragment: Fragment, groupId: String, resultCallback: ((groupDetail: GroupChatDetailInfo?) -> Unit)? = null
        ) {
            fragment.setFragmentResultListener(GroupChatProfileFragment.REQUEST_KEY_GROUP_EDIT) { _, bundle ->
                bundle.apply {
                    resultCallback?.invoke(
                        getParcelable(GroupChatProfileFragment.KEY_GROUP_EDIT_RESULT)
                    )
                }
            }
            Control.navigate(
                fragment, R.id.groupProfilePage, GroupChatProfileFragmentArgs(
                    groupId = groupId
                ).toBundle()
            )
        }

        fun goGroupProfileGuestFragment(
            fragment: Fragment, groupInfo: GroupChatInfo, resultCallback: ((groupInfo: GroupChatInfo?, result: Boolean) -> Unit)? = null
        ) {
            fragment.setFragmentResultListener(GroupChatProfileGuestFragment.REQUEST_KEY_JOIN_GROUP) { _, bundle ->
                bundle.apply {
                    resultCallback?.invoke(
                        getParcelable(GroupChatProfileGuestFragment.KEY_JOIN_GROUP_INFO_RESULT), getBoolean(GroupChatProfileGuestFragment.KEY_JOIN_GROUP_RESULT)
                    )
                }
            }
            Control.navigate(
                fragment, R.id.groupProfileGuestPage, GroupChatProfileGuestFragmentArgs(
                    groupInfo = groupInfo
                ).toBundle()
            )
        }

        fun goGroupNameEditDialog(
            fragment: Fragment, groupId: Long, currentGroupName: String, resultCallback: ((isAuditing: Boolean) -> Unit)? = null
        ) {
            fragment.setFragmentResultListener(EditGroupChatNameDialog.REQUEST_KEY_GROUP_EDIT_NAME) { _, bundle ->
                bundle.apply {
                    resultCallback?.invoke(
                        getBoolean(EditGroupChatNameDialog.KEY_GROUP_EDIT_NAME_RESULT)
                    )
                }
            }
            Control.navigate(
                fragment, R.id.dialogEditGroupName, EditGroupChatNameDialogArgs(
                    groupId = groupId,
                    currentGroupName = currentGroupName,
                ).toBundle()
            )
        }

        fun goGroupDescEditFragment(
            fragment: Fragment, groupId: Long, currentGroupDesc: String, enableEdit: Boolean, resultCallback: ((isAuditing: Boolean) -> Unit)? = null
        ) {
            fragment.setFragmentResultListener(EditGroupDescFragment.REQUEST_KEY_GROUP_EDIT_DESC) { _, bundle ->
                bundle.apply {
                    resultCallback?.invoke(
                        getBoolean(EditGroupDescFragment.KEY_GROUP_EDIT_DESC_RESULT)
                    )
                }
            }
            Control.navigate(
                fragment, R.id.editGroupDescPage, EditGroupDescFragmentArgs(
                    groupId = groupId,
                    currentGroupDesc = currentGroupDesc,
                    enableEdit = enableEdit,
                ).toBundle()
            )
        }

        fun goGroupJoinInviteRulesEditDialog(
            fragment: Fragment, groupId: Long, currentJoinType: Int, currentInviteType: Int, resultCallback: ((groupDetail: GroupChatDetailInfo?) -> Unit)? = null
        ) {
            fragment.setFragmentResultListener(EditGroupChatJoinInviteRuleDialog.REQUEST_KEY_GROUP_EDIT_JOIN_INVITE_RULE) { _, bundle ->
                bundle.apply {
                    resultCallback?.invoke(
                        getParcelable(EditGroupChatJoinInviteRuleDialog.KEY_GROUP_EDIT_JOIN_INVITE_RULE)
                    )
                }
            }
            Control.navigate(
                fragment, R.id.dialogEditGroupJoinInviteRule, EditGroupChatJoinInviteRuleDialogArgs(
                    groupId = groupId,
                    currentJoinType = currentJoinType,
                    currentInviteType = currentInviteType,
                ).toBundle()
            )
        }

        fun goGroupJoinRequestFragment(
            fragment: Fragment, groupInfo: GroupChatInfo, resultCallback: ((groupInfo: GroupChatInfo?, result: Boolean) -> Unit)? = null
        ) {
            fragment.setFragmentResultListener(RequestJoinGroupFragment.REQUEST_KEY_JOIN_GROUP) { _, bundle ->
                bundle.apply {
                    resultCallback?.invoke(
                        getParcelable(RequestJoinGroupFragment.KEY_JOIN_GROUP_INFO_RESULT), getBoolean(RequestJoinGroupFragment.KEY_JOIN_GROUP_RESULT)
                    )
                }
            }
            Control.navigate(
                fragment, R.id.fragmentRequestJoinGroup, RequestJoinGroupFragmentArgs(
                    groupInfo = groupInfo
                ).toBundle()
            )
        }

        fun goGroupJoinRequestApprovalDialog(
            fragment: Fragment, applyInfo: GroupChatApplyInfo, resultCallback: ((result: Int) -> Unit)? = null
        ) {
            fragment.setFragmentResultListener(GroupJoinRequestApprovalDialog.REQUEST_KEY_JOIN_REQUEST_APPROVAL) { _, bundle ->
                bundle.apply {
                    resultCallback?.invoke(
                        getInt(GroupJoinRequestApprovalDialog.KEY_JOIN_REQUEST_APPROVAL_RESULT, -1)
                    )
                }
            }
            Control.navigate(
                fragment, R.id.dialogRequestJoinGroupApproval, GroupJoinRequestApprovalDialogArgs(
                    requestInfo = applyInfo
                ).toBundle()
            )
        }

        fun goGroupMembersFragment(
            fragment: Fragment, groupDetail: GroupChatDetailInfo, resultCallback: ((groupDetail: GroupChatDetailInfo?) -> Unit)? = null
        ) {
            fragment.setFragmentResultListener(GroupChatMembersFragment.REQUEST_KEY_MEMBERS_EDIT) { _, bundle ->
                bundle.apply {
                    resultCallback?.invoke(
                        getParcelable(GroupChatMembersFragment.KEY_MEMBERS_EDIT_RESULT),
                    )
                }
            }
            Control.navigate(
                fragment, R.id.groupChatMembersPage, GroupChatMembersFragmentArgs(
                    groupDetail = groupDetail
                ).toBundle()
            )
        }
    }

    object Auth {
        fun toLoginConfirm(fragment: Fragment, ssoLoginRequest: SsoLoginRequest? = null) {
            Control.navigate(
                fragment, R.id.login_confirm, LoginConfirmFragmentArgs(ssoLoginRequest).toBundle()
            )
        }
    }

    object GameReview {

        fun goReviewDialog(
            fragment: Fragment, gameId: String, gameName: String?, gameIcon: String?
        ) {
            Control.navigate(
                fragment, R.id.gameReviewDialog, GameReviewDialogArgs(gameId, gameName, gameIcon).toBundle()
            )
        }

        fun goDeleteReviewDialog(fragment: Fragment) {
            Control.navigate(fragment, R.id.deleteReviewDialog)
        }

        fun goEditReview(
            fragment: Fragment, gameId: String? = null, needFetchReview: Boolean, attitude: Int?, lengthLimit: Long, errorMessage: String?, botId: String? = null
        ) {
            AccPwdV7Dialog.show(fragment, AccPwdV7DialogArgs.SOURCE_PUBLISH_PGC_COMMENT) {
                if (it) {
                    Control.navigate(
                        fragment, R.id.edit_game_review, EditGameReviewFragmentArgs(
                            gameId, needFetchReview, attitude = attitude ?: 0, lengthLimit = lengthLimit, errorMessage = errorMessage ?: "", botId = botId
                        ).toBundle()
                    )
                }
            }
        }

        fun goReviewList(
            fragment: Fragment, gameId: String, reviewCount: String, canWriteReview: Boolean
        ) {
            Control.navigate(
                fragment, R.id.reviewList, ReviewListFragmentArgs(gameId, reviewCount, canWriteReview).toBundle()
            )
        }
    }

    object Notice {
        fun operationNotice(fragment: Fragment) {
            Control.navigate(fragment, R.id.operationList)
        }

        fun editorNotice(fragment: Fragment) {
            Control.navigate(fragment, R.id.noticeList)
        }
    }

    object Startup {
        fun guideLogin(
            fragment: Fragment,
            loginSource: LoginSource,
            navOptions: NavOptions? = FadeNavOptions.getFadeNavOptions(),
            continueAccountInfo: ContinueAccountInfo? = null,
        ) {
            Control.navigate(
                fragment, R.id.guideLogin, navData = GuideLoginFragmentArgs(
                    loginSource.source, continueAccountInfo
                ).asMavericksArgs(), navOptions = navOptions
            )
        }

//        fun buildGuide(fragment: Fragment, showBack: Boolean, navOptions: NavOptions?) {
//            Control.navigate(
//                fragment,
//                R.id.build_guide,
//                navData = BuildGuideFragmentArgs(showBack).asMavericksArgs(),
//                navOptions = NavOptions.Builder()
//                    .setPopUpTo(-1, false)
////                    .setEnterAnim(R.anim.slide_in_right)
////                    .setExitAnim(R.anim.slide_out_left)
//                    .setPopEnterAnim(R.anim.slide_in_left)
//                    .setPopExitAnim(R.anim.slide_out_right)
//                    .build()
//            )
//        }

        fun guideNext(
            fragment: Fragment, currentScene: GuideScene, defaultRoleInfo: DefaultRoleInfo? = null, defaultRolePortraitRes: Int? = null, skipAvatar: Boolean = true, nickname: String? = null, showBack: Boolean? = true, showSkip: Boolean? = false, age: Int? = null, gender: Int? = null
        ) {
            var toWhere = 0
            when (currentScene) {
                GuideScene.NONE -> {
                    // 刚登录完，理论上是去引导用户创建角色，但是开关可能是关的，所以可能是后续的其他步骤
                    toWhere = if (PandoraToggle.enableNewbieGuideSex)
                    // 走新手引导，选择性别
                        1 else if (PandoraToggle.enableNewbieGuideBirthday)
                    // 走新手引导，不选择性别，需要选择年龄
                        2 else if (PandoraToggle.enableNewbieGuideName)
                    // 走新手引导，不选择性别，不选择年龄，需要输入昵称
                        3 else if (PandoraToggle.enableNewbieGuideCreateExperience)
                    // 走新手引导，不选择性别，不选择年龄，不输入昵称，需要调查创作经验
                        4 else if (PandoraToggle.enableNewbieGuideCreateTheme)
                    // 走新手引导，不选择性别，不选择年龄，不输入昵称，不调查创作经验，但是需要走兴趣选择
                        5 else
                    // 不走新手引导
                        6
                }
                // 用户创建角色完成，下一步看开关，可能是去选择年龄，去输入昵称或者调查创作经验，也可能去主页了
                GuideScene.CREATE_AVATAR -> toWhere = if (PandoraToggle.enableNewbieGuideBirthday) 2
                    else if (PandoraToggle.enableNewbieGuideName) 3
                    else if (PandoraToggle.enableNewbieGuideCreateExperience) 4
                    else if (PandoraToggle.enableNewbieGuideCreateTheme) 5
                    else 6
                // 用户选择年龄完成，下一步看开关，可能是去输入昵称或者调查创作经验，也可能是去主页了
                GuideScene.SELECT_BIRTHDAY -> toWhere = if (PandoraToggle.enableNewbieGuideName) 3
                    else if (PandoraToggle.enableNewbieGuideCreateExperience) 4
                    else if (PandoraToggle.enableNewbieGuideCreateTheme) 5
                    else 6
                // 用户输入昵称完成，下一步看开关，可能是去调查创作经验或者选择兴趣，也可能是去主页了
                GuideScene.ENTER_NAME -> toWhere = if (PandoraToggle.enableNewbieGuideCreateExperience) 4
                    else if (PandoraToggle.enableNewbieGuideCreateTheme) 5
                    else 6
                // 调查用户创作经验完成，下一步去主页要不要建造引导
                GuideScene.CREATE_EXPERIENCE -> toWhere = if (PandoraToggle.enableNewbieGuideCreateTheme) 5 else 6
            }

            when (toWhere) {
                // 选择性别Avatar
                1 -> Control.navigate(fragment, R.id.startupCreateAvatarV2, navData = CreateAvatarFragmentArgs(showBack ?: true, showSkip ?: false).asMavericksArgs(), isPrePop = true)
                // 选择年龄
                2 -> {
                    val defaultAvatarInfo = defaultRoleInfo ?: EditorRepository.DEFAULT_PLACEHOLDER_AVATAR_INFO_V2[1]
                    val portraitRes = defaultRolePortraitRes ?: R.drawable.ic_create_avatar_portrait_male
                    selectBirthday(fragment, defaultAvatarInfo, nickname ?: "", portraitRes = portraitRes, skipAvatar = skipAvatar, gender = gender)
                }
                // 输入昵称
                3 -> {
                    val defaultAvatarInfo = defaultRoleInfo ?: EditorRepository.DEFAULT_PLACEHOLDER_AVATAR_INFO_V2[1]
                    val portraitRes = defaultRolePortraitRes ?: R.drawable.ic_create_avatar_portrait_male
                    enterName(fragment, defaultAvatarInfo, portraitRes, skipAvatar = skipAvatar, age = age, gender = gender)
                }
                // 创作经验调研
                4 -> {
                    Control.navigate(fragment, R.id.startupCreateExperience, isPrePop = true)
                }
                // 有建造引导
                5 -> {
                    val mainViewModel = fragment.getSharedViewModel<MainViewModel>()
                    mainViewModel.buildMode = 1
                    Main.tabHome(fragment, fragment.requireContext())
                }
                // 无引导，进首页
                6 -> {
                    val mainViewModel = fragment.getSharedViewModel<MainViewModel>()
                    mainViewModel.buildMode = 0
//                    fragment.findNavController().popBackStack()
                    Main.tabHome(fragment, fragment.requireContext())
                }
                // 默认进首页
                else -> {
                    val mainViewModel = fragment.getSharedViewModel<MainViewModel>()
                    mainViewModel.buildMode = 0
//                    fragment.findNavController().popBackStack()
                    Main.tabHome(fragment, fragment.requireContext())
                }
            }
        }

        fun createAvatar(
            fragment: Fragment, showBack: Boolean, showSkip: Boolean = false, navOptions: NavOptions? = null, fromBuildGuide: Boolean = false
        ) {
            guideNext(fragment, GuideScene.NONE, showBack = showBack, showSkip = showSkip)
        }

        fun enterName(
            fragment: Fragment, defaultRoleInfo: DefaultRoleInfo, portraitRes: Int, skipAvatar: Boolean = true, age: Int? = null, gender: Int? = null
        ) {
            Control.navigate(
                fragment, R.id.startupEnterName, navData = EnterNameFragmentArgs(
                    avatar = defaultRoleInfo, portraitRes = portraitRes, skipAvatar = skipAvatar, age = age, gender = gender
                ).asMavericksArgs(), isPrePop = true
            )
        }

        fun selectBirthday(
            fragment: Fragment, avatar: DefaultRoleInfo, name: String, portraitRes: Int = 0, skipAvatar: Boolean = false, gender: Int? = null
        ) {
            Control.navigate(
                fragment, R.id.startupSelectBirthDay, SelectBirthdayFragmentArgs(avatar, name, portraitRes, skipAvatar, gender).asMavericksArgs(), isPrePop = true
            )
        }

        fun selectModeGame(fragment: Fragment) {
            Control.navigate(
                fragment, R.id.startupSelectModeGame, navOptions = navOptions { }, isPrePop = true)
        }
    }

    object MobileEditor {

        @Deprecated("Deprecated", replaceWith = ReplaceWith("avatarEditor"))
        fun fullScreenRole(context: Context, args: FullScreenEditorActivityArgs) {
            context.startActivity(Intent(context, FullScreenEditorActivity::class.java).apply {
                if (context !is Activity) addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                putExtras(args.toBundle())
            })
        }


        /**
         * 使用scheme跳转角色编辑器的编辑态，并可透传数据给游戏内部
         * @param data 需要透传给游戏的数据
         */
        fun avatarEditorByScheme(
            context: Context, categoryId: Int, source: String, data: String? = null
        ) {
            val uri = Main.buildLocalJumpUri(
                action = MetaDeepLink.ACTION_JUMP_AVATAR_EDITOR, data = mapOf("category_id" to categoryId, "data" to data)
            )
            Main.jumpScheme(context, uri.toString(), source)
        }

        /**
         * 跳转角色编辑器的编辑态，并可透传数据给游戏内部
         * @param data 需要透传给游戏的数据
         */
        fun avatarEditor(context: Context, categoryId: Int, data: String? = null) {
            // 个人主页tab改版之前, 下面的代码会触发 TabLinkHandler.handle() 方法, 进而触发 MainViewModel.setSelectedItem() 方法
            // 然后使用个人主页tab的全屏展示角编, 个人主页改版之后, 流程已经走不通了, 所以改成用 fullScreenRole 的方式显示角编
//            Main.toTab(
//                context, MainBottomNavigationItem.EDITOR_HOME, bundleOf("category_id" to categoryId, "data" to data)
//            )

            fullScreenRole(context,FullScreenEditorActivityArgs(
                categoryId = categoryId,
                opacityData = data ?: ""
            ))
        }

        fun renameLocalGame(
            fragment: Fragment, currentName: String, filePath: String, requestKey: String
        ) {
            Control.navigate(
                fragment, R.id.dialogRenameLocal, RenameLocalDialogArgs(
                    currentName = currentName, filePath = filePath, requestKey = requestKey
                ).toBundle()
            )
        }

        fun creation(
            fragment: Fragment, initTab: Int = -1, categoryId: Int = -1, source: String? = null
        ) {
            if (PandoraToggle.enableUgcBuildVersion == "2" || PandoraToggle.enableUgcBuildV2) {
                Control.navigate(
                    fragment, R.id.editor_create_v2, EditorCreateV2FragmentArgs(
                        initTab = initTab, categoryId = categoryId, source = source
                    ).toBundle()
                )
            } else {
                Control.navigate(
                    fragment, R.id.editor_create, EditorCreateFragmentArgs(source = source).toBundle()
                )
            }
        }

        fun ugcDetail(
            fragment: Fragment,
            args: Bundle,
        ) {
            Control.navigate(
                fragment, R.id.ugc_game_detail, navData = args
            )
        }

        fun ugcDetail(
            fragment: Fragment,
            ugcId: String,
            parentId: String?,
            resIdBean: ResIdBean,
            targetCommentId: String? = null,
            targetReplyId: String? = null,
            source: String? = null,
        ) {
            runCatching {
                if (ugcId.isNullOrBlank()) {
                    fragment.toast(R.string.invalid_ugcid)
                } else {
                    val args = UgcGameDetailFragmentArgs(
                        ugcId, parentId.orEmpty(), resIdBean, targetCommentId, targetReplyId, source ?: UgcGameDetailFragmentArgs.DEFAULT_SOURCE
                    ).asMavericksArgs()
                    ugcDetail(fragment, args)
                }
            }
        }

        fun ugcAll(fragment: Fragment, cardId: String, title: String) {
            Control.navigate(
                fragment, R.id.ugc_all, UgcAllFragmentArgs(cardId, title).asMavericksArgs()
            )
        }

        fun duplicateImage(
            activity: Activity,
            ratioWidth: Int,
            ratioHeight: Int,
            mimeType: List<String>?,
            maxFileSize: Long,
            folderPath: String,
            messageId: Int,
            bizCode: String?,
            compressVideo: Boolean?,
            maxVideoSecond: Int?,
            minVideoSecond: Int?,
        ) {
            activity.startActivity(Intent(activity, DuplicateImageActivity::class.java).apply {
                putExtra(
                    DuplicateImageActivity.EXTRA_KEY_PARAMS_DUPLICATE, DuplicateImageActivityArgs(
                        ratioWidth, ratioHeight, mimeType, maxFileSize, folderPath, messageId, StartupContext.get().processType.desc, bizCode, compressVideo, maxVideoSecond, minVideoSecond
                    )
                )
            })
        }

        fun ugcBackup(fragment: Fragment, args: UgcBackupFragmentArgs) {
            Control.navigate(fragment, R.id.ugc_backup_fragment, args.asMavericksArgs())
        }
    }

    object Profile {
        fun mine(fragment: Fragment) {
            Control.navigate(fragment, R.id.myProfile, navOptions = navOptions {
                anim {
                    enter = R.anim.pickerview_slide_in_bottom
                    exit = androidx.navigation.ui.R.anim.nav_default_exit_anim
                    popExit = R.anim.pickerview_slide_out_bottom
                    popEnter = androidx.navigation.ui.R.anim.nav_default_pop_enter_anim
                }
            })
        }

        fun other(
            fragment: Fragment, otherUuid: String, from: String, checkFollow: Boolean = false
        ) {
            if (GlobalContext.get().get<AccountInteractor>().isMe(otherUuid)) {
                Control.navigate(
                    fragment, R.id.profileFragment, ProfileArgs(
                        uuid = otherUuid, from = from, false, isMe = true, isFromBottom = false, entry = MeProfileFragment.FROM_OTHER
                    ).asMavericksArgs(), navOptions = Control.getCommonNavOptionsBuilder().setPopUpTo(R.id.profileFragment, inclusive = true).build()
                )
            } else {
                Control.navigate(
                    fragment, R.id.profileFragment, ProfileArgs(
                        uuid = otherUuid, from = from, checkFollow = checkFollow, isMe = false, isFromBottom = false, entry = -1
                    ).asMavericksArgs(), navOptions = Control.getCommonNavOptionsBuilder().setPopUpTo(R.id.profileFragment, inclusive = true).build()
                )
            }
        }

        fun followAndFans(
            fragment: Fragment, isMe: Boolean, uuid: String, followCount: Long, fansCount: Long, friendCount: Long? = 0, type: Int
        ) {
            Control.navigate(
                fragment, R.id.userRelationTabFragment, UserFansTabFragmentDialogArgs(
                    isMe, uuid, followCount, fansCount, friendCount, type
                ).asMavericksArgs()
            )
        }
    }

    object Report {

        fun userReport(
            fragment: Fragment, source: String, targetUserName: String, targetUid: String, requestKey: String
        ) {
            fragment.setFragmentResultListenerByActivity(requestKey) { key, bundle ->
                if (bundle.getBoolean(AppReportUserFragment.RESULT_KEY_SUCCESS)) {
                    fragment.clearFragmentResultAndListenerByActivity(key)
                    ReportReasonDialog.showReportSuccessDialog(
                        fragment, ReportSuccessDialogAnalyticsParams.User(
                            uuid = targetUid
                        )
                    )
                }
            }
            Control.navigate(
                fragment, R.id.appReportUser, AppReportUserFragmentArgs(
                    targetUid = targetUid, targetUerName = targetUserName, source = source, requestKey = requestKey
                ).toBundle()
            )
        }

        fun pgcCommentReport(
            fragment: Fragment, commentId: String?, type: ReportType, gameId: String?, requestKey: String = ReportReasonDialog.REQUEST_REPORT_REASON_DIALOG
        ) {
            commentId?.let {
                Control.navigate(
                    fragment, R.id.dialogReport, ReportReasonDialogArgs(
                        reportType = type, reportTargetId = it, gameid = gameId, reportUuid = null, needResult = false
                    ).toBundle()
                )
            }
        }

        fun reportRoom(fragment: Fragment, roomId: String, ownerId: String) {
            Control.navigate(
                fragment, R.id.dialogReport, ReportReasonDialogArgs(
                    reportType = ReportType.ReportRoom, reportTargetId = roomId, gameid = null, reportUuid = ownerId, needResult = false
                ).toBundle()
            )
        }

        fun postReport(
            fragment: Fragment, targetId: String, targetType: ReportType, gameId: String? = null, callback: ((Boolean) -> Unit)? = null
        ) {
            if (callback != null) {
                fragment.setFragmentResultListenerByActivity(
                    ReportReasonDialog.KEY_REPORT_RESULT, fragment.viewLifecycleOwner
                ) { _, bundle ->
                    fragment.clearFragmentResultAndListenerByActivity(ReportReasonDialog.KEY_REPORT_RESULT)
                    val result = bundle.getBoolean(ReportReasonDialog.KEY_REPORT_RESULT, false)
                    callback(result)
                }
            }
            Control.navigate(
                fragment, R.id.dialogReport, ReportReasonDialogArgs(
                    reportType = targetType, reportTargetId = targetId, gameid = gameId, reportUuid = null, needResult = callback != null
                ).toBundle()
            )
        }

        /**
         * 群聊举报
         */
        fun reportChatGroup(
            fragment: Fragment, groupId: String, callback: ((Boolean) -> Unit)? = null
        ) {
            if (callback != null) {
                fragment.setFragmentResultListenerByActivity(
                    ReportReasonDialog.KEY_REPORT_RESULT, fragment.viewLifecycleOwner
                ) { _, bundle ->
                    fragment.clearFragmentResultAndListenerByActivity(ReportReasonDialog.KEY_REPORT_RESULT)
                    val result = bundle.getBoolean(ReportReasonDialog.KEY_REPORT_RESULT, false)
                    callback(result)
                }
            }
            Control.navigate(
                fragment, R.id.dialogReport, ReportReasonDialogArgs(
                    reportType = ReportType.ReportGroupChat, reportTargetId = groupId, gameid = null, reportUuid = null, needResult = callback != null
                ).toBundle()
            )
        }

        fun violateRulesDialog(fragment: Fragment, args: ViolateRulesDialogArgs) {
            ViolateRulesDialog.newInstance(args).show(fragment.childFragmentManager, "ViolateRulesDialog")
        }

        fun violateRulesDialog(fragmentManager: FragmentManager, args: ViolateRulesDialogArgs) {
            ViolateRulesDialog.newInstance(args).show(fragmentManager, "ViolateRulesDialog_fragmentManager")
        }
    }

    object Comment {
        fun goCommentList(fragment: Fragment, gid: String) {
            GameDetail.navigate(
                fragment, gid, ResIdBean().setCategoryID(CategoryId.SYS_MESSAGE), "", isComment = true
            )
        }
    }

    object Pay {
        fun startPay(payInfo: PayInfo) {
            GamePayLifecycle.startPay(payInfo)
        }

        fun goPay(fragment: Fragment) {
            Control.navigate(fragment, R.id.pay)
        }

        fun goBuyCoinsPage(
            context: Context,
            fragment: Fragment?,
            pageSource: String?,
            gameCode: String? = null,
        ) {
            if (fragment == null) {
                RootNavHostFragmentActivity.start(
                    context,
                    R.id.buyCoinsPage,
                    BuyCoinsFragmentArgs(
                        pageSource = pageSource,
                        gameCode = gameCode,
                    ).toBundle(),
                )
            } else {
                Control.navigate(
                    fragment,
                    R.id.buyCoinsPage,
                    BuyCoinsFragmentArgs(
                        pageSource = pageSource,
                        gameCode = gameCode,
                    ).toBundle(),
                )
            }
        }

        /**
         * 跳转交易详情页
         */
        fun goTransactionDetailsPage(
            fragment: Fragment,
        ) {
            Control.navigate(
                fragment, R.id.transactionDetailsPage
            )
        }
    }

    object Post {
        fun goPostRule(fragment: Fragment, showConfirmBtn: Boolean = true) {
            PostRuleDialog().apply {
                arguments = bundleOf(PostRuleDialog.KEY to showConfirmBtn)
                show(fragment.childFragmentManager, PostRuleDialog.KEY)
            }
        }

        fun goPostRuleWithCallback(
            fragment: Fragment, callback: (Boolean) -> Unit
        ) {
            if (GlobalContext.get().get<MetaKV>().communityKV.hasReadCommunityRule) {
                callback(true)
            } else {
                fragment.childFragmentManager.setFragmentResultListener(
                    PostRuleDialog.KEY, fragment.viewLifecycleOwner
                ) { _, bundle ->
                    val result = bundle.getBoolean(PostRuleDialog.KEY)
                    if (result) {
                        GlobalContext.get().get<MetaKV>().communityKV.hasReadCommunityRule = true
                    }
                    callback(result)
                    fragment.childFragmentManager.clearFragmentResultListener(PostRuleDialog.KEY)
                }
                PostRuleDialog().apply {
                    arguments = bundleOf(PostRuleDialog.KEY to true)
                    show(fragment.childFragmentManager, PostRuleDialog.KEY)
                }
            }
        }

        fun goPublishPost(
            fragment: Fragment,
            resIdBean: ResIdBean,
            showRule: Boolean = true,
            content: String? = null,
            medias: List<PostMediaResource>? = null,
            games: List<PostCardInfo>? = null,
            tags: List<PostTag>? = null,
            fromGameId: String? = null,
            fromPkgName: String? = null,
            templateId: Int? = null,
            navOptions: NavOptions? = null,
            clearTopBackFeed: Boolean = false,
            customCacheKey: String? = null,
            enableOutfitShare: Boolean = false,
            isPublishVideo: Boolean = false,
            isFromMoment: Boolean = false,
            moments: List<PostMomentCard>? = null,
            ugcDesign: PostUgcDesignCard? = null,
            shareReqId: String? = null,
            postPublish: PostPublish? = null,
            isMainActivity: Boolean = false
        ): Pair<Boolean, String?> {
            if (!fragment.canShowDialog) return false to "fragment unavailable"
            val publishPostInteractor: PublishPostInteractor = GlobalContext.get().get()
            if (!publishPostInteractor.canPublish()) {
                fragment.toast(R.string.post_publishing_tip)
                if (!fromGameId.isNullOrEmpty()) {
                    resumeGameById(fromGameId)
                }
                return false to "The last post is being uploaded"
            }

            AccPwdV7Dialog.show(fragment, AccPwdV7DialogArgs.SOURCE_PUBLISH_POST) {
                if (!it) {
                    if (!fromGameId.isNullOrEmpty()) {
                        resumeGameById(fromGameId)
                    }
                    return@show
                }
                fun goPublishPostHelper() {
                    Analytics.track(EventConstants.EVENT_EVENT_PUBLISH_POST_JUMP)
                    val args = PublishPostFragmentArgs(
                        content,
                        PostPublish.validateMedias(medias),
                        PostPublish.validateGames(games),
                        PostPublish.validateTags(tags),
                        clearTopBackFeed, fromGameId,
                        fromPkgName,
                        resIdBean,
                        templateId,
                        customCacheKey,
                        enableOutfitShare,
                        isPublishVideo,
                        isFromMoment,
                        moments,
                        ugcDesign,
                        shareReqId,
                        postPublish
                    ).asMavericksArgs()

                    if(isMainActivity) {
                        kotlin.runCatching {
                            Control.navigate(
                                fragment,
                                R.id.publish_post,
                                args,
                                navOptions
                            )
                        }
                    } else {
                        RootNavHostFragmentActivity.start(
                            fragment.requireContext(),
                            R.id.publish_post,
                            args
                        )
                    }
                }

                if (showRule) {
                    if (!fragment.canShowDialog) return@show
                    goPostRuleWithCallback(fragment) {
                        if (it) {
                            goPublishPostHelper()
                        } else {
                            Analytics.track(EventConstants.EVENT_PUBLISH_RULE_JUMP)
                            if (!fromGameId.isNullOrEmpty()) {
                                resumeGameById(fromGameId)
                            }
                        }
                    }
                } else {
                    goPublishPostHelper()
                }
            }

            return true to null
        }

        /**
         * 全局截图有三种打开发帖页的情形：
         * 1.游戏进程内；
         * 2.主进程中MainActivity内；
         * 3.主进程中其他Activity内。
         * */
        fun screenShort2PublishPost(
            fragment: Fragment,
            rawData: ShareRawData
        ) {
            val imageUrl = rawData.image ?: return
            val isReleasedGamed = rawData.isReleasedGame ?: false
            val isMainActivity = rawData.isMainActivity ?: false

            if(isReleasedGamed) {
                // 在游戏进程内(此处的游戏特指已发布的ugc或pgc游戏)
                val cardInfo: PostCardInfo? = if(rawData.ugcGame != null) {
                    val ugcGame = rawData.ugcGame
                    PostCardInfo(
                        PostCardInfo.TYPE_UGC,
                        ugcGame.id,
                        ugcGame.packageName.orEmpty(),
                        ugcGame.loveQuantity,
                        ugcGame.pvCount,
                        ugcGame.author?.name,
                        ugcGame.banner,
                        null,
                        ugcGame.ugcGameName,
                        0.0f,
                        null
                    )
                } else if(rawData.pgcGame != null) {
                    val pgcGame = rawData.pgcGame
                    PostCardInfo(
                        PostCardInfo.TYPE_PGC,
                        pgcGame.id.orEmpty(),
                        pgcGame.pkg.orEmpty(),
                        pgcGame.likeCount ?: 0,
                        pgcGame.playerCount ?: 0,
                        pgcGame.authorName,
                        pgcGame.icon,
                        null,
                        pgcGame.name,
                        pgcGame.avgScore ?: 0.0f,
                        null
                    )
                } else {
                    null
                }

                val cardList = if(cardInfo != null) arrayListOf(cardInfo) else null
                Main.openPostPublishFromGame(
                    fragment.requireContext(),
                    content = null,
                    briefMedias = arrayListOf(imageUrl),
                    completeCardList = cardList,
                    tagIdList = null,
                    clearTopBackFeed = false,
                    MWBizBridge.currentGameId(),
                    MWBizBridge.currentGamePkg(),
                    enableOutfitShare = false,
                    showCategoryId = CategoryId.SCREENSHOT_SHARE_POST
                )
            } else {
                // 在主进程打开内, 可能在MainActivity或其他Activity(例如：角编页的FullScreenEditorActivity)
                val mediaList = JumpPublishPostUtil
                    .assembleMediaList(fragment.requireContext(), listOf(imageUrl))
                goPublishPost(
                    fragment,
                    ResIdBean().setCategoryID(CategoryId.SCREENSHOT_SHARE_POST),
                    medias = mediaList,
                    isMainActivity = isMainActivity
                )
            }
        }

        fun addTag(fragment: Fragment) {
            AddTagDialog().show(fragment.childFragmentManager, "AddTagDialog")
        }

        fun addGameCard(fragment: Fragment, maxNum: Int) {
            AddGameCardFrameFragmentDialog.newInstance(
                AddCardFragmentDialogArgs(maxNum)
            ).show(fragment.childFragmentManager, "AddGameCardFrameFragmentDialog")
        }

        /**
         * @param postId 帖子id
         * @param source 来源
         * @param fromTagDetailInfo 从话题详情页进入时携带的话题页信息
         * @param targetCommentId 拉取定位评论id
         * @param videoProgress 视频进度
         */
        fun goPostDetail(
            fragment: Fragment,
            postId: String,
            source: String,
            fromTagDetailInfo: PostTag? = null,
            targetCommentId: String? = null,
            targetReplyId: String? = null,
            videoProgress: Long = 0L,
        ) {
            Control.navigate(
                fragment, R.id.post_detail, PostDetailFragmentArgs(
                    postId, targetCommentId, targetReplyId, videoProgress, source, fromTagDetailInfo
                ).asMavericksArgs()
            )
        }

        fun topicSquare(fragment: Fragment) {
            Analytics.track(EventConstants.TOPIC_SQUARE_SHOW)
            Control.navigate(fragment, R.id.topic_square)
        }

        /**
         * 话题详情页
         * @param tagId 话题id
         * @param tagName 话题名称
         * @param source 来源埋点 [com.socialplay.gpark.function.analytics.EventParamConstants.SOURCE_TOPIC_FEED]
         * @param sourcePostId 来源帖子id
         */
        fun topicDetail(
            fragment: Fragment, tagInfo: PostTag, source: String, sourcePostId: String?, initTab: Int = -1
        ) {
            Analytics.track(EventConstants.COMMUNITY_TAG_CLICK) {
                put(EventParamConstants.KEY_SOURCE, source)
                put(EventParamConstants.KEY_TAG_ID, tagInfo.tagId)
                sourcePostId?.let { put(EventParamConstants.KEY_POSTID, it) }
            }
            Analytics.track(EventConstants.TOPIC_DETAIL_SHOW) {
                put(EventParamConstants.KEY_TAG_ID, tagInfo.tagId)
            }
            Control.navigate(
                fragment, R.id.topic_detail, TopicDetailTabFragmentArgs(tagInfo, initTab).asMavericksArgs()
            )
        }
    }

    object Video {
        fun fullScreenPlayer(
            fragment: Fragment, analyticFrom: String, analyticExtra: Bundle? = null
        ) {
            Control.navigate(
                fragment, R.id.videoPlayerFullScreen, VideoPlayerFullScreenFragmentArgs(
                    analyticFrom = analyticFrom, analyticExtra = analyticExtra
                ).toBundle()
            )
        }

        fun goRecommendVideoList(
            fragment: Fragment, resIdBean: ResIdBean, initialList: VideoFeedApiResult? = null
        ) {
            Control.navigate(
                fragment, R.id.recommendVideoList, RecommendVideoListArgs(resIdBean, initialList).asMavericksArgs()
            )
        }

        fun goRecommendVideoFeed(
            fragment: Fragment, resIdBean: ResIdBean, videoId: String? = null
        ) {
            val args = RecommendVideoFeedArgs(resIdBean, videoId).asMavericksArgs()
            Control.navigate(fragment, R.id.recommendVideoFeed, args)
        }

        fun goVideoPublishRule(fragment: Fragment) {
            VideoPublishRuleDialog.show(fragment)
        }

        private fun showVideoPublishRuleWithCallback(
            fragment: Fragment, callback: (hasReadPublishRule: Boolean) -> Unit
        ) {
            val videoFeedKV = GlobalContext.get().get<MetaKV>().videoFeedKV

            val hasReadPublishRule = videoFeedKV.hasReadPublishRule

            if (hasReadPublishRule) {
                callback(true)
            } else {
                VideoPublishRuleDialog.show(fragment) {
                    if (it) {
                        videoFeedKV.hasReadPublishRule = true
                    }
                    callback(it)
                }
            }
        }

        fun publish(fragment: Fragment, resIdBean: ResIdBean) {
            showVideoPublishRuleWithCallback(fragment) {
                if (it) {
                    Post.goPublishPost(
                        fragment, resIdBean = resIdBean, showRule = false, isPublishVideo = true
                    )
                }
            }
        }
    }

    object HomeRoom {
        fun goAllRoom(fragment: Fragment) {
            Control.navigate(fragment, R.id.all_room)
        }

        fun goCreateRoom(fragment: Fragment) {
            Control.navigate(fragment, R.id.create_room)
        }

        fun goAllHomeRoom(fragment: Fragment) {
            Control.navigate(fragment, R.id.cottageAllRoom)
        }
    }

    object Feedback {
        /**
         * @param gameId 来源游戏id
         * @param defaultSelectType 默认选择type [com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest.NET_SOURCE_APP]
         * @param source 来源页面 [com.socialplay.gpark.data.model.feedback.SubmitNewFeedbackRequest.SOURCE_AVATAR_NUMBER]
         */
        fun feedback(
            fragment: Fragment, gameId: String?, source: String, defaultSelectType: String?, needBackRole: Boolean, needBackGame: Boolean, fromGameId: String?
        ) {
            Analytics.track(EventConstants.EVENT_FEEDBACK_CLICK) {
                put("gameid", gameId.toString())
                put("source", source)
            }
            Control.navigate(
                fragment, R.id.feedback, FeedbackFragmentArgs(
                    gameId, source, defaultSelectType, needBackRole, needBackGame, fromGameId
                ).asMavericksArgs()
            )
        }
    }

    object Plot {
        @Deprecated("Use Moments.main instead")
        fun list(fragment: Fragment, categoryId: Int) {
            Moments.main(fragment, categoryId)
        }

        fun chooseImage(
            activity: Activity,
            ratioWidth: Int,
            ratioHeight: Int,
            useClip: Boolean,
            messageId: Int,
            gameId: String,
        ) {
            activity.startActivity(Intent(activity, PlotChooseImageActivity::class.java).apply {
                putExtra(
                    PlotChooseImageActivity.EXTRA_KEY_PARAMS, PlotClipImageActivityArgs(ratioWidth, ratioHeight, useClip, messageId, gameId)
                )
            })
        }
    }

    object Moments {
        fun main(fragment: Fragment, categoryId: Int, finishMain: Boolean = false) {
            Control.navigate(
                fragment, R.id.moments, MomentsFragmentArgs(categoryId, finishMain).toBundle()
            )
        }

        fun type(fragment: Fragment, title: String, type: Int, categoryId: Int) {
            Control.navigate(
                fragment, R.id.moments_type, MomentsTypeListFragmentArgs(title, type, categoryId).toBundle()
            )
        }
    }

    object Settings {
        fun changeLanguage(context: Context) {
            context.startActivity(Intent(context, LanguageRecreateActivity::class.java))
        }
    }

    object Scheme {

        /**
         * 跳转内部scheme
         * @param [source] 来源 [com.socialplay.gpark.function.deeplink.LinkData]
         * @return [Boolean] 是否成功被处理 true:处理成功 false:未处理
         */
        fun jumpScheme(
            fragment: Fragment, mainViewModel: MainViewModel, uri: Uri, source: String
        ): Boolean {
            if (uri.scheme == BuildConfig.SCHEME_URI) {
                return MetaDeepLink.handle(
                    fragment.requireActivity(), fragment, mainViewModel, uri, source
                ).succeeded
            }
            return false
        }

        /**
         * 用startActivity的方式跳转内部scheme
         * @param [source] 来源 [com.socialplay.gpark.function.deeplink.LinkData]
         */
        fun jumpScheme(fragment: Fragment, uri: Uri, source: String) {
            if (uri.scheme == BuildConfig.SCHEME_URI) {
                // 内部scheme
                fragment.context?.let {
                    runCatching {
                        it.startActivity(Main.mainActivityIntent(it, source).apply {
                            data = uri
                        })
                    }.getOrElse {
                        Timber.e(it, "inside scheme jump failed. uri:${uri}, source:${source}")
                    }
                }
            } else {
                // 外部scheme/http url
                runCatching {
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    fragment.startActivity(intent)
                }.getOrElse {
                    Timber.e(it, "outside scheme jump failed. uri:${uri}, source:${source}")
                }
            }
        }
    }

    object Share {
        fun share(
            fragment: Fragment, shareContent: ShareContent, requestKey: String, callback: ((success: Boolean, selectedUuid: String?) -> Unit)? = null
        ) {
            fragment.setFragmentResultListenerByActivity(requestKey) { _, bundle ->
                val result = bundle.getBoolean(ContactListFragment.SHARE_CALLBACK, false)
                val uuid = bundle.getString(ContactListFragment.KEY_SELECT_UUID)
                callback?.invoke(result, uuid)
            }
            Control.navigate(
                fragment,
                R.id.community_share_dialog,
                navData = CommunityShareDialogArgs(
                    GsonUtil.safeToJson(shareContent), requestKey
                ).asMavericksArgs(),
            )
        }
    }

    object AiBot {
        fun showGenderDialog(fragment: Fragment) {
            AIBotUGCGenderDialog().show(fragment.childFragmentManager, "AIBotUGCGenderDialog")
        }

        fun gotoAIBotCreate(fragment: Fragment, gender: String) {
            Control.navigate(
                fragment, R.id.fragment_ai_bot_ugc, AIBotUGCCreateFragmentArgs(
                    gender
                ).toBundle()
            )
        }

        fun showAiTagDialog(
            fragment: Fragment, tagIdList: List<Int>?, isBlack: Boolean = false, maxCount: Int = 1, callback: (List<BotLabelInfo>?) -> Unit
        ) {
            val request = AiBotTagDialog.KEY_REQUEST_TAG
            fragment.setFragmentResultListenerByActivity(request) { _, bundle ->
                fragment.childFragmentManager.clearFragmentResultListener(request)
                val data = bundle.getString(request)
                val list = GsonUtil.gsonSafeParseCollection<List<BotLabelInfo>>(data)
                callback(list)
            }
            Control.navigate(
                fragment, R.id.aiBotTagDialog, navData = AiBotTagDialogArgs(tagIdList, isBlack, maxCount).asMavericksArgs()
            )
        }

        fun gotoConversation(
            fragment: Fragment, botId: String, id: String, source: String, reqid: String? = null
        ) {
            Analytics.track(
                EventConstants.COMMUNITY_APP_BOT_ENTER, map = mapOf("botid" to botId, "source" to source, "reqid" to reqid.toString())
            )
            Control.navigate(
                fragment, R.id.ai_bot_conversation, navData = AiBotConversationFragmentArgs(botId, id, source, reqid).asMavericksArgs()
            )
        }

        fun gotoAiBotDetail(
            fragment: Fragment, botId: String, id: String, source: String, reqid: String? = null
        ) {
            Analytics.track(
                EventConstants.COMMUNITY_APP_BOT_PROFILE, map = mapOf(
                    "botid" to botId,
                    "source" to source,
                    "reqid" to reqid.toString(),
                )
            )
            Control.navigate(
                fragment, R.id.ai_bot_detail, navData = AiBotDetailFragmentArgs(botId, id, reqid).asMavericksArgs()
            )
        }

        fun gotoAIBotCreateResult(
            fragment: Fragment, data: String
        ) {
            Control.navigate(
                fragment, R.id.fragment_ai_bot_ugc_result, navData = AIBotUGCCreateResultFragmentArgs(
                    data = data
                ).toBundle()
            )
        }

        fun gotoAIBotSelect(fragment: Fragment, data: String) {
            Control.navigate(
                fragment, R.id.fragment_ai_bot_ugc_select, navData = AIBotUGCSelectFragmentArgs(
                    data = data
                ).toBundle()
            )
        }

        fun clipAIBotImage(
            fragment: Fragment, inputPath: String, ratioWidth: Int, ratioHeight: Int, callback: (String?) -> Unit
        ) {
            val request = AIBotUGCImageClipFragment.CLIP_RESULT
            fragment.setFragmentResultListener(request) { _, bundle ->
                fragment.childFragmentManager.clearFragmentResultListener(request)
                callback(bundle.getString(request))
                fragment.clearFragmentResult(request)
            }
            Control.navigate(
                fragment, R.id.fragmentAiBotClip, navData = PlotClipImageFragmentArgs(
                    inputPath, ratioWidth, ratioHeight, true
                ).asMavericksArgs()
            )
        }

        fun gotoAIBotGenerate(fragment: Fragment, data: String) {
            Control.navigate(
                fragment, R.id.fragment_ai_bot_generate, navData = AIBotUGCGenerateFragmentArgs(
                    data = data
                ).toBundle()
            )
        }
    }

    object Kol {
        fun showAllLabelDialog(
            fragment: Fragment, labelList: List<CommonLabelInfo>, requestKey: String, callback: (label: CommonLabelInfo?) -> Unit
        ) {
            fragment.setFragmentResultListenerByActivity(requestKey) { _, bundle ->
                fragment.childFragmentManager.clearFragmentResultListener(requestKey)
                callback(bundle.getParcelable(KolGameLabelDialog.KEY_SELECT_TAG))
            }
            Control.navigate(
                fragment, R.id.kolGameLabelDialog, navData = KolGameLabelDialogArgs(requestKey, labelList).asMavericksArgs()
            )
        }

        fun moreFollowedUgc(fragment: Fragment) {
            Control.navigate(
                fragment, R.id.kolMoreUgc, navData = KolMoreUgcFragmentArgs(KolMoreUgcFragment.PAGE_TYPE_FOLLOW).asMavericksArgs()
            )
        }

        fun moreRecommendUgc(fragment: Fragment) {
            Control.navigate(
                fragment, R.id.kolMoreUgc, navData = KolMoreUgcFragmentArgs(KolMoreUgcFragment.PAGE_TYPE_RECOMMEND).asMavericksArgs()
            )
        }

        fun moreTypeCreator(
            fragment: Fragment,
            selectTab: Int = TypeKoCreatorParentFragment.TAB_FOLLOW
        ) {
            Control.navigate(
                fragment,
                R.id.kolTypeCreator,
                navData = TypeKoCreatorParentFragmentArgs(selectTab = selectTab).asMavericksArgs()
            )
        }

        fun moreLabelCreator(fragment: Fragment, labelList: List<KolCreatorLabel>) {
            Control.navigate(
                fragment, R.id.kolLabelCreator, navData = LabelKolCreatorFragmentArgs(labelList).asMavericksArgs()
            )
        }
    }

    object Played {
        fun manager(fragment: Fragment) {
            if (fragment.isAdded && !fragment.isDetached) {
                Control.navigate(fragment, R.id.continueManage)
            } else {
                Timber.w("Prevent navigation from detached fragment")
            }
        }
    }

    object UgcDesign {
        fun detail(
            fragment: Fragment,
            itemId: String,
            categoryId: Int,
            tabId: Int = -1,
            targetCommentId: String? = null,
            targetReplyId: String? = null,
            needResult: Boolean = false
        ) {
            Control.navigate(
                fragment,
                R.id.ugc_design_detail,
                navData = UgcDesignDetailArgs(
                    itemId,
                    when (categoryId) {
                        CategoryId.UGC_DESIGN_FEED -> 0
                        CategoryId.UGC_DESIGN_PROFILE_CLOTHES -> 1
                        CategoryId.UGC_DESIGN_PROFILE_DESIGN_TAB -> 3
                        else -> -1
                    },
                    tabId,
                    targetCommentId,
                    targetReplyId,
                    categoryId,
                    needResult
                ).asMavericksArgs()
            )
        }

        fun edit(
            fragment: Fragment,
            itemId: String,
            cover: String?,
            mutable: UgcAssetMutable,
            trackId: String,
            from: String? = null,
            forceShowPrice: Boolean = false
        ) {
            Control.navigate(
                fragment, R.id.ugc_design_edit,
                navData = UgcDesignEditFragmentArgs(
                    itemId,
                    cover,
                    mutable,
                    trackId,
                    from,
                    forceShowPrice = forceShowPrice
                ).asMavericksArgs()
            )
        }

        fun moduleTab(
            fragment: Fragment, fromGuide: Int = UgcModuleTabArgs.GUIDE_TYPE_NONE
        ) {
            Control.navigate(
                fragment, R.id.ugcModuleTab, UgcModuleTabArgs(fromGuide).asMavericksArgs()
            )
        }

        fun assetList(
            fragment: Fragment,
            entrance: Int,
            uuid: String,
            isMe: Boolean,
            title: String?,
            type: Int,
            mode: Int = UgcAssetListState.MODE_NORMAL
        ) {
            Control.navigate(
                fragment,
                R.id.ugcAssetList,
                navData = UgcAssetListArgs(
                    entrance,
                    uuid,
                    isMe,
                    title,
                    type,
                    mode
                ).asMavericksArgs()
            )
        }

        fun rookie(
            fragment: Fragment
        ) {
            Control.navigate(
                fragment, R.id.ugcAssetRookie
            )
        }

        fun rookie(
            navController: NavController
        ) {
            Control.navigate(
                navController, R.id.ugcAssetRookie
            )
        }

        fun recommendToolkitPage(
            fragment: Fragment,
            isMe: Boolean,
            gameId: String,
        ) {
            Control.navigate(
                fragment,
                R.id.toolkitRecommendFragment,
                ToolkitRecommendFragmentArgs(
                    isMe = isMe,
                    gameId = gameId,
                ).toBundle()
            )
        }
    }

    object Assets {
        fun assetList(fragment: Fragment, item: ChoiceCardInfo) {
            val args = AssetListFragmentArgs(
                cardId = "${item.cardId}", cardName = "${item.cardName}", initialList = item.gameListNoNull
            )
            Control.navigate(fragment, R.id.assetListFragment, args.asMavericksArgs())
        }
    }

    object Template {
        fun templateListDetail(
            fragment: Fragment,
            item: ChoiceCardInfo,
        ) {
            val args = TemplateListDetailFragmentArgs(
                cardId = "${item.cardId}", cardName = "${item.cardName}", initialList = item.gameListNoNull
            ).asMavericksArgs()

            Control.navigate(
                fragment, R.id.templateListDetailFragment, args
            )
        }
    }

    object Campaign {
        /**
         * @param source
         * home_tab 首页页签
         * home_banner 首页banner
         * discover_banner 精选banner
         * profile_pop 个人页弹窗
         * community_tab 社区页签
         */
        fun welfareTaskDetail(
            fragment: Fragment,
            source: String,
        ) {
            Control.navigate(
                fragment,
                R.id.welfareTaskFragment,
                WelfareTaskFragmentArgs(
                    fromTabLayout = false,
                    source = source,
                ).toBundle()
            )
        }
    }
}