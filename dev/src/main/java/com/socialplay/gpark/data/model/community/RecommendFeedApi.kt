package com.socialplay.gpark.data.model.community

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.internal.model.toLocalStatus
import com.meta.box.biz.friend.model.LabelInfo
import com.meta.box.biz.friend.model.LabelInfo.LabelExtendInfo
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.data.model.post.CommunityFeedInfo.Companion.OPTION_LIKE
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostDetail
import com.socialplay.gpark.data.model.post.PostDetail.Companion.STATUS_OK
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostMomentCard
import com.socialplay.gpark.data.model.post.PostStyleCard
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PostUgcDesignCard
import com.socialplay.gpark.data.model.post.TopicBean
import com.socialplay.gpark.data.model.post.event.PostMetaData
import com.socialplay.gpark.data.model.user.UserTagInfo
import com.socialplay.gpark.util.GsonUtil
import java.lang.reflect.Type

data class RecommendFeedCardRequestBody(
    /**
     * 每页数量
     */
    val pageSize: Int,
    /**
     * 翻页偏移量
     */
    val offset: String?,
)

data class RecommendUsersResponse(
    val offset: String?,
    val hasMore: Boolean?,
    val list: List<UserFeedUser>?,
)

data class RecommendFeedCardListResponse(
    /**
     * 是否还有更多数据
     */
    val hasMore: Boolean?,
    /**
     * 下一页的滚动偏移量
     */
    val offset: String?,
    val list: List<RecommendFeedCard>?,
) {
    fun filterInvalidElements(currentUuid: String): RecommendFeedCardListResponse {
        val list = this.list?.mapNotNull { feedCard ->
            // Mavericks 会对数据对象调用 hashCode() 方法
            // 如果字段声明为非空类型, 但实际没下发或者下发了null, 调用 hashCode() 方法就会闪退
            // 所以就过滤掉有问题的数据
            val isDebug = BuildConfig.DEBUG
            var result = if (isDebug) {
                // debug 情况下, 为了提前暴露问题, 就不做过滤
                feedCard
            } else {
                // release 情况下, 尽量不影响用户, 就过滤掉不合法的数据
                try {
                    feedCard.hashCode()
                    feedCard
                } catch (_: Throwable) {
                    null
                }
            }
            if (result != null && result is UserFeedCard) {
                // 推荐用户列表需要, 需要把自己过滤掉
                val userList = result.list?.filter { user ->
                    !user.uid.isNullOrEmpty() && user.uid != currentUuid
                }
                result = result.copy(
                    list = userList
                )
            }
            result
        }
        return this.copy(list = list)
    }
}

sealed class RecommendFeedCard() {
    companion object {
        const val CARD_TYPE_POST = 1
        const val CARD_TYPE_TOPIC = 2
        const val CARD_TYPE_USER = 3
    }

    val isPostFeedCard: Boolean get() = this is PostFeedCard

    fun asPostFeedCard(): PostFeedCard? {
        if (this is PostFeedCard) {
            return this
        }
        return null
    }
}

data class PostFeedCard(
    val cardType: Int,
    val uid: String? = null,
    val postId: String? = null,
    val essence: Boolean? = null,
    val title: String? = null,
    val content: String? = null,
    val createTime: Long? = null,
    val lastDiscussionTime: Long? = null,
    val commentCount: Long? = null,
    val likeCount: Long? = null,
    // 是否置顶
    val top: Boolean? = null,
    // 用户点赞状态：0无感、1点赞
    val opinion: Int? = null,
    val shareCount: Long? = null,
    val user: User? = null,
    val userStatus: FriendStatus? = null,
    val styleCardList: List<PostStyleCard>? = null,
    val plotCardList: List<PostMomentCard>? = null,
    val gameCardList: List<PostCardInfo>? = null,
    val mediaList: List<PostMediaResource>? = null,
    // TODO 服务器缺少此字段
    val clothesCardList: List<PostUgcDesignCard>? = null,
    // 话题列表
    val communityTagList: List<TopicBean>? = null,
    val commentInfo: CommentInfo? = null,
    // 本地用：发布中的帖子的本地id
    val localId: Long? = null,
    // 本地用：是否是发布中的帖子
    val localPublishing: Boolean = false,
    val status: Int = STATUS_OK
) : RecommendFeedCard() {

    private var _tagList: List<PostTag>? = null

    // 新接口不再下发tagList字段, 改成只用 communityTagList
    // 客户端代码中, 也没发现 tagList 和 communityTagList 同时存在的必要性
    val tagList: List<PostTag>?
        get() {
            if (_tagList == null) {
                _tagList = communityTagList?.mapNotNull {
                    if (it.tagId == null) {
                        null
                    } else {
                        PostTag(
                            tagId = it.tagId,
                            tagName = it.tagName,
                        )
                    }
                }
            }
            return _tagList
        }

    // 本地用：ui排序id，兼容发布中的帖子
    val localUniqueId: String
        get() = if (localId != null) "$localId" else postId ?: ""

    val imageList: List<PostMediaResource>?
        get() = mediaList?.filter { it.resourceType == PostMediaResource.TYPE_IMG }

    val firstVideo: PostMediaResource?
        get() = videoList?.firstOrNull()

    val videoList: List<PostMediaResource>?
        get() = mediaList?.filter { it.resourceType == PostMediaResource.TYPE_VIDEO }

    val opinionLike: Boolean
        get() = opinion == OPTION_LIKE

    val canGoRoom
        get() = userStatus != null && userStatus.toLocalStatus() == FriendStatus.PLAYING_GAME && userStatus.gameStatus?.room?.roomIdFromCp != null

    val outfit: PostStyleCard?
        get() = if (canTryOn) styleCardList?.firstOrNull { it.isOutfit } else null

    val ugcDesign: PostUgcDesignCard?
        get() = clothesCardList?.firstOrNull { it.isUgcDesign }

    val canTryOn: Boolean
        get() = user?.ootdPrivateSwitch == true

    val reviewOk get() = PostDetail.isOk(status)
    val reviewInProgress get() = PostDetail.isInProgress(status)
    val reviewFail get() = PostDetail.isFail(status)
    val reviewStatus2TrackParam get() = PostDetail.convertReviewStatus2TrackParam(status)

    val netThumbNail
        get() = mediaList?.firstOrNull { it.isNetResource && it.isVideo }?.cover
            ?: mediaList?.firstOrNull { it.isNetResource && it.isImage }?.resourceValue

    val netVideo
        get() = mediaList?.firstOrNull { it.isNetResource && it.isVideo }?.resourceValue

    private var _cardList: List<Any>? = null
    val cardList: List<Any>
        get() {
            if (_cardList == null) {
                _cardList = (gameCardList ?: emptyList())
                    .plus(plotCardList ?: emptyList())
                    .plus(if (canTryOn) styleCardList ?: emptyList() else emptyList())
                    .plus(clothesCardList ?: emptyList())
            }
            return _cardList!!
        }

    fun getPostMetaData(): PostMetaData {
        return PostMetaData(
            postId = postId ?: "",
            isLike = opinionLike,
            likeCount = likeCount ?: 0,
            commentCount = commentCount ?: 0,
            shareCount = shareCount ?: 0,
            tagList = tagList
        )
    }

    fun existsGameCard(gameId: String): Boolean {
        return gameCardList?.firstOrNull { it.gameId == gameId } != null
    }
}

data class TopicFeedCard(
    val cardType: Int,
    val list: List<Topic>? = null,
) : RecommendFeedCard() {
    fun existsTopic(topicId: Long): Boolean {
        return list?.firstOrNull { it.tagId == topicId } != null
    }
}

data class UserFeedCard(
    val cardType: Int,
    val title: String? = null,
    val link: String? = null,
    val list: List<UserFeedUser>? = null
) : RecommendFeedCard()

object RecommendFeedApiDeserializer : JsonDeserializer<RecommendFeedCard?> {
    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): RecommendFeedCard? {
        try {
            val jsonObject = json?.asJsonObject ?: return null
            val type = jsonObject.get("cardType")?.asInt ?: return null
            val params = when (type) {
                RecommendFeedCard.CARD_TYPE_POST -> {
                    GsonUtil.gson.fromJson(jsonObject, PostFeedCard::class.java)
                }

                RecommendFeedCard.CARD_TYPE_TOPIC -> {
                    GsonUtil.gson.fromJson(jsonObject, TopicFeedCard::class.java)
                }

                RecommendFeedCard.CARD_TYPE_USER -> {
                    GsonUtil.gson.fromJson(jsonObject, UserFeedCard::class.java)
                }

                else -> {
                    null
                }
            }
            return params
        } catch (e: Exception) {
            return null
        }
    }
}

// PostFeedCard嵌套数据类

data class User(
    val uid: String? = null,
    val nickname: String? = null,
    val avatar: String? = null,
    val signature: String? = null,
    val origin: String? = null,
    val gender: Int? = null,
    val followStatus: Boolean? = null,
    val isOfficial: Boolean? = null,
    val tags: List<UserTagInfo>? = null,
    val labelInfo: LabelInfo? = null,
    val userReleaseCount: Int? = null,
    // TODO 服务器目前没下发此字段
    val ootdPrivateSwitch: Boolean? = null,
) {
    val tagIds get() = tags?.map { it.id }
}

// 对应于: com.meta.box.biz.friend.model.LabelInfo
//data class LabelInfo(
//    val id: Long? = null,
//    val uid: String? = null,
//    val labelId: Long? = null,
//    val name: String? = null,
//    val icon: String? = null,
//    val scope: String? = null,
//    val scopeType: Int? = null,
//    val scopeName: String? = null,
//    val createTime: String? = null,
//    val endTime: String? = null,
//    val label: Label? = null,
//    val duration: Int? = null,
//    val groupId: String? = null,
//    val type: Int? = null,
//    val extendDTO: Any? = null,
//    val level: Int? = null,
//    val using: Boolean? = null,
//    val count: Int? = null,
//    val ownCount: Int? = null,
//    val desc: String? = null,
//    val own: Boolean? = null,
//    val extend: String? = null,
//    val img: String? = null,
//    val imgName: String? = null
//)

//data class Label(
//    val id: Long? = null,
//    val name: String? = null,
//    val icon: String? = null,
//    val scopeType: Int? = null,
//    val receiveRules: String? = null,
//    val extend: String? = null,
//    val groupId: String? = null,
//    val groupName: String? = null,
//    val type: Int? = null,
//    val extendDTO: ExtendDTO? = null,
//    val level: Int? = null,
//    val desc: String? = null,
//    val displayFlag: Boolean? = null,
//    val categoryId: String? = null,
//    val categoryName: String? = null
//)

data class ExtendDTO(
    val link: String? = null,
    val gradientColor: String? = null,
    val bgColor: String? = null,
    val textColor: String? = null,
    val thumbnailIcon: String? = null,
    val dataType: Int? = null,
    val acquiringMethods: String? = null,
    val acquiringLink: String? = null,
    val quality: Int? = null,
    val guid: String? = null,
    val icon: String? = null,
    val introduction: String? = null,
    val effectParams: String? = null,
    val linkStartTime: String? = null,
    val linkEndTime: String? = null
)

data class CommentInfo(
    val user: User? = null,
    val uid: String? = null,
    val commentId: String? = null,
    val likeCount: Long? = null,
    val opinion: Int? = null,
    val content: String? = null,
    val mediaList: List<PostMediaResource>? = null
) {
    val imageList: List<PostMediaResource>?
        get() = mediaList?.filter { it.resourceType == PostMediaResource.TYPE_IMG }

    val opinionLike: Boolean
        get() = opinion == OPTION_LIKE
}

// UserFeedCard 嵌套数据类
data class UserFeedUser(
    val uid: String? = null,
    val nickname: String? = null,
    val avatar: String? = null,
    val signature: String? = null,
    val origin: String? = null,
    val gender: Int? = null,
    val followStatus: Boolean? = null,
    val tags: List<UserTagInfo>? = null,
    val userLabelDTO: UserFeedUserLabelDTO? = null,
    val online: Boolean? = false,
    val releaseCount: Long? = 0
) {
    val tagIds get() = tags?.map { it.id }

    fun toKolCreatorInfo(): KolCreatorInfo {
        return KolCreatorInfo(
            uuid = uid ?: "",
            nickname = nickname,
            avatar = avatar,
            online = online ?: false,
            followUser = followStatus ?: false,
            sinceId = null,
            releaseCount = releaseCount ?: 0,
            tags = tags,
        )
    }
}

data class UserFeedUserLabelDTO(
    val id: Long? = null,
    val uid: String? = null,
    val labelId: Long? = null,
    val name: String? = null,
    val icon: String? = null,
    val scope: String? = null,
    val scopeType: Int? = null,
    val scopeName: String? = null,
    val createTime: String? = null,
    val endTime: String? = null,
    val label: UserFeedLabel? = null,
    val duration: Int? = null,
    val groupId: String? = null,
    val type: Int? = null,
    val extendDTO: UserFeedExtendDTO? = null,
    val level: Int? = null,
    val using: Boolean? = null,
    val count: Int? = null,
    val ownCount: Long? = null,
    val desc: String? = null,
    val own: Boolean? = null,
    val extend: String? = null,
    val img: String? = null,
    val imgName: String? = null
) {
    fun toLabelInfo(): LabelInfo {
        return LabelInfo(
            labelId = labelId ?: id,
            name = name,
            icon = icon,
            scope = scope,
            // 服务器下发的字符串格式的时间戳, 目前这个字段本地没用到, 所以先随便给一个值
            endTime = System.currentTimeMillis() + 24 * 60 * 60 * 1000,
            level = level?.toString(),
            type = type,
            isWear = using ?: true,
            extendDTO = extendDTO?.toLabelExtendInfo(),
            ownCount = ownCount,
            count = count,
            desc = desc,
            own = own,
            using = using,
        )
    }
}

data class UserFeedLabel(
    val id: Long? = null,
    val name: String? = null,
    val icon: String? = null,
    val scopeType: Int? = null,
    val receiveRules: String? = null,
    val extend: String? = null,
    val groupId: String? = null,
    val groupName: String? = null,
    val type: Int? = null,
    val extendDTO: UserFeedExtendDTO? = null,
    val level: Int? = null,
    val desc: String? = null,
    val displayFlag: Boolean? = null,
    val categoryId: String? = null,
    val categoryName: String? = null
)

data class UserFeedExtendDTO(
    val link: String? = null,
    val gradientColor: String? = null,
    val bgColor: String? = null,
    val textColor: String? = null,
    val thumbnailIcon: String? = null,
    val dataType: Long? = null,
    val acquiringMethods: String? = null,
    val acquiringLink: String? = null,
    val quality: Long? = null,
    val guid: String? = null,
    val icon: String? = null,
    val introduction: String? = null,
    val effectParams: String? = null,
    val linkStartTime: String? = null,
    val linkEndTime: String? = null
) {
    fun toLabelExtendInfo(): LabelExtendInfo {
        return LabelExtendInfo(
            bgColor = bgColor,
            textColor = textColor,
            dataType = dataType,
            acquiringMethods = acquiringMethods,
            acquiringLink = acquiringLink,
            quality = quality,
            guid = guid,
            icon = icon,
            introduction = introduction,
            effectParams = effectParams,
        )
    }
}

data class Topic(
    val tagId: Long? = null,
    val tagName: String? = null,
    val viewCount: Long? = null,
    val hot: Boolean? = null,
    val recommendStatus: Int? = null,
    val followCount: Int? = null,
    val follow: Boolean? = null
) {
    fun toPostTag(): PostTag {
        return PostTag(
            tagId = tagId ?: 0,
            tagName = tagName,
            viewCount = viewCount ?: 0,
            hot = hot == true,
        )
    }
}

