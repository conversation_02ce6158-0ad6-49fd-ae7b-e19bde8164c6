package com.socialplay.gpark.data.model.choice

/**
 * 内容类型:1游戏;2链接;4任务;8礼包;16道
 * <AUTHOR>
 * @date 2021/07/05
 */
object ChoiceContentType {
    // 游戏
    const val GAME = 1

    // web链接
    const val LINK = 2

    // 运营位
    const val OPERATION = 4096

    // 玩法模板
    const val TEMPLATE = 32768

    // 模组
    const val MODULE = 65536

    // 服装
    const val CLOTH = 131072

    private val supportCardTypes = listOf(GAME, LINK, OPERATION, TEMPLATE, MODULE, CLOTH)

    fun isSupportType(type: Int): Boolean = supportCardTypes.contains(type)
}

