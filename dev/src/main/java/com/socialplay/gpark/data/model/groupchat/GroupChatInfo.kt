package com.socialplay.gpark.data.model.groupchat

import android.os.Parcelable
import com.ly123.tes.mgs.metacloud.origin.GroupMemberInfo
import kotlinx.parcelize.Parcelize

/**
 * 群成员信息
 */
@Parcelize
data class GroupChatMemberInfo(
    /**
     * 用户头像
     */
    val avatar: String?,
    /**
     * 修改时间 单位毫秒
     */
    val modifyTime: Long?,
    /**
     * 用户昵称
     */
    val nickname: String?,
    /**
     * 成员权限 1:所有者 2:管理者 3:普通成员
     */
    val power: Int?,
    /**
     * 成员用户id
     */
    val uuid: String?,
) : Parcelable {
    companion object {
        const val POWER_OWNER = 1
        const val POWER_MANAGER = 2
        const val POWER_MEMBER = 3
    }

    fun isOwner(): <PERSON><PERSON><PERSON> {
        return power == POWER_OWNER
    }

    fun isManager(): Boolean {
        return power == POWER_OWNER || power == POWER_MANAGER
    }

    fun toGroupMemberInfo(): GroupMemberInfo{
        return GroupMemberInfo(
            avatar = avatar,
            modifyTime = modifyTime,
            nickname = nickname,
            power = power,
            uuid = uuid
        )
    }
}

data class GroupChatMemberInfoPage(
    /**
     * 群聊成员最后变更时间  类似于成员版本，如果返回的该值与传入的不同，需要重新加载成员列表
     */
    val memberLastChange: Long,
    /**
     * 下次加载的滚动id
     */
    val rollId: Int?,
    /**
     * 此次加载的成员列表
     */
    val members: List<GroupChatMemberInfo>?
)

/**
 * 群信息
 */
@Parcelize
data class GroupChatInfo(
    /**
     * 群描述
     */
    val describe: String?,
    /**
     * 群icon
     */
    val icon: String?,
    /**
     * 群聊id
     */
    val id: Long?,
    /**
     * 加入状态
     * 0 不可加入
     * 1 可直接加入
     * 2 可申请加入
     * 3 已加入
     * 4 已申请
     */
    val joinStatus: Int?,
    /**
     * 申请加群的请求状态(本地字段)
     * 1. 默认状态
     * 2. 网络请求中
     */
    val requestJoinStatus: Int?,
    /**
     * 成员数量
     */
    val memberCount: Int?,
    /**
     * 群名称
     */
    val name: String?,
) : Parcelable {
    companion object {
        /**
         * 0 不可加入
         */
        const val JOIN_STATUS_CANT_JOIN = 0

        /**
         * 1 可直接加入
         */
        const val JOIN_STATUS_CAN_JOIN = 1

        /**
         * 2 可申请加入
         */
        const val JOIN_STATUS_CAN_APPLY_JOIN = 2

        /**
         * 3 已加入
         */
        const val JOIN_STATUS_JOINED = 3

        /**
         * 4 已申请
         */
        const val JOIN_STATUS_REQUESTED = 4

        /**
         * 1 加群请求-默认状态(本地状态记录)
         */
        const val REQUEST_JOIN_STATUS_DEFAULT = 1

        /**
         * 2 加群请求-网络请求中(本地状态记录)
         */
        const val REQUEST_JOIN_STATUS_LOADING = 2
    }
}

/**
 * 群列表
 */
data class GroupChatInfoPage(
    /**
     * 查询滚动id
     */
    val rollId: String?,
    /**
     * 群聊列表
     */
    val chatGroups: List<GroupChatInfo>?
)

/**
 * 群数量
 */
data class GroupChatCount(
    /**
     * 用户创建群聊数量
     */
    val createCount: Int?,
    /**
     * 用户加入群聊数量
     */
    val joinCount: Int?
)

/**
 * 群聊信息详情
 */
@Parcelize
data class GroupChatDetailInfo(
    /**
     * 获取群详情失败错误码
     * 群解散: 1016
     * 不是群成员: 1400
     */
    val errorCode: Int? = null,
    /**
     * 群公告
     */
    val announcement: String?,
    /**
     * 创建时间
     */
    val createTime: Long?,
    /**
     * 创建者uuid
     */
    val creator: String?,
    /**
     * 群描述
     */
    val describe: String?,
    /**
     * 群icon
     */
    val icon: String?,
    /**
     * 群聊id
     */
    val id: Long?,
    /**
     * 群关联Im的id
     */
    val imId: String?,
    /**
     * 邀请入群方式
     * 1 成员都可以邀请进入
     * 2 管理员邀请才能进入
     */
    val inviteType: Int?,
    /**
     * 成员进入群方式
     * 1 任何人都可以进入
     * 2 通过申请可以进入
     */
    val joinType: Int?,
    /**
     * 成员数量
     */
    val memberCount: Int?,
    /**
     * 群组成员变更最后时间点
     */
    val memberLastChange: Long?,
    /**
     * 修改时间
     */
    val modifyTime: Long?,
    /**
     * 群名称
     */
    val name: String?,
    /**
     * 当前用户是否打开通知
     */
    val notifications: Boolean?,
    /**
     * 标识当前是否处于审核状态
     */
    val auditing: Boolean?,
    /**
     * 管理成员列表
     */
    val members: List<GroupChatMemberInfo>?,
) : Parcelable {
    companion object {
        /**
         * 管理员最大数量
         */
        const val MAX_ADMIN_COUNT = 5
        const val INVITE_TYPE_EVERYONE = 1
        const val INVITE_TYPE_MANAGER = 2
        const val JOIN_TYPE_EVERYONE = 1
        const val JOIN_TYPE_APPLY = 2

        /**
         * 群被解散时的错误码
         */
        const val ERROR_CODE_GROUP_DISBAND = 1016

        /**
         * 不是群成员时的错误码
         */
        const val ERROR_CODE_NOT_GROUP_MEMBER = 1400

        fun isJoinTypeOk(joinType: Int): Boolean {
            return joinType == JOIN_TYPE_EVERYONE || joinType == JOIN_TYPE_APPLY
        }

        fun isInviteTypeOk(inviteType: Int): Boolean {
            return inviteType == INVITE_TYPE_EVERYONE || inviteType == INVITE_TYPE_MANAGER
        }
    }

    fun isGroupOwner(userId: String): Boolean {
        return creator == userId
    }

    fun isGroupManager(userId: String): Boolean {
        if (creator == userId) {
            return true
        }
        val managerIndex = members?.indexOfFirst { memberInfo ->
            memberInfo.uuid == userId
        }
        return managerIndex != null && managerIndex >= 0
    }

    /**
     * 将原有的 GroupChatDetailInfo 精简, members 只保留 owner 和 manager
     * 方便在跨页面 Bundle 传输时, 避免数据量过大导致App闪退
     */
    fun simplifiedMembers(): GroupChatDetailInfo {
        if (members.isNullOrEmpty() || members.size < MAX_ADMIN_COUNT + 1) {
            return this
        }

        val simplifiedMembers = members.filter { memberInfo ->
            memberInfo.power == GroupChatMemberInfo.POWER_OWNER || memberInfo.power == GroupChatMemberInfo.POWER_MANAGER
        }
        return this.copy(
            members = simplifiedMembers
        )
    }

    fun updateFrom(other: GroupChatDetailInfo?): GroupChatDetailInfo {
        if (other == null){
            return this
        }
        return this.copy(
            announcement = other.announcement ?: this.announcement,
            createTime = other.createTime ?: this.createTime,
            creator = other.creator ?: this.creator,
            describe = other.describe ?: this.describe,
            icon = other.icon ?: this.icon,
            id = other.id ?: this.id,
            imId = other.imId ?: this.imId,
            inviteType = other.inviteType ?: this.inviteType,
            joinType = other.joinType ?: this.joinType,
            memberCount = other.memberCount ?: this.memberCount,
            memberLastChange = other.memberLastChange ?: this.memberLastChange,
            modifyTime = other.modifyTime ?: this.modifyTime,
            name = other.name ?: this.name,
            notifications = other.notifications ?: this.notifications,
            members = other.members ?: this.members,
        )
    }

    fun isDisband(): Boolean {
        return errorCode == ERROR_CODE_GROUP_DISBAND
    }

    fun isLeaveGroup(): Boolean {
        return errorCode == ERROR_CODE_NOT_GROUP_MEMBER
    }
}

/**
 * 群聊申请信息
 */
@Parcelize
data class GroupChatApplyInfo(
    /**
     * 申请id
     */
    val id: Long?,
    /**
     * 申请用户
     */
    val askUuid: String?,
    /**
     * 用户名称
     */
    val userName: String?,
    /**
     * 用户头像
     */
    val userAvatar: String?,
    /**
     * 群聊id
     */
    val chatGroupId: Long?,
    /**
     * 群聊名称
     */
    val chatGroupName: String?,
    /**
     * 群聊icon
     */
    val chatGroupIcon: String?,
    /**
     * 申请原因
     */
    val reason: String?,
    /**
     * 申请状态:
     * 0申请中;
     * 1拒绝;
     * 2同意
     */
    val status: Int?,
    /**
     * 申请时间 毫秒时间戳
     */
    val createTime: Long?,
) : Parcelable {
    companion object {
        const val STATUS_APPLYING = 0
        const val STATUS_REJECT = 1
        const val STATUS_AGREE = 2
    }
}

/**
 * 群聊申请信息列表
 */
data class GroupChatApplyInfoList(
    /**
     * 是否还有下一页
     */
    val hasNext: Boolean?,
    val asks: List<GroupChatApplyInfo>?,
)

/**
 * 群成员新增结果
 */
data class GroupChatAddMembers(
    /**
     * 已存在的群成员失败
     */
    val failExistMember: List<String>?,
    /**
     * 群成员达到上限失败
     */
    val failMaxGroupMember: List<String>?,
    /**
     * 对方达到加群上限失败
     */
    val failMaxJoinLimit: List<String>?,
    /**
     * 成功的成员
     */
    val success: List<String>?,
)

/**
 * 加载群聊成员
 */
data class GroupChatMembersPage(
    /**
     * 群聊成员最后变更时间  类似于成员版本，如果返回的该值与传入的不同，需要重新加载成员列表
     */
    val memberLastChange: Long?,
    /**
     * 下次加载的滚动id
     */
    val rollId: Long?,
    /**
     * 成员列表
     */
    val members: List<GroupChatMemberInfo>?
)

/**
 * 通过 imIds 获取的群信息
 */
data class GroupSimpleInfo(
    /**
     * 群聊id
     */
    val id: String?,
    /**
     * 群名称
     */
    val name: String?,
    /**
     * 群icon
     */
    val icon: String?,
    /**
     * 群描述
     */
    val describe: String?,
)


