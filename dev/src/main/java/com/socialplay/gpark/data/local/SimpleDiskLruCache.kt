package com.socialplay.gpark.data.local

import android.content.Context
import com.bumptech.glide.disklrucache.DiskLruCache
import com.socialplay.gpark.util.FileUtil
import com.socialplay.gpark.util.GsonUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.lang.reflect.Type

/**
 * create by: bin on 2021/5/16
 *
 * 简单lru 磁盘缓存
 */
@Suppress("BlockingMethodInNonBlockingContext")
class SimpleDiskLruCache(context: Context) {
    companion object {
        // 2021.5
        const val VERSION = 202105

        // 默认50m
        const val MAX_SIZE = 50 * 1024 * 1024L

        // 一个key 对应一个value
        const val VALUE_COUNT = 1
    }

    private val lruDir = File(context.filesDir, "meta_disk_lru")

    private var _cache = DiskLruCacheSafeWrapper(DiskLruCache.open(lruDir, VERSION, VALUE_COUNT, MAX_SIZE))
    val cache: DiskLruCacheSafeWrapper
        get() = _cache

    private fun putString(key: String, value: String) {
        if (FileUtil.getSDFreeMemory() <= 0) return
        checkCache()
        cache.putString(key, value)
    }

    suspend fun <T> put(key: String, t: T) {
        withContext(Dispatchers.IO) {
            if (t is String) {
                putString(key, t)
            } else {
                putString(key, GsonUtil.gson.toJson(t))
            }
        }
    }

    suspend fun getString(key: String): String {
        checkCache()
        return withContext(Dispatchers.IO) {
            cache.getString(key)
        }
    }

    suspend fun <T> get(key: String, typeOfT: Type): T? {
        checkCache()
        return withContext(Dispatchers.IO) {
            kotlin.runCatching { GsonUtil.gson.fromJson<T>(cache.getString(key), typeOfT) }.getOrNull()
        }
    }

    suspend inline fun <reified T> get(key: String): T? {
        checkCache()
        return withContext(Dispatchers.IO) {
            kotlin.runCatching { GsonUtil.gson.fromJson(cache.getString(key), T::class.java) }.getOrNull()
        }
    }

    fun delete() {
        _cache.delete()
    }

    fun reset() {
        synchronized(this) {
            if (cache.cache.isClosed) {
                _cache = DiskLruCacheSafeWrapper(DiskLruCache.open(lruDir, VERSION, VALUE_COUNT, MAX_SIZE))
            }
        }
    }

    fun checkCache() {
        if (cache.cache.isClosed) {
            synchronized(this) {
                if (cache.cache.isClosed) {
                    _cache = DiskLruCacheSafeWrapper(DiskLruCache.open(lruDir, VERSION, VALUE_COUNT, MAX_SIZE))
                }
            }
        }
    }

    class DiskLruCacheSafeWrapper(val cache: DiskLruCache) {

        fun getString(key: String): String {
            return kotlin.runCatching { cache.get(key).getString(0) }.getOrDefault("") ?: ""
        }

        fun putString(key: String, value: String) {
            kotlin.runCatching {
                val edit = cache.edit(key)
                edit.set(0, value)
                edit.commit()
            }.getOrElse {
                it.printStackTrace()
            }
        }

        fun delete() {
            kotlin.runCatching {
                cache.delete()
            }
        }

        fun size(): Long {
            return cache.size()
        }
    }
}