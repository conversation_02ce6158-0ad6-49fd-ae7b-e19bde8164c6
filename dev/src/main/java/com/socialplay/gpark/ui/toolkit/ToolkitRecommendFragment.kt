package com.socialplay.gpark.ui.toolkit

import android.content.res.Configuration
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.outfit.UgcDesignFeed
import com.socialplay.gpark.data.model.outfit.UgcDesignToolkit
import com.socialplay.gpark.databinding.FragmentToolkitRecommendBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.itemsPerRow4
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.maverick.whenSuccessEmpty
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.ui.dialog.LoadingDialogFragment
import com.socialplay.gpark.ui.gamepay.PayDialogStyle
import com.socialplay.gpark.ui.gamepay.PayResult
import com.socialplay.gpark.ui.gamepay.PayScene
import com.socialplay.gpark.ui.outfit.BuyUgcDesignState
import com.socialplay.gpark.ui.outfit.BuyUgcDesignViewModel
import com.socialplay.gpark.ui.outfit.UgcDesignDetailFragment
import com.socialplay.gpark.util.PadAdapterUtil
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.attach
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setFragmentResultListenerByHostFragment
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible

class ToolkitRecommendFragment :
    BaseRecyclerViewFragment<FragmentToolkitRecommendBinding>(R.layout.fragment_toolkit_recommend) {
    private val viewModel: UgcDesignToolkitViewModel by fragmentViewModel()
    private val buyViewModel: BuyUgcDesignViewModel by fragmentViewModel()
    private val args by navArgs<ToolkitRecommendFragmentArgs>()
    private var loadingDialog: LoadingDialogFragment? = null
    private var isFirstLoad = true
    private var shouldRefreshBalance = false

    /**
     * 注意: totalSpanCount 一定要为偶数, 否者界面展示不太协调
     */
    private val totalSpanCount: Int
        get() {
            val count = itemsPerRow4
            return if (count % 2 == 0) {
                count
            } else {
                count + 1
            }
        }
    private var visibilityTracker: EpoxyVisibilityTracker? = null
    private val ugcClickedListener = object : IUgcItemClickedListener {
        override fun onBuyClicked(item: UgcDesignToolkit) {
            val needPurchase = item.needPurchase
            if (needPurchase) {
                loadingDialog?.dismissAllowingStateLoss()
                loadingDialog = MetaRouter.Dialog.loading(childFragmentManager)
                buyViewModel.buy(
                    lifecycleOwner = viewLifecycleOwner,
                    commodityId = item.commodityId,
                    onStartPay = { payInfo ->
                        loadingDialog?.dismissAllowingStateLoss()
                        loadingDialog = null
                        payInfo.copy(
                            productCode = item.commodityId ?: "",
                            productName = item.title ?: "",
                            productCount = 1,
                            sceneCode = PayScene.MODULE.value,
                            source = "detail",
                            metrialidid = item.guid,
                            payDialogStyle = PayDialogStyle.BOTTOM,
                            productImageUrl = item.cover ?: "",
                            productSoldCount = item.purchaseCount ?: 0L,
                            creatorName = item.userName ?: "",
                            creatorAvatarUrl = item.userIcon ?: "",
                        )
                    },
                    onPayResult = { payResult ->
                        loadingDialog?.dismissAllowingStateLoss()
                        loadingDialog = null
                        trackGetToolkitClick(item, payResult.isSuccess)
                        if (payResult.isSuccess) {
                            toast(R.string.assets_obtain_success)
                            // 支付成功,处理支付成功逻辑,更新Item展示状态
                            viewModel.buyUgcDesignSuccess(args.gameId ?: "", item)
                            if (payResult.coinsBalance != null && payResult.coinsBalance >= 0) {
                                viewModel.updateBalance(payResult.coinsBalance)
                            } else {
                                viewModel.loadBalance()
                            }
                        } else if (payResult.resultCode == PayResult.RESULT_CODE_FAILED) {
                            // 支付失败,处理支付失败逻辑
                            toast(payResult.failedReason ?: getString(R.string.common_failed))
                        } else {
                            // 用户取消
                        }
                    }
                )
            } else {
                // 无需购买, 直接获取
                val feedId = item.feedId ?: return
                buyViewModel.get(
                    viewLifecycleOwner,
                    feedId,
                    onGetResult = { getResult ->
                        if (getResult is Success) {
                            trackGetToolkitClick(item, true)
                            // 获取成功,处理支付成功逻辑,更新Item展示状态
                            viewModel.buyUgcDesignSuccess(args.gameId ?: "", item)
                            toast(R.string.assets_obtain_success)
                        } else if (getResult is Fail) {
                            trackGetToolkitClick(item, false)
                            toast(getResult.invoke())
                        }
                    })
            }
        }

        override fun onClicked(item: UgcDesignToolkit) {
            item.feedId ?: return
            shouldRefreshBalance = true
            MetaRouter.UgcDesign.detail(
                this@ToolkitRecommendFragment,
                item.feedId,
                // 这个参数用于埋点
                CategoryId.ASSETS_TOOLKIT_DETAIL.toInt(),
                needResult = true,
            )
        }

        override fun onItemVisible(item: UgcDesignToolkit) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_SHOW,
                "metrialidid" to item.guid.orEmpty(),
                "show_categoryid" to CategoryId.ASSETS_TOOLKIT_DETAIL,
                "authorid" to item.uuid.orEmpty(),
                "type" to (if (item.isCloth) {
                    "0"
                } else if (item.isMod) {
                    "1"
                } else {
                    ""
                }),
                "is_recreate" to (if (item.editable == true) "yes" else "no"),
                "is_price" to (if (item.isPriced == true) "yes" else "no")
            )
        }
    }

    private val recommendClickedListener = object : IItemClickedListener {
        override fun onClicked(item: UgcDesignFeed) {
            shouldRefreshBalance = true
            MetaRouter.UgcDesign.detail(
                this@ToolkitRecommendFragment,
                item.feedId,
                // 这个参数用于埋点
                CategoryId.ASSETS_TOOLKIT_DETAIL_RECOMMEND.toInt(),
                needResult = true,
            )
        }

        override fun onItemVisible(item: UgcDesignFeed) {
            Analytics.track(
                EventConstants.LIBRARY_ITEM_SHOW,
                "metrialidid" to item.guid.orEmpty(),
                "show_categoryid" to CategoryId.ASSETS_TOOLKIT_DETAIL_RECOMMEND,
                "authorid" to item.uuid.orEmpty(),
                "type" to (if (item.isCloth) {
                    "0"
                } else if (item.isMod) {
                    "1"
                } else {
                    ""
                }),
                "is_recreate" to (if (item.editable == true) "yes" else "no"),
                "is_price" to (if (item.isPriced == true) "yes" else "no")
            )
        }
    }

    private fun trackGetToolkitClick(item: UgcDesignToolkit, isSuccess: Boolean) {
        Analytics.track(
            EventConstants.LIBRARY_METARIAL_GET_CLICK,
            "metrialidid" to item.guid.orEmpty(),
            "show_categoryid" to CategoryId.ASSETS_TOOLKIT_DETAIL,
            "authorid" to item.uuid.orEmpty(),
            "result" to (if (isSuccess) "0" else "1"),
            "type" to (if (item.isCloth) {
                "0"
            } else if (item.isMod) {
                "1"
            } else {
                ""
            }),
            "is_price" to (if (item.isPriced == true) "yes" else "no"),
        )
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentToolkitRecommendBinding? {
        return FragmentToolkitRecommendBinding.inflate(inflater, container, false)
    }

    override val recyclerView: EpoxyRecyclerView get() = binding.rv

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Analytics.track(EventConstants.ASSETS_SECONDARY_PAGE_SHOW)
        initView()
        initData()
    }

    override fun onResume() {
        super.onResume()
        visibilityTracker?.clearVisibilityStates()
        visibilityTracker?.requestVisibilityCheck()
        if (shouldRefreshBalance) {
            shouldRefreshBalance = false
            viewModel.loadBalance()
        }
    }

    private fun initView() {
        binding.ivBalanceAdd.visible(!args.isMe)
        binding.titleBar.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        binding.layoutBalance.setOnAntiViolenceClickListener {
            shouldRefreshBalance = true
            Analytics.track(EventConstants.WALLET_ICON_CLICK)
            MetaRouter.Pay.goBuyCoinsPage(
                requireContext(),
                this,
                "detail",
            )
        }
        binding.loadingView.setRetry {
            viewModel.initData(
                isRefresh = true,
                gameId = args.gameId ?: "",
                needsFillUsed = false,
                loadRecommend = !args.isMe,
                loadBalance = true
            )
        }
        binding.refreshLayout.setOnRefreshListener {
            viewModel.initData(
                isRefresh = true,
                gameId = args.gameId ?: "",
                needsFillUsed = false,
                loadRecommend = !args.isMe,
                loadBalance = true
            )
        }
        binding.rv.layoutManager = GridLayoutManager(requireContext(), totalSpanCount)
        binding.rv.addItemDecoration(object : RecyclerView.ItemDecoration(){
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                val lp = view.layoutParams as GridLayoutManager.LayoutParams
                val spanSize = lp.spanSize
                val column = lp.spanIndex
                // 调整item之间的间距
                when (spanSize) {
                    1 -> {
                        // 占1列的 item: 工具包item
                        outRect.left = 0
                        outRect.right = 0
                    }

                    2 -> {
                        // 占2列的 item: 资源推荐item
                        outRect.left = 0
                        outRect.right = 0
                        when (column) {
                            0 -> {
                                // 第一列
                                outRect.right = dp(2)
                            }

                            totalSpanCount - 2 -> {
                                // 最后一列
                                outRect.left = dp(2)
                            }

                            else -> {
                                outRect.left = dp(2)
                                outRect.right = dp(2)
                            }
                        }
                    }

                    totalSpanCount -> {
                        // 占满列的 item: 分割线
                        outRect.left = dp(4)
                        outRect.right = dp(4)
                    }
                }
                outRect.top = 0
                outRect.bottom = 0
            }
        })
        visibilityTracker = EpoxyVisibilityTracker().attach(viewLifecycleOwner, binding.rv)
        // Item 有 50% 的部分显示出来时
        visibilityTracker?.partialImpressionThresholdPercentage = 50
    }

    private fun initData() {
        viewModel.onEach(ToolkitRecommendState::coinsBalance) { coinsBalance ->
            if (coinsBalance == null) {
                binding.tvBalance.text = "---"
            } else {
                binding.tvBalance.text = UnitUtilWrapper.formatCoinCont(coinsBalance)
            }
        }

        viewModel.onEach(
            ToolkitRecommendState::usedToolkitList,
            ToolkitRecommendState::recommendAssets,
        ) { usedToolkitList, recommendAssets ->
            val async = usedToolkitList.map { it.second }
                .whenSuccessEmpty { recommendAssets }

            updateRefreshLoadingByAsync(
                async = async,
                binding.loadingView,
                binding.refreshLayout,
            )
        }

        viewModel.initData(
            isRefresh = isFirstLoad,
            gameId = args.gameId ?: "",
            needsFillUsed = false,
            loadRecommend = !args.isMe,
            loadBalance = true,
        )
        isFirstLoad = false

        buyViewModel.registerAsyncErrorToast(BuyUgcDesignState::getResult)

        setFragmentResultListenerByHostFragment(
            UgcDesignDetailFragment.TAG,
            viewLifecycleOwner
        ) { _, bundle ->
            viewModel.updateItem(UgcDesignDetailFragment.getResult(bundle))
        }
    }

    override fun epoxyController() = simpleController(
        viewModel,
        ToolkitRecommendState::uniqueUsedTag,
        ToolkitRecommendState::usedToolkitList,
        ToolkitRecommendState::loadUsedMore,
        ToolkitRecommendState::recommendAssets,
        ToolkitRecommendState::loadRecommendMore,
    ) { uniqueUsedTag, usedToolkitListAsync, loadUsedMore, recommendToolkitsAsync, loadRecommendMore ->
        spacer(
            height = dp(16),
            spanCount = totalSpanCount,
        )
        val usedToolkitsPair = usedToolkitListAsync.invoke() ?: return@simpleController
        var existsUsedToolkits = false
        if (args.gameId == usedToolkitsPair.first) {
            val usedToolkits = usedToolkitsPair.second
            if (usedToolkits.isNotEmpty()) {
                existsUsedToolkits = true
                usedToolkits.forEach { toolkit ->
                    add {
                        ToolkitUsedItem(
                            glide = glide,
                            isMainState = args.isMe,
                            currentUuid = viewModel.currentAccountUuid(),
                            item = toolkit,
                            listener = ugcClickedListener
                        ).id(
                            "ToolkitUsedItem-${uniqueUsedTag}-${toolkit.inMyModLibrary}-${toolkit.isPurchased}-${toolkit.guid}-${toolkit.feedId}-${toolkit.title}-${toolkit.price}"
                        ).apply {
                            spanSizeOverride { totalSpanCount, _, _ ->
                                1
                            }
                        }
                    }
                }

                loadMoreFooter(
                    loadUsedMore,
                    idStr = "ToolkitUsedItem-Footer-${uniqueUsedTag}",
                    spanSize = totalSpanCount,
                    // 主态的时候, 不显示推荐的资源, 所以需要显示"暂无更多"
                    showEnd = args.isMe,
                    staggerFullSpan = true
                ) {
                    viewModel.loadGameUsedUgcDesign(
                        isRefresh = false,
                        gameId = args.gameId ?: "",
                        needsFillUsed = false,
                        loadRecommend = !args.isMe,
                    )
                }
            }
        }
        val recommendToolkits = recommendToolkitsAsync.invoke()
        if (!recommendToolkits.isNullOrEmpty()) {
            if (existsUsedToolkits) {
                add {
                    ToolkitDividerItem().id(
                        "ToolkitDividerItem"
                    ).apply {
                        spanSizeOverride { totalSpanCount, _, _ ->
                            totalSpanCount
                        }
                    }
                }
            }
            recommendToolkits.forEach { toolkit ->
                add {
                    ToolkitRecommendItem(
                        glide = glide,
                        item = toolkit,
                        listener = recommendClickedListener
                    ).id(
                        "ToolkitRecommendItem-$totalSpanCount-${toolkit.guid}-${toolkit.feedId}-${toolkit.title}-${toolkit.price}-${toolkit.favorites}"
                    ).apply {
                        spanSizeOverride { totalSpanCount, _, _ ->
                            2
                        }
                    }
                }
            }

            loadMoreFooter(
                loadRecommendMore,
                idStr = "ToolkitRecommendFragment-RecommendFooter",
                spanSize = totalSpanCount,
                showEnd = true,
                staggerFullSpan = true
            ) {
                viewModel.loadRecommendUgcDesign(false)
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (isBindingAvailable()) {
            PadAdapterUtil.configChangedByGrid(recyclerView, totalSpanCount, epoxyController)
        }
    }

    override fun onDestroyView() {
        visibilityTracker = null
        loadingDialog = null
        super.onDestroyView()
    }

    override fun getPageName() = PageNameConstants.FRAGMENT_TOOLKIT_RECOMMEND
}