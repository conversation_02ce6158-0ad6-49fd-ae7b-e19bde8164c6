package com.socialplay.gpark.ui.recommend.choice

import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceContentType
import com.socialplay.gpark.databinding.AdapterChoiceCardProviderBannerBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.editorschoice.adapter.BannerItemAdapter
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setFullSpan
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setMargin
import com.youth.banner.listener.OnPageChangeListener
import com.zhpan.indicator.enums.IndicatorSlideMode
import com.zhpan.indicator.enums.IndicatorStyle

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/01
 *     desc   :
 * </pre>
 */
fun MetaModelCollector.choiceBannerCard(
    owner: LifecycleOwner,
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    add(ChoiceTitleMoreItem(card, spanSize, listener, needMore = false).id("ChoiceBannerCard-choiceBannerCard-$cardPosition-${card.cardName.hashCode()}").spanSizeOverride { totalSpanCount, _, _ -> spanSize.coerceAtMost(totalSpanCount) })
    add(
        BannerCardItem(
            owner,
            card,
            cardPosition,
            spanSize,
            listener
        ).spanSizeOverride { totalSpanCount, _, _ ->
            spanSize.coerceAtMost(totalSpanCount)
        }.id("ChoiceBannerCard-$cardPosition-${card.cardName.hashCode()}")
    )
}

data class BannerCardItem(
    val owner: LifecycleOwner,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardProviderBannerBinding>(
    R.layout.adapter_choice_card_provider_banner,
    AdapterChoiceCardProviderBannerBinding::bind
) {

    override fun AdapterChoiceCardProviderBannerBinding.onBind() {

        val games = card.gameListNoNull
        val urls = games?.map {
            if (it.type == ChoiceContentType.OPERATION) {
                it.operatingPosition?.iconUrl
            } else {
                it.imageUrl
            }.orEmpty()
        }
        val size = games?.size ?: 0
        if (size == 1) {
            games?.firstOrNull()?.let {
                listener.onItemShow(cardPosition, card, 0, it, true)
            }
        }
        if (card.bannerPosition !in 1..size) {
            card.bannerPosition = 1
        }
        if (card.bannerPosition in 1..size) {
            banner.startPosition = card.bannerPosition
        }
        val singleBanner = size <= 1
        // 指示器
        indicator.isVisible = !singleBanner
        indicator.apply {
            val dp3 = dp(3).toFloat()
            val dp5 = dp(5).toFloat()
            val dp8 = dp(8).toFloat()
            setIndicatorStyle(IndicatorStyle.ROUND_RECT)
            setSliderWidth(dp3, dp8)
            setSliderHeight(dp3)
            setSlideMode(IndicatorSlideMode.NORMAL)
            setSliderGap(dp3)
            setPageSize(size)
            notifyDataChanged()
        }
        indicator.setCurrentPosition(card.bannerPosition - 1)
//        banner.setHeight(((screenWidth - dp(32)) / 343.0f * 142).toInt())
        val adapter = BannerItemAdapter(
            urls,
            true,
            marginLeft = dp(26),
            marginRight = dp(26),
            listener.getGlideOrNull()
        )
        banner.registerOnPageCallback()
            .addBannerResumeLifecycleObserver(owner)
            .setAdapter(adapter)
            .isAutoLoop(size > 1)
            .setLoopTime(5_000L)
            .setOnBannerListener { _, position ->
                games?.getOrNull(position)?.let {
                    listener.onItemClick(
                        cardPosition,
                        card,
                        position,
                        it,
                        true
                    )
                }
            }
            .addOnPageChangeListener(object : OnPageChangeListener {
                override fun onPageScrolled(
                    position: Int,
                    positionOffset: Float,
                    positionOffsetPixels: Int
                ) {
                    indicator.onPageScrolled(position, positionOffset, positionOffsetPixels)
                }

                override fun onPageSelected(position: Int) {
                    card.bannerPosition = position + 1
                    games?.getOrNull(position)?.let {
                        listener.onItemShow(cardPosition, card, position, it, true)
                    }
                    indicator.onPageSelected(position)
                }

                override fun onPageScrollStateChanged(state: Int) {
                    indicator.onPageScrollStateChanged(state)
                }
            })
        banner.setMargin(left = -(10.dp), right = -(10.dp))
        root.post {
            root.setMargin(left = -(10.dp), right = -(10.dp))
        }
    }

    override fun AdapterChoiceCardProviderBannerBinding.onUnbind() {
        banner.unregisterOnPageCallback()
            .removeBannerResumeLifecycleObserver()
            .setOnBannerListener(null)
            .addOnPageChangeListener(null)
            .stop()
    }
}