package com.socialplay.gpark.ui.im.conversation

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.ly123.tes.mgs.metacloud.IChatRoomSystemListener
import com.ly123.tes.mgs.metacloud.MetaCloud
import com.socialplay.gpark.data.base.DataResult
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import kotlinx.coroutines.launch
import org.json.JSONObject
import org.koin.android.ext.android.get
import timber.log.Timber

data class GroupChatProfileModelState(
    val groupId: String? = null,
    val isAuditing: Boolean = false,
    val groupDetail: Async<GroupChatDetailInfo> = Uninitialized,
    val leaveGroupResult: Async<DataResult<Boolean>> = Uninitialized,
    val disbandGroupResult: Async<DataResult<Boolean>> = Uninitialized,
    val editGroupNotificationResult: Async<DataResult<Boolean>> = Uninitialized,
) : MavericksState

class GroupChatProfileViewModel(
    initialState: GroupChatProfileModelState,
    val metaRepository: com.socialplay.gpark.data.IMetaRepository,
    val accountInteractor: AccountInteractor,
) : BaseViewModel<GroupChatProfileModelState>(initialState) {

    val groupDetail: GroupChatDetailInfo? get() = oldState.groupDetail.invoke()

    private val chatRoomSystemListener = object : IChatRoomSystemListener {
        override fun onReceiveRESTCustomData(groupID: String?, customData: String) {
            if(groupID == groupDetail?.imId) {
                onReceiveRoomSystemMessage(customData)
            }
        }
    }

    init {
        MetaCloud.registerChatRoomSystemListener(chatRoomSystemListener)
    }

    override fun onCleared() {
        MetaCloud.unRegisterChatRoomSystemListener(chatRoomSystemListener)
        super.onCleared()
    }

    fun updateGroupId(groupId: String) {
        setState {
            copy(groupId = groupId)
        }
    }

    private fun onReceiveRoomSystemMessage(customData: String) {
        try {
            val data = JSONObject(customData)
            val type = data.optString("type")
            val content = data.optJSONObject("content")
            val status = content?.optJSONObject("contentMsg")?.optInt("status")
            if(type != "infoAudit" || status == null) return

            when(status) {
                INFO_AUDIT_STATUS_APPLYING -> {
                    setState {
                        copy(isAuditing = true)
                    }
                }
                INFO_AUDIT_STATUS_REJECT -> {
                    setState {
                        copy(isAuditing = false)
                    }
                }
                INFO_AUDIT_STATUS_AGREE -> {
                    updateAuditInfo(oldState.groupId ?: return)
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e)
        }
    }

    fun isGroupOwner(): Boolean? {
        return oldState.groupDetail.invoke()?.isGroupOwner(accountInteractor.curUuid)
    }

    /**
     * 是否为群管理员
     */
    fun isGroupManager(): Boolean? {
        return oldState.groupDetail.invoke()?.isGroupManager(accountInteractor.curUuid)
    }

    private fun updateAuditInfo(groupId: String) = viewModelScope.launch {
        if (oldState.groupDetail is Loading) return@launch
        val groupIdLong = runCatching { groupId.toLong() }.getOrNull() ?: return@launch

        metaRepository.getGroupChatDetailInfo(groupIdLong).collect { result ->
            if (result.succeeded && result.data != null) {
                setState {
                    val data = result.data!!
                    copy(
                        groupDetail = Success(data),
                        isAuditing = data.auditing ?: false
                    )
                }
            } else {
                setState {
                    copy(
                        groupDetail = Fail(
                            result.exception ?: Exception("unknown exception")
                        )
                    )
                }
            }
        }
    }

    fun getGroupChatDetailInfo(groupId: String) {
        if (oldState.groupDetail is Loading) {
            return
        }
        val groupIdLong = runCatching {
            groupId.toLong()
        }.getOrNull() ?: return
        setState {
            copy(groupDetail = Loading())
        }
        viewModelScope.launch {
            metaRepository.getGroupChatDetailInfo(groupIdLong).collect { result ->
                if (result.succeeded && result.data != null) {
                    setState {
                        val data = result.data!!
                        copy(
                            groupDetail = Success(data),
                            isAuditing = data.auditing ?: false
                        )
                    }
                } else {
                    setState {
                        copy(
                            groupDetail = Fail(
                                result.exception ?: Exception("unknown exception")
                            )
                        )
                    }
                }
            }
        }
    }

    fun leaveGroupChat(groupId: String) {
        if (oldState.leaveGroupResult is Loading) {
            return
        }
        val groupIdLong = runCatching {
            groupId.toLong()
        }.getOrNull() ?: return
        setState {
            copy(leaveGroupResult = Loading())
        }
        viewModelScope.launch {
            val result = metaRepository.leaveGroupChat(groupIdLong)
            setState {
                copy(leaveGroupResult = Success(result))
            }
        }
    }

    fun disbandGroupChat(groupId: String) {
        if (oldState.disbandGroupResult is Loading) {
            return
        }
        val groupIdLong = runCatching {
            groupId.toLong()
        }.getOrNull() ?: return
        setState {
            copy(disbandGroupResult = Loading())
        }
        viewModelScope.launch {
            val result = metaRepository.disbandGroupChat(groupIdLong)
            setState {
                copy(disbandGroupResult = Success(result))
            }
        }
    }

    fun editGroupChatNotification(groupId: String, enableNotification: Boolean) {
        if (oldState.editGroupNotificationResult is Loading) {
            return
        }
        val groupIdLong = runCatching {
            groupId.toLong()
        }.getOrNull() ?: return
        setState {
            copy(editGroupNotificationResult = Loading())
        }
        viewModelScope.launch {
            val result = metaRepository.editGroupChatNotification(groupIdLong, enableNotification)
            val currentGroupDetail = oldState.groupDetail.invoke()
            if (currentGroupDetail == null) {
                setState {
                    copy(
                        editGroupNotificationResult = Success(result)
                    )
                }
            } else {
                setState {
                    copy(
                        groupDetail = Success(currentGroupDetail.copy(notifications = enableNotification)),
                        editGroupNotificationResult = Success(result)
                    )
                }
            }
        }
    }

    fun updateAuditing(auditing: Boolean) {
        setState {
            copy(isAuditing = auditing)
        }
    }

    fun updateGroupChatDetail(groupDetail: GroupChatDetailInfo) {
        val oldGroupDetail = oldState.groupDetail.invoke()
        if (oldGroupDetail == null) {
            setState {
                copy(groupDetail = Success(groupDetail))
            }
        } else {
            setState {
                copy(groupDetail = Success(oldGroupDetail.updateFrom(groupDetail)))
            }
        }
    }

    companion object :
        KoinViewModelFactory<GroupChatProfileViewModel, GroupChatProfileModelState>() {
        private const val TAG = "GroupChatProfileViewModel"
        private const val INFO_AUDIT_STATUS_APPLYING = 0 // 审核中
        private const val INFO_AUDIT_STATUS_REJECT = 1 // 审核未通过
        private const val INFO_AUDIT_STATUS_AGREE = 2 // 审核通过

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: GroupChatProfileModelState
        ): GroupChatProfileViewModel {
            return GroupChatProfileViewModel(state, get(), get())
        }
    }
}