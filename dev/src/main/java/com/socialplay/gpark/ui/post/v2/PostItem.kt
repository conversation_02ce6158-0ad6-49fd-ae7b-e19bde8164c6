package com.socialplay.gpark.ui.post.v2

import android.view.View
import android.view.ViewGroup
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostMomentCard
import com.socialplay.gpark.data.model.post.PostStyleCard
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PostUgcDesignCard
import com.socialplay.gpark.databinding.AdapterPostMediaBinding
import com.socialplay.gpark.databinding.AdapterPublishNewPostTagBinding
import com.socialplay.gpark.databinding.AdapterPublishPostGameBinding
import com.socialplay.gpark.databinding.AdapterPublishPostMediaBinding
import com.socialplay.gpark.databinding.AdapterPublishPostTagBinding
import com.socialplay.gpark.databinding.ItemCommunityAssetsCardFullWidthBinding
import com.socialplay.gpark.ui.core.IBaseEpoxyItemListener
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.editor.detail.comment.IUgcCommentListener
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getString
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setHeight
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.unsetOnClickAndClickable
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.ifNullOrEmpty

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/28
 *     desc   :
 * </pre>
 */
interface IPostListener : IBaseEpoxyItemListener {
    fun openMedia(media: PostMediaResource, position: Int) {}
    fun openGame(game: PostCardInfo) {}
    fun goMoment(moment: PostMomentCard) {}
    fun goOutfit(outfit: PostStyleCard) {}
    fun goUgcDesign(ugcDesign: PostUgcDesignCard) {}
    fun clickTag(tag: PostTag) {}
    fun deleteTag(tag: PostTag) {}
    fun delMedia(media: PostMediaResource) {}
    fun delGame(game: PostCardInfo) {}
    fun onAssetsCardVisible(card: Any) {}
}

interface IPublishPostListener : IPostListener {
    fun openAlbum()
}

interface IPostDetailListener : IUgcCommentListener, IPostListener

fun MetaModelCollector.publishPostMediaItem(
    item: PostMediaResource,
    position: Int,
    listener: IPostListener
) {
    add {
        PublishPostMediaItem(item, position, listener).apply {
            id("PublishPostMediaItem-${item.itemId}")
        }
    }
}

data class PublishPostMediaItem(
    val item: PostMediaResource,
    val position: Int,
    val listener: IPostListener
) : ViewBindingItemModel<AdapterPublishPostMediaBinding>(
    R.layout.adapter_publish_post_media,
    AdapterPublishPostMediaBinding::bind
) {
    override fun AdapterPublishPostMediaBinding.onBind() {
        val url = if (item.isImage) {
            ivPlay.gone()
            item.thumbnail
        } else if (item.isVideo) {
            ivPlay.visible()
            ivPlay.setImageResource(R.drawable.ic_post_play)
            item.thumbnail
        } else {
            ivPlay.gone()
            ""
        }
        ivDelBtn.visible()
        listener.getGlideOrNull()?.run {
            load(url).placeholder(R.drawable.bg_f6f6f6_round_6)
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(ivCover)
        }
        root.setOnAntiViolenceClickListener { listener.openMedia(item, position) }
        ivDelBtn.setOnAntiViolenceClickListener { listener.delMedia(item) }
    }

    override fun AdapterPublishPostMediaBinding.onUnbind() {
        root.unsetOnClick()
        ivDelBtn.unsetOnClick()
    }
}

fun MetaModelCollector.publishPostMediaAddItem(
    listener: IPublishPostListener
) {
    add {
        PublishPostMediaAddItem(listener).apply {
            id("PublishPostMediaAddItem-$it")
        }
    }
}

data class PublishPostMediaAddItem(
    val listener: IPublishPostListener
) : ViewBindingItemModel<AdapterPublishPostMediaBinding>(
    R.layout.adapter_publish_post_media,
    AdapterPublishPostMediaBinding::bind
) {
    override fun AdapterPublishPostMediaBinding.onBind() {
        ivCover.setImageResource(R.color.color_F6F6F6)
        ivPlay.visible()
        ivPlay.setImageResource(R.drawable.ic_post_add)
        ivDelBtn.gone()
        root.setOnAntiViolenceClickListener { listener.openAlbum() }
    }

    override fun AdapterPublishPostMediaBinding.onUnbind() {
        root.unsetOnClick()
    }
}

fun MetaModelCollector.postMediaItem(
    item: PostMediaResource,
    position: Int,
    totalCount: Int,
    listener: IPostListener
) {
    add {
        PostMediaItem(
            item,
            position,
            totalCount,
            listener
        ).id("PostMediaItem-$it")
    }
}

data class PostMediaItem(
    val item: PostMediaResource,
    val position: Int,
    val totalCount: Int,
    val listener: IPostListener
) : ViewBindingItemModel<AdapterPostMediaBinding>(
    R.layout.adapter_post_media,
    AdapterPostMediaBinding::bind
) {
    override fun AdapterPostMediaBinding.onBind() {
        val target = if (totalCount == 1) {
            val itemWidth = ScreenUtil.screenWidth - 32.dp
            ivAuto.visible()
            ivFixed.gone()
            ivAuto.setHeight(
                if (item.resourceWidth > 0 && item.resourceHeight > 0) {
                    ((itemWidth) * (item.resourceHeight / item.resourceWidth.toFloat())).toInt()
                } else { // 不知道宽或高的情况下自适应
                    ViewGroup.LayoutParams.WRAP_CONTENT
                }
            )
            ivAuto.maxHeight = when {
                item.isVideo -> (itemWidth * 1.3F).toInt()
                item.isHorizontal -> 350.dp
                else -> 442.dp
            }
            ivAuto
        } else {
            ivAuto.gone()
            ivFixed.visible()
            ivFixed
        }
        listener.getGlideOrNull()?.run {
            load(item.thumbnail).placeholder(R.drawable.placeholder_corner_6)
                .into(target)
        }
        ivPlay.visible(item.isVideo)
        root.setOnAntiViolenceClickListener { listener.openMedia(item, position) }
    }

    override fun AdapterPostMediaBinding.onUnbind() {
        root.unsetOnClick()
    }

    override fun getSpanSize(totalSpanCount: Int, position: Int, itemCount: Int): Int {
        return when (itemCount) {
            1 -> 6
            2, 4 -> 3
            else -> 2
        }
    }
}

fun MetaModelCollector.postGameItem(
    item: PostCardInfo,
    margin: Int,
    enableClick: Boolean,
    listener: IPostListener
) {
    add {
        PostGameItem(
            item,
            margin,
            enableClick,
            listener
        ).id("PostGameItem-${item.resourceType}-${item.gameId}")
    }
}

data class PostGameItem(
    val item: PostCardInfo,
    val margin: Int,
    val enableClick: Boolean,
    val listener: IPostListener
) : ViewBindingItemModel<AdapterPublishPostGameBinding>(
    R.layout.adapter_publish_post_game,
    AdapterPublishPostGameBinding::bind
) {
    override fun AdapterPublishPostGameBinding.onBind() {
        root.setMargin(top = margin)
        listener.getGlideOrNull()?.run {
            load(item.gameIcon).error(R.drawable.placeholder)
                .into(includeGameInfo.ivIcon)
        }
        includeGameInfo.tvGameTitle.text = item.gameName
        includeGameInfo.tvGameAuthor.setTextWithArgs(
            R.string.creator_cap_with_param,
            item.gameAuthor
        )
        when (item.resourceType) {
            PostCardInfo.TYPE_PGC -> {
                includeGameInfo.tvGamePeople.gone()
                includeGameInfo.tvGameLike.visible()
                includeGameInfo.tvGameLike.text = item.likeCountStr
                includeGameInfo.tvGameScore.gone()
            }

            PostCardInfo.TYPE_UGC -> {
                includeGameInfo.tvGamePeople.visible()
                includeGameInfo.tvGamePeople.text = UnitUtil.formatPlayerCount(item.player ?: 0)
                includeGameInfo.tvGameLike.gone()
                includeGameInfo.tvGameScore.gone()
            }

            else -> {
                includeGameInfo.tvGamePeople.gone()
                includeGameInfo.tvGameLike.gone()
                includeGameInfo.tvGameScore.gone()
            }
        }
        tvView.visible(enableClick)
        ivDelBtn.visible(!enableClick)
        if (enableClick) {
            root.setOnAntiViolenceClickListener { listener.openGame(item) }
            ivDelBtn.unsetOnClick()
        } else {
            root.unsetOnClickAndClickable()
            ivDelBtn.setOnClickListener { listener.delGame(item) }
        }
    }

    override fun AdapterPublishPostGameBinding.onUnbind() {
        root.unsetOnClickAndClickable()
        ivDelBtn.unsetOnClick()
    }
}

fun MetaModelCollector.postAssetsItem(
    /**
     * item 的类型可能有以下类型:
     * 游戏卡片: PostCardInfo
     * 拍剧卡片: PostMomentCard
     * 穿搭卡片: PostStyleCard
     * 服装卡片: PostUgcDesignCard
     */
    card: Any,
    nickname: String?,
    margin: Int,
    listener: IPostListener
) {
    val id = if (card is PostCardInfo) {
        "${card.resourceType}-${card.gameId}"
    } else if (card is PostMomentCard) {
        "${card.templateId}-${card.gameId}"
    } else if (card is PostStyleCard) {
        "${card.roleId}-${card.resourceType}-${card.resourceValue}"
    } else if (card is PostUgcDesignCard) {
        "${card.itemId}-${card.resourceType}-${card.resourceValue}"
    } else {
        return
    }
    add {
        PostAssetsItem(
            card,
            nickname,
            margin,
            listener
        ).id("PostAssetsItem-$id")
    }
}

data class PostAssetsItem(
    /**
     * item 的类型可能有以下类型:
     * 游戏卡片: PostCardInfo
     * 拍剧卡片: PostMomentCard
     * 穿搭卡片: PostStyleCard
     * 服装卡片: PostUgcDesignCard
     */
    val card: Any,
    val nickname: String?,
    val margin: Int,
    val listener: IPostListener
) : ViewBindingItemModel<ItemCommunityAssetsCardFullWidthBinding>(
    R.layout.item_community_assets_card_full_width,
    ItemCommunityAssetsCardFullWidthBinding::bind
) {
    override fun ItemCommunityAssetsCardFullWidthBinding.onBind() {
        root.setMargin(top = margin)
        ivDelBtn.gone()

        val defaultBgColor = getColorByRes(R.color.color_97BCDE)
        if (card is PostCardInfo) {
            // 游戏卡片
            layoutMetaLike.visible()
            mlvLike.visible()
            mlvPlayers.visible()
            mlvTryOn.gone()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            listener.getGlideOrNull()?.apply {
                load(card.gameIcon).centerCrop()
                    .placeholder(R.drawable.placeholder_corner_8)
                    .into(ivIcon)
            }
            tvTitle.text = card.gameName
            mlvLike.setLikeText(UnitUtil.formatKMCount(card.likeCount ?: 0))
            mlvPlayers.setLikeText(UnitUtil.formatKMCount(card.player ?: 0))
            bgCard.setOnAntiViolenceClickListener {
                listener.openGame(card)
            }
        } else if (card is PostMomentCard) {
            // 拍剧卡片
            layoutMetaLike.gone()
            mlvLike.gone()
            mlvPlayers.gone()
            mlvTryOn.gone()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            listener.getGlideOrNull()?.apply {
                load(card.materialUrl).centerCrop()
                    .placeholder(R.drawable.placeholder_corner_8)
                    .into(ivIcon)
            }
            tvTitle.text = card.templateName
            bgCard.setOnAntiViolenceClickListener {
                listener.goMoment(card)
            }
        } else if (card is PostStyleCard) {
            // 穿搭卡片
            layoutMetaLike.visible()
            mlvLike.gone()
            mlvPlayers.gone()
            mlvTryOn.visible()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            listener.getGlideOrNull()?.apply {
                load(card.wholeBodyImage).centerCrop()
                    .placeholder(R.drawable.avatar_friend_placeholder)
                    .into(ivIcon)
            }
            tvTitle.setTextWithArgs(
                R.string.s_outfit,
                nickname.orEmpty()
            )
            mlvTryOn.setLikeText(getString(R.string.tried_on, card.pvStr))
            bgCard.setOnAntiViolenceClickListener {
                listener.goOutfit(card)
            }
        } else if (card is PostUgcDesignCard) {
            // 服装卡片
            layoutMetaLike.visible()
            mlvLike.gone()
            mlvPlayers.gone()
            mlvTryOn.visible()
            vMainColor.setBackgroundColor(card.cardBgColorInt ?: defaultBgColor)
            listener.getGlideOrNull()?.apply {
                load(card.cover).centerCrop()
                    .placeholder(R.drawable.placeholder_corner_8)
                    .into(ivIcon)
            }
            tvTitle.text = card.title.ifNullOrEmpty { getString(R.string.fashion_design) }
            mlvTryOn.setLikeText(getString(R.string.tried_on, card.tryOnCount ?: 0))
            bgCard.setOnAntiViolenceClickListener {
                listener.goUgcDesign(card)
            }
        }
    }

    override fun ItemCommunityAssetsCardFullWidthBinding.onUnbind() {
        root.unsetOnClickAndClickable()
        ivDelBtn.unsetOnClick()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.FOCUSED_VISIBLE) {
            listener.onAssetsCardVisible(card)
        }
    }
}

fun MetaModelCollector.postTagItem(
    item: PostTag,
    margin: Int,
    enableDelete: Boolean,
    color: Int,
    listener: IPostListener
) {
    add {
        PostTagItem(
            item,
            margin,
            enableDelete,
            color,
            listener
        ).id("AddTagItem-${item.tagId}")
    }
}

fun MetaModelCollector.postNewTagItem(
    item: PostTag,
    listener: IPostListener
) {
    add {
        PostNewTagItem(
            item,
            listener
        ).id("AddTagItem-${item.tagId}")
    }
}

data class PostTagItem(
    val item: PostTag,
    val margin: Int,
    val enableDelete: Boolean,
    val color: Int,
    val listener: IPostListener
) : ViewBindingItemModel<AdapterPublishPostTagBinding>(
    R.layout.adapter_publish_post_tag,
    AdapterPublishPostTagBinding::bind
) {
    override fun AdapterPublishPostTagBinding.onBind() {
        root.setMargin(left = margin, top = margin)
        tvTopicTitle.text = item.tagName
        if (color != 0) {
            tvTopicTitle.setTextColor(color)
        }
        root.setOnAntiViolenceClickListener { listener.clickTag(item) }
        if (enableDelete) {
            ivTopicDel.visible()
            ivTopicDel.setOnAntiViolenceClickListener { listener.deleteTag(item) }
        } else {
            ivTopicDel.gone()
            ivTopicDel.unsetOnClick()
        }
    }

    override fun AdapterPublishPostTagBinding.onUnbind() {
        root.unsetOnClick()
        ivTopicDel.unsetOnClick()
    }
}

data class PostNewTagItem(
    val item: PostTag,
    val listener: IPostListener
) : ViewBindingItemModel<AdapterPublishNewPostTagBinding>(
    R.layout.adapter_publish__new_post_tag,
    AdapterPublishNewPostTagBinding::bind
) {
    override fun AdapterPublishNewPostTagBinding.onBind() {
        tvTopicTitle.text = PublishPostFragment.TAG_RULE + item.tagName

        tvViews.text = getItemView().context.getString(
            R.string.x_views,
            UnitUtil.formatKMCount2(item.viewCount)
        )
        root.setOnAntiViolenceClickListener { listener.clickTag(item) }
    }

    override fun AdapterPublishNewPostTagBinding.onUnbind() {
        root.unsetOnClick()
    }
}