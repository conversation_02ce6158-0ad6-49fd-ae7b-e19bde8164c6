package com.socialplay.gpark.ui.im.friendsearch

import android.os.Bundle
import android.text.Editable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.QrCodeInteractor
import com.socialplay.gpark.data.model.qrcode.ScanEntry
import com.socialplay.gpark.data.model.qrcode.ScanRequestData
import com.socialplay.gpark.data.model.qrcode.ScanResultData
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.share.ShareUserInfo
import com.socialplay.gpark.databinding.FragmentFriendSearchBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.base.adapter.withStatusAndRefresh
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.subListTitle
import com.socialplay.gpark.ui.home.adapter.HomeLoadMoreFooterAdapter
import com.socialplay.gpark.ui.kol.creator.IKolMoreCreatorAction
import com.socialplay.gpark.ui.kol.creator.kolMoreCreatorItem
import com.socialplay.gpark.ui.qrcode.QRCodeScanFragment
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.TextWatcherAdapter
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setFragmentResultListenerByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.koin.core.context.GlobalContext
import timber.log.Timber


/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/6/23
 *  desc   :
 */
class FriendSearchFragment: BaseRecyclerViewFragment<FragmentFriendSearchBinding>(R.layout.fragment_friend_search) {
    override val recyclerView: EpoxyRecyclerView
        get() = binding.recommendRV

    private val viewModel: FriendSearchViewModel by fragmentViewModel()
    private val adapter = FriendSearchAdapter(::glide) {
        UserLabelView.showDescDialog(this, it)
    }
    private val actionListener by lazy { getEditorAction() }

    private val userCardListener = object:IMyInfoCardListener{
        override fun copyId(userNumber: String) {
            lifecycleScope.launch {
                ClipBoardUtil.setClipBoardContent(userNumber, requireContext(), "ID")
                context?.let {
                    toast(R.string.id_copied_ok)
                }
            }
        }

        override fun fetchQrCode() {
            viewModel.fetchQrCode()
        }
    }

    private val creatorListener = object : IKolMoreCreatorAction {
        override fun goProfile(uuid: String) {
            MetaRouter.Profile.other(
                this@FriendSearchFragment,
                uuid,
                getPageName()
            )
        }

        override fun changeFollow(uuid: String, toFollow: Boolean) {
            val trackLocation = "friend_add"
            Analytics.track(EventConstants.EVENT_FOLLOW_CLICK) {
                put(EventParamConstants.KEY_USERID, uuid)
                put(EventParamConstants.KEY_LOCATION, trackLocation)
                put(
                    EventParamConstants.KEY_TYPE,
                    if (toFollow) EventParamConstants.TYPE_FOLLOW else EventParamConstants.TYPE_UNFOLLOW
                )
            }
            viewModel.changeFollow(uuid, toFollow, trackLocation)
        }

        override fun onItemShow(uuid: String) {
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentFriendSearchBinding? {
        return FragmentFriendSearchBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
    }

    private fun initData() {
        setFragmentResultListenerByActivity(
            QRCodeScanFragment.KEY_ADD_FRIEND_REQUEST_SCAN_QRCODE,
            viewLifecycleOwner
        ) { _, bundle ->
            val request = ScanRequestData.from(bundle)
            val result = ScanResultData.from(bundle)

            if (result != null && request != null) {
                viewLifecycleOwner.lifecycleScope.launch {
                    GlobalContext.get().get<QrCodeInteractor>()
                        .dispatchQRCodeScanResult(this@FriendSearchFragment, request, result)
                }
            }
        }
        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            viewModel.friendSearch.collectLatest {
                adapter.submitData(it)
            }
        }

        viewModel.onEach(
            FriendSearchExtraState::creatorList
        ) { creatorListAsync ->
            if (creatorListAsync is Fail || creatorListAsync is Success) {
                binding.recommendRefreshLayout.isRefreshing = false
            }
        }

        viewModel.onAsync(
            FriendSearchExtraState::shareProfileData,
            deliveryMode = uniqueOnly(),
            onFail = {}
        ) {
            share(it)
        }

        viewModel.registerToast(FriendSearchExtraState::toastMsg)
    }

    override fun onPause() {
        super.onPause()
        InputUtil.hideKeyboard(binding.eTSearch)
    }

    override fun loadFirstData() {
        viewModel.refresh()
    }

    private fun initView() {
        binding.titleBar.setOnBackClickedListener {
            navigateUp()
        }
        binding.ivClearSearch.setOnAntiViolenceClickListener {
            binding.eTSearch.setText("")
            clearSearResult()
        }
        binding.ivScanBtn.setOnAntiViolenceClickListener {
            MetaRouter.IM.goQRCodeScan(
                requireActivity(),
                this,
                QRCodeScanFragment.KEY_ADD_FRIEND_REQUEST_SCAN_QRCODE,
                ScanEntry.Friend
            )
        }
        binding.tvSearch.setOnAntiViolenceClickListener {
            getSearchResult(binding.eTSearch.text.toString())
        }
        binding.eTSearch.setOnEditorActionListener(actionListener)
        binding.eTSearch.addTextChangedListener(etWatcher)
        updateEtView(binding.eTSearch.text?.toString().isNullOrBlank())
        binding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        initAdapter()

        binding.eTSearch.setOnFocusChangeListener { v, hasFocus ->
            if (isBindingAvailable()) {
                if (hasFocus) {
                    binding.mlContentContainer.transitionToState(R.id.show_search_button)
                } else {
                    binding.mlContentContainer.transitionToState(R.id.hide_search_button)
                }
            }
        }

        binding.ivShareBtn.setOnAntiViolenceClickListener {
            val recentGames = viewModel.oldState.shareProfileData()
            if (recentGames == null) {
                toast(R.string.loading)
                viewModel.getUserExtra4Share()
            } else {
                share(recentGames)
            }
        }

        binding.recommendRefreshLayout.setOnRefreshListener {
            viewModel.refresh()
        }
    }

    private fun updateEtView(isEmpty: Boolean) {
        binding.tvSearch.isEnabled = !isEmpty
        binding.ivClearSearch.visible(!isEmpty)
        binding.ivScanBtn.visible(isEmpty)
    }

    private fun initAdapter() {
        adapter.apply {
            withStatusAndRefresh(viewLifecycleOwner, binding.lv, null) {
                Timber.d("Empty::$it keywords:${viewModel.searchKeyFlow.value}")
                val isEmptyResult = it && viewModel.searchKeyFlow.value.isNotEmpty()
                if (isEmptyResult) {
                    if (!NetUtil.isNetworkAvailable()) {
                        binding.lv.showError(true)
                    } else {
                        binding.lv.showEmpty()
                    }
                } else {
                    binding.lv.hide()
                }
                binding.recommendRV.visible(viewModel.searchKeyFlow.value.isEmpty() && !isEmptyResult)
            }

            val loadMoreAdapter = HomeLoadMoreFooterAdapter {
                retry()
            }
            addLoadStateListener { loadStates ->
                loadMoreAdapter.loadState = loadStates.append
            }
            addChildClickViewIds(R.id.tv_add, R.id.v_click)
            setOnItemChildClickListener { view, position ->
                val item = adapter.peek(position) ?: return@setOnItemChildClickListener
                if (!NetUtil.isNetworkAvailable()) {
                    toast(R.string.net_unavailable)
                    return@setOnItemChildClickListener
                }
                when (view.id) {
                    R.id.tv_add -> {
                        Analytics.track(EventConstants.EVENT_IM_ADD_FRIEND_CLICK) { put("from", 3) }
                        MetaRouter.IM.goFriendApply(
                            fragment = this@FriendSearchFragment,
                            uuid = item.uid,
                            userNumber = item.userNumber ?: "",
                            gamePackageName = "",
                            tagIds = item.tagIds,
                            labelInfo = item.labelInfo,
                            userName = item.nickname,
                            avatar = item.portrait
                        )
                    }

                    R.id.v_click -> {
                        if (!item.uid.isNullOrBlank()) {
                            Analytics.track(
                                EventConstants.EVENT_SEARCH_FRIEND_SEARCH_RESULT_ITEM_CLICK
                            )
                            MetaRouter.IM.goAddFriendDialog(
                                this@FriendSearchFragment,
                                item.uid,
                                from = "3"
                            )
                        }
                    }
                }
            }
            binding.recyclerView.adapter = adapter
        }
    }

    private fun clearSearResult() {
        binding.lv.visible(false)
        viewModel.clearSearchResult()
    }

    private fun getEditorAction() = TextView.OnEditorActionListener { _, actionId, _ ->
        if (actionId == EditorInfo.IME_ACTION_SEARCH) { // 按下按钮，这里和xml文件中的EditText中属性imeOptions对应
            getSearchResult(binding.eTSearch.text.toString())
        }
        true //返回true，保留软键盘;false，隐藏软键盘
    }

    private val etWatcher = object : TextWatcherAdapter() {
        override fun afterTextChanged(s: Editable?) {
            updateEtView(s?.toString().isNullOrBlank())
            if (s.isNullOrEmpty()) {
                clearSearResult()
            } else {
//                getSearchResult(s.toString(), false)
            }
        }
    }

    /**
     * 搜索点击
     */
    private fun getSearchResult(keyword: String?, hideKeyboard: Boolean = true) {
        Analytics.track(EventConstants.EVENT_SEARCH_FRIEND_SEARCH_CLICK)

        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.net_unavailable)
            return
        }
        if (keyword.isNullOrBlank()) {
            toast(R.string.friend_search_key_empty_tip)
            return
        }
        if (keyword == viewModel.searchKeyFlow.value) {
            adapter.refresh()
        }
        if (hideKeyboard) {
            InputUtil.hideKeyboard(binding.eTSearch)
        }
        viewModel.searchNewKey(keyword)
    }

    private fun share(userExtra: ShareRawData.UserExtra?) {
        val s = viewModel.oldState
        val user = s.userInfo?:return
        GlobalShareDialog.show(
            childFragmentManager,
            ShareRawData.user(
                ShareUserInfo(
                    uid = user.uuid,
                    userNumber = user.userNumber,
                    nickname = user.nickname,
                    gender = user.gender,
                    birth = user.birth,
                    city = user.city,
                    portrait = user.portrait,
                    signature = user.signature,
                    friendTotal = user.friendTotal,
                    followCount = user.followCount,
                    fansCount = user.fansCount,
                    likeCount = user.likeCount,
                ),
                userExtra
            )
        )
    }

    override fun onDestroyView() {
        binding.eTSearch.removeTextChangedListener(etWatcher)
        binding.recyclerView.adapter = null
        adapter.setOnItemChildClickListener(null)
        binding.eTSearch.setOnEditorActionListener(null)
        binding.eTSearch.setOnKeyListener(null)
        super.onDestroyView()
    }

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        FriendSearchExtraState::userInfo,
        FriendSearchExtraState::creatorList,
        FriendSearchExtraState::creatorListLoadMore,
        FriendSearchExtraState::qrCodeUrl,
    ) { userInfo, creatorListAsync, creatorListLoadMore, qrCodeUrl ->
        if (userInfo != null) {
            add {
                SearchFriendMyInfoCard(
                    glide,
                    userInfo = userInfo,
                    qrCodeUrl = qrCodeUrl,
                    listener = userCardListener,
                ).id("SearchFriendMyInfoCard-${userInfo.uuid}")
            }
        }
        subListTitle(
            title = getString(R.string.community_suggested_users),
            bottomSpace = 0,
            showMore = false
        )
        val creatorList = creatorListAsync.invoke() ?: emptyList()
        if (creatorList.isEmpty() && creatorListAsync !is Loading) {
            empty(
                descRes = R.string.no_recent_activity,
                top = dp(18)
            ) {
                viewModel.getRecommendUserList()
            }
        } else {
            creatorList.forEachIndexed { index, user ->
                kolMoreCreatorItem(
                    getPageName(),
                    user.toKolCreatorInfo(),
                    true,
                    creatorListener
                )
            }
        }
        // 加载更多
        if (creatorListAsync is Success && creatorListAsync.invoke().isNotEmpty()) {
            loadMoreFooter(creatorListLoadMore) {
                viewModel.loadMoreCreatorInfoList()
            }
        }
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_NAME_FRIEND_SEARCH
}