package com.socialplay.gpark.ui.recommend.choice

import android.graphics.drawable.Drawable
import android.view.View
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceUgcCreateFullBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.setHorizontalScrollPadding
import com.socialplay.gpark.ui.room.RoomCardUtil
import com.socialplay.gpark.util.PaletteColorUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setWidth
import com.socialplay.gpark.ui.view.*
import com.socialplay.gpark.util.extension.unsetOnClick
import kotlin.math.abs

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/01
 *     desc   :
 * </pre>
 */
fun MetaModelCollector.choiceUgcCreateBigCard(
    card: ChoiceCardInfo, cardPosition: Int, spanSize: Int, listener: IChoiceListener
) {
    val games = card.gameListNoNull
    if (games.isNullOrEmpty()) return
    add(
        ChoiceTitleMoreItem(card, spanSize, listener, getCardRedCacheKey(card)).id("ChoiceUgcCreateCardTitle-choiceUgcCreateBigCard-$cardPosition-${card.cardName.hashCode()}").spanSizeOverride { _, _, _ -> spanSize })
    carouselNoSnapWrapBuilder {
        id("ChoiceUgcCreateCardList-choiceUgcCreateBigCard-$cardPosition")
        setHorizontalScrollPadding()
        hasFixedSize(true)
        initialPrefetchItemCount(1)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        spanSizeOverride { _, _, _ ->
            spanSize
        }
        val itemWidth = RoomCardUtil.getRoomCardWidth()
        games.forEachIndexed { position, game ->
            add(
                ChoiceUgcCreateCardItem(
                    game, position, card, cardPosition, itemWidth, spanSize, listener
                ).id("ChoiceUgcCreateCardGame-${card.cardId}-$cardPosition-$position-${game.code}").spanSizeOverride { _, _, _ ->
                    1
                }
            )
        }
    }
}

data class ChoiceUgcCreateCardItem(
    val item: ChoiceGameInfo, val position: Int, val card: ChoiceCardInfo, val cardPosition: Int, val itemWidth: Int, val spanSize: Int, val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceUgcCreateFullBinding>(
    R.layout.adapter_choice_ugc_create_full, AdapterChoiceUgcCreateFullBinding::bind
) {

    override fun AdapterChoiceUgcCreateFullBinding.onBind() {
        tvAuthorName.text = item.nickname
        tvName.text = item.displayName
        tvHeat.setLikeText(UnitUtil.formatKMCount((item.playingCount?.toLong()) ?: 0L))
        ivAuthorAvatar.setOnAntiViolenceClickListener {
            listener.onOtherClick(2, position, card, item)
        }
        tvAuthorName.setOnAntiViolenceClickListener {
            listener.onOtherClick(2, position, card, item)
        }

        listener.getGlideOrNull()?.run {
            load(item.avatar).circleCrop().into(ivAuthorAvatar)

            load(item.imageUrl)
                .placeholder(PaletteColorUtil.getPlaceholderDrawable(root.context, item.gameColor))
                .error(PaletteColorUtil.getFailedPlaceholderDrawable(root.context)) // 添加错误占位图
                .into(ivIcon)
        }

        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
        ivBottomBackground.setBackgroundColor(PaletteColorUtil.getAssetPlaceholderColor(item.gameColor, item.displayName.orEmpty()))
    }

    override fun AdapterChoiceUgcCreateFullBinding.onUnbind() {
        root.unsetOnClick()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}