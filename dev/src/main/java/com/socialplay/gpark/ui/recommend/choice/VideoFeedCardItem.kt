package com.socialplay.gpark.ui.recommend.choice

import android.view.View
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.videofeed.ChoiceHomeVideoItem
import com.socialplay.gpark.databinding.AdapterChoiceCardItemVideoListBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.room.RoomCardUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setWidth

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/01
 *     desc   :
 * </pre>
 */
fun MetaModelCollector.choiceVideoFeedCard(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val videoFeedApiResult = card.videoFeeds ?: return
    if (videoFeedApiResult.items.isNullOrEmpty()) return
    add(
        ChoiceTitleMoreItem(card, spanSize, listener).id("ChoiceVideoFeedCardTitle-$cardPosition")
            .spanSizeOverride { totalSpanCount, _, _ -> spanSize.coerceAtMost(totalSpanCount) }
    )
    carouselNoSnapWrapBuilder {
        id("ChoiceVideoFeedCardList-$cardPosition")
        padding(Carousel.Padding.dp(16, 16, 6, 8, 0))
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        spanSizeOverride { totalSpanCount, _, _ ->
            spanSize.coerceAtMost(totalSpanCount)
        }
        val itemWidth = RoomCardUtil.getRoomCardWidth()
        videoFeedApiResult.items.map {
            ChoiceHomeVideoItem(
                it,
                videoFeedApiResult.reqId.orEmpty()
            )
        }.forEachIndexed { position, video ->
            add(
                ChoiceVideoFeedCardItem(
                    video,
                    position,
                    card,
                    cardPosition,
                    itemWidth,
                    spanSize,
                    listener
                ).id("ChoiceVideoFeedCardGame-${card.cardId}-$cardPosition-$position-${video.reqId}")
            )
        }
    }
}

data class ChoiceVideoFeedCardItem(
    val item: ChoiceHomeVideoItem,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val itemWidth: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardItemVideoListBinding>(
    R.layout.adapter_choice_card_item_video_list,
    AdapterChoiceCardItemVideoListBinding::bind
) {

    override fun AdapterChoiceCardItemVideoListBinding.onBind() {
        root.setWidth(itemWidth)
        root.setMargin(right = dp(10))
        val postDetail = item.postDetail
        clCover.clipToOutline = true
        tvTitle.text = postDetail.content
        listener.getGlideOrNull()?.run {
            load(postDetail.mediaList?.first { it.isVideo }?.cover).into(ivCover)
            load(postDetail.avatar).into(ivAuthorAvatar)
        }
        tvAuthorName.text = postDetail.nickname.orEmpty()
        tvLikeCount.text = UnitUtil.formatKMCount(postDetail.likeCount)
        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}