package com.socialplay.gpark.ui.recommend

import android.graphics.drawable.Drawable
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.core.view.updatePadding

import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.HomeCustomRecommend
import com.socialplay.gpark.databinding.ItemRecommendGameBinding
import com.socialplay.gpark.databinding.ViewChoiceContinueGameBinding
import com.socialplay.gpark.databinding.ViewChoiceFriendsViewBinding
import com.socialplay.gpark.ui.core.GlideGetter
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.editorschoice.header.friends.ChoiceHomeHeaderFriends
import com.socialplay.gpark.ui.main.HomeImageShowAnalytics
import com.socialplay.gpark.ui.main.startup.HomeStartupProject
import com.socialplay.gpark.ui.recommend.choice.IChoiceListener
import com.socialplay.gpark.util.PaletteColorUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener

import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.ui.view.BlurEffectManager
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.setSize
import com.socialplay.gpark.util.extension.setWidth
import timber.log.Timber

fun EpoxyController.addFriendHeader(header: ChoiceHomeHeaderFriends, spanSize: Int) {
    add(FriendHeader(header).apply {
        id("friendHeader")
        spanSizeOverride { _, _, _ ->
            // 在GridLayoutManager中，spanSizeOverride控制Item占用的列数
            // FriendHeader需要占满整行
            spanSize
        }
    })
}

fun EpoxyController.addContinueHeader(
    playedCount: Int, listener: IChoiceListener, spanSize: Int
) {
    add(ContinueHeader(playedCount, listener).apply {
        id("continueHeader")
        // 在GridLayoutManager中，spanSizeOverride控制Item占用的列数
        // ContinueHeader需要占满整行
        spanSizeOverride { _, _, _ ->
            spanSize
        }
    })
}

fun EpoxyController.addGameItem(
    spanSize: Int, index: Int, item: HomeCustomRecommend.RecommendList, onShow: () -> Unit, onClick: () -> Unit, onAvatarClick: () -> Unit, getGlide: GlideGetter
) {
    add(GameItem(spanSize, index, item, onShow, onClick, onAvatarClick, getGlide).apply {
        id("Recommend-GameItem-${item.gameId}-${index}")
        // 在GridLayoutManager中，spanSizeOverride控制Item占用的列数
        // GameItem只占用一列，不占满整行
        spanSizeOverride { _, _, _ ->
            1
        }
    })
}

private fun <T> RequestBuilder<T>.loadImageListener(url: String?, position: Int): RequestBuilder<T> {
    HomeStartupProject.onHomeStartLoadImage(url, position)
    HomeImageShowAnalytics.onHomeStartLoadImage(url, position)
    return this.listener(object : RequestListener<T> {
        override fun onLoadFailed(
            e: GlideException?, model: Any?, target: Target<T>, isFirstResource: Boolean
        ): Boolean {
            HomeStartupProject.onHomeFinishLoadImage(url, position)
            HomeImageShowAnalytics.onHomeFinishLoadImage(url, position)
            return false
        }

        override fun onResourceReady(
            resource: T & Any, model: Any, target: Target<T>?, dataSource: DataSource, isFirstResource: Boolean
        ): Boolean {
            HomeStartupProject.onHomeFinishLoadImage(url, position)
            HomeImageShowAnalytics.onHomeFinishLoadImage(url, position)
            return false
        }
    })
}

data class GameItem(
    val spanSize: Int,
    val index: Int,
    val item: HomeCustomRecommend.RecommendList,
    val onShow: () -> Unit,
    val onClick: () -> Unit,
    val onAvatarClick: () -> Unit,
    val getGlide: GlideGetter,
) : ViewBindingItemModel<ItemRecommendGameBinding>(
    R.layout.item_recommend_game, ItemRecommendGameBinding::bind
) {

    override fun ItemRecommendGameBinding.onBind() {
        val bindStartTimestamp = System.currentTimeMillis()
        Timber.tag("Performance").d("Performance GameItem onBind called - Index: $index, GameId: ${item.gameId} - Start Time: $bindStartTimestamp")
        mlvLike.setLikeText(UnitUtil.formatKMCount(item.localLikeCount ?: item.likeCount ?: 0L))
        mlvPlayerNum.setLikeText(UnitUtil.formatKMCount(item.pvCount ?: 0L))

        // 标签(因为这期2.9.0后台标签还不全，所以直接隐藏不展示)
        layoutTags.gone()
//        layoutTags.setTags(item.tagList ?: emptyList())
        tvTitle.text = item.gameName
        tvName.text = item.userName
        getGlide()?.run {
            load(item.banner)
                .loadImageListener(item.banner, index)
                .placeholder(PaletteColorUtil.getPlaceholderDrawable(root.context, item.gameColor))
                .error(PaletteColorUtil.getFailedPlaceholderDrawable(root.context)) // 添加错误占位图
                .into(iv)

            load(item.userIcon).placeholder(R.drawable.placeholder_round).transform(CenterCrop(), RoundedCorners(12.dp)).into(ivIcon)
        }

        ivIcon.setOnAntiViolenceClickListener {
            onAvatarClick.invoke()
        }
        tvName.setOnAntiViolenceClickListener {
            onAvatarClick.invoke()
        }
        root.setOnAntiViolenceClickListener {
            onClick.invoke()
        }
        Timber.tag("Performance").d("Performance GameItem onBind finished - Index: $index, GameId: ${item.gameId} - End Time: ${System.currentTimeMillis()} - Bind Time: ${System.currentTimeMillis() - bindStartTimestamp}ms")
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.VISIBLE) {
            onShow.invoke()
        }
    }

    override fun ItemRecommendGameBinding.onUnbind() {
        root.unsetOnClick()
        getGlide()?.clear(iv)
        getGlide()?.clear(ivIcon)
        Timber.tag("Performance").d("GameItem onUnbind called - Index: $index, GameId: ${item.gameId}")
    }

}

data class ContinueHeader(
    val playedCount: Int, val listener: IChoiceListener
) : ViewBindingItemModel<ViewChoiceContinueGameBinding>(
    R.layout.view_choice_continue_game, ViewChoiceContinueGameBinding::bind
) {

    override fun createView(parent: ViewGroup): View {
        return super.createView(parent)
    }

    override fun ViewChoiceContinueGameBinding.onBind() {
        // 在GridLayoutManager中，spanSize通过spanSizeOverride控制，不需要setFullSpan
        Timber.d("ContinueHeader: spanSize controlled by spanSizeOverride")

        val showContinue = playedCount > 0
        rvRecentlyPlayed.isVisible = showContinue
        tvPlayedTitle.isVisible = showContinue
        vContinueSpace.isVisible = showContinue
        tvPlayedManage.isVisible = showContinue
        ivPlayedManage.isVisible = showContinue
        ivPlayedBg.isVisible = showContinue
        clPlayed.isVisible = showContinue
        listener.bindPlayed(playedCount, this)
    }

    override fun ViewChoiceContinueGameBinding.onUnbind() {
        listener.unbindPlayed(playedCount, this)
    }
}

data class FriendHeader(
    val header: ChoiceHomeHeaderFriends
) : ViewBindingItemModel<ViewChoiceFriendsViewBinding>(
    R.layout.view_choice_friends_view, ViewChoiceFriendsViewBinding::bind
) {
    override fun createView(parent: ViewGroup): View {
        val view = header.getView()
        (view.parent as? ViewGroup)?.removeView(view)
        return view
    }

    override fun ViewChoiceFriendsViewBinding.onBind() {
        rootView.post {
            // 需要等布局完成再设置，否则首次不生效
            rootView.setMargin(left = -(10.dp), right = -(10.dp))
        }
        rvFriendsList.setPaddingEx(left = 16.dp)
        Timber.d("FriendHeader: spanSize controlled by spanSizeOverride, left padding added")
    }
}