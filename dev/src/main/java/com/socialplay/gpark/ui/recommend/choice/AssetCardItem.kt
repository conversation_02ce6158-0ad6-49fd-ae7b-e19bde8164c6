package com.socialplay.gpark.ui.recommend.choice

import android.graphics.drawable.Drawable
import android.view.View
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemBuyBinding
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.setHorizontalScrollMargins
import com.socialplay.gpark.ui.core.views.setHorizontalScrollPadding
import com.socialplay.gpark.util.PaletteColorUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.ui.view.*

fun MetaModelCollector.assetCardList(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener,
    selfUuid: String?
) {
    val games = card.gameListNoNull ?: return
    // 先添加标题和更多按钮
    add(
        ChoiceTitleMoreItem(card, spanSize, listener, getCardRedCacheKey(card))
            .id("AssetCardTitle-$cardPosition-${card.cardName.hashCode()}")
            .spanSizeOverride { _, _, _ -> spanSize }
    )
    carouselNoSnapWrapBuilder {
        id("AssetCardList-$cardPosition")
        setHorizontalScrollPadding()
        hasFixedSize(true)
        initialPrefetchItemCount(1)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        // 在StaggeredGridLayoutManager中，spanSizeOverride不会生效
        spanSizeOverride { _, _, _ ->
            spanSize
        }
        games.forEachIndexed { index, game ->
            if (index >= 8) {
                return@forEachIndexed
            }
            add(
                BuyCardItem(
                    item = game,
                    position = index,
                    card = card,
                    cardPosition = cardPosition,
                    spanSize = spanSize,
                    listener = listener,
                    selfUuid = selfUuid
                ).id("BuyCardItem-${card.cardId}-$cardPosition-$index-${game.contentId}").spanSizeOverride { _, _, _ ->
                    1
                }
            )
        }
    }
}

data class BuyCardItem(
    val item: ChoiceGameInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val spanSize: Int,
    val listener: IChoiceListener,
    val selfUuid: String?
) : ViewBindingItemModel<AdapterChoiceCardItemBuyBinding>(
    R.layout.adapter_choice_card_item_buy,
    AdapterChoiceCardItemBuyBinding::bind
) {
    
    // 生成唯一的视图ID
    override fun AdapterChoiceCardItemBuyBinding.onBind() {
        // 点赞
        likeView.setLikeText(UnitUtil.formatKMCount(item.localLikeCount ?: item.likeCount ?: 0))
        // 顶部图片
        listener.getGlideOrNull()?.run {
            load(item.iconUrl)
                .placeholder(PaletteColorUtil.getPlaceholderDrawable(root.context, item.gameColor))
                .error(PaletteColorUtil.getFailedPlaceholderDrawable(root.context)) // 添加错误占位图
                .into(ivGameIcon)
        }
        // 名称
        tvGameTitle.text = item.displayName
        // 标签
        tagContainer.setTags(item.tagList ?: emptyList())
        // 价格
        tvPrice.text = "${item.price ?: 0L}"
        // 金币icon
        ivCoin.setImageResource(R.drawable.icon_item_buy_coins_g)
        if (item.isHidePrice()) {
            // 服装,不展示金币和价格
            layoutPrice.invisible()
        } else {
            layoutPrice.visible()

            if (item.isShowFree()) {
                // 未定价，价格为0，价格展示为Free
                tvPrice.text = context.getString(R.string.cloth_price_free)
            }
        }
        // 购买按钮
        if (item.assetShowState(selfUuid) == 1) {
            btnBuy.alpha = 0.3f
            btnBuy.text = context.getString(R.string.obtained)
        } else if (item.assetShowState(selfUuid) == 2) {
            btnBuy.alpha = 1.0f
            btnBuy.text = context.getString(R.string.acquired)
        } else {
            btnBuy.alpha = 1.0f
            btnBuy.text = context.getString(R.string.buy_now)
        }
        btnBuy.setOnAntiViolenceClickListener {
            if (item.isPurchase != true) {
                listener.onOtherClick(1, position, card, item)
            }
        }

        if (!PandoraToggle.enableAssetCommercialization) {
            btnBuy.gone()
        }

        // 整卡点击
        root.setOnAntiViolenceClickListener { listener.onItemClick(cardPosition, card, position, item, false) }
    }

    override fun AdapterChoiceCardItemBuyBinding.onUnbind() {
        root.unsetOnClick()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
} 