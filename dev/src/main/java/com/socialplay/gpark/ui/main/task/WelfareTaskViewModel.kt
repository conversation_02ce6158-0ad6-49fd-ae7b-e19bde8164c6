package com.socialplay.gpark.ui.main.task

import android.content.ComponentCallbacks
import android.os.CountDownTimer
import androidx.compose.ui.geometry.Rect
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.meta.verse.lib.loader.env.HostEnv.isParty
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.exception
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.interactor.TTaiInteractor
import com.socialplay.gpark.data.kv.TTaiKV
import com.socialplay.gpark.data.model.pay.UserBalance
import com.socialplay.gpark.data.model.task.CampaignCheckInRequest
import com.socialplay.gpark.data.model.task.CampaignDetailResponse
import com.socialplay.gpark.data.model.task.CampaignGameSetting
import com.socialplay.gpark.data.model.task.CampaignInfo
import com.socialplay.gpark.data.model.task.CampaignModule
import com.socialplay.gpark.data.model.task.CampaignModuleData
import com.socialplay.gpark.data.model.task.CampaignModuleDataCheckIn
import com.socialplay.gpark.data.model.task.CampaignModuleDataMission
import com.socialplay.gpark.data.model.task.CampaignReceiveRequest
import com.socialplay.gpark.data.model.task.CampaignShareSetting
import com.socialplay.gpark.data.model.task.CampaignUserActivityStatus
import com.socialplay.gpark.data.model.task.CampaignUserRewardStatus
import com.socialplay.gpark.data.model.task.WelfareTaskAnimInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.pay.IPayInteractor
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.default
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.getStringByGlobal
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import org.koin.core.context.GlobalContext

/**
 * 签到成功的信息
 */
data class WelfareCheckInSuccessInfo(
    val step: Int,
    val activityId: Int?,
    val moduleId: Int,
    val checkInId: Int,
    val rewardId: Int,
    val item: CampaignModuleDataCheckIn.CheckInReward,
    val viewRect: Rect,
) {
    companion object {
        // 网络请求成功
        const val STEP_REQUEST_SUCCESS = 0

        // 弹出签到成功的弹窗
        const val STEP_SHOW_OBTAIN_DIALOG = 1

        // 余额增长
        const val STEP_INCREASE_BALANCE = 2

        // 播放进度条动画
        const val STEP_PLAY_PROGRESS = 3

        // 结束
        const val STEP_END = 4
    }
}

/**
 * 任务领取成功信息
 */
data class WelfareRewardObtainSuccess(
    val step: Int,
    val activityId: Int?,
    val moduleId: Int,
    val missionId: Int,
    val item: CampaignModuleDataMission.MissionTarget,
    val viewRect: Rect
) {
    companion object {
        // 网络请求成功
        const val STEP_REQUEST_SUCCESS = 0

        // 弹出获取成功的弹窗
        const val STEP_SHOW_OBTAIN_DIALOG = 1

        // 余额增长
        const val STEP_INCREASE_BALANCE = 2

        // 显示领取完成后的卡片
        const val STEP_SHOW_CARD = 3

        // 结束
        const val STEP_END = 4
    }
}

data class WelfareTaskState(
    val detail: Async<CampaignDetailResponse> = Uninitialized,
    val coinsBalance: Long? = null,
    val pointsBalance: Long? = null,
    val meetWelfareTaskRules: Boolean? = null,
    val checkInRequestSuccess: WelfareCheckInSuccessInfo? = null,
    /**
     * 签到成功
     * 没有去修改 detail 的值, 而是另外起新的值
     * 因为 detail 的字段嵌套太深了, 不好修改和维护
     * key 是 rewardId
     */
    val checkInSuccess: Map<Int, WelfareCheckInSuccessInfo>? = null,
    val rewardObtainRequestSuccess: WelfareRewardObtainSuccess? = null,
    /**
     * 奖励获取成功
     * 没有去修改 detail 的值, 而是另外起新的值
     * 因为 detail 的字段嵌套太深了, 不好修改和维护
     * key 是 missionId
     */
    val rewardObtainSuccess: Map<Int, WelfareRewardObtainSuccess>? = null,
    val limitTimeTaskRemainingMs: Long = 0,
    val animInfo: WelfareTaskAnimInfo? = null,
    val toast: ToastData = ToastData.EMPTY,
) : MavericksState

class WelfareTaskViewModel(
    initialState: WelfareTaskState,
    val payInteractor: IPayInteractor,
    val metaRepository: com.socialplay.gpark.data.IMetaRepository,
    val accountInteractor: AccountInteractor,
    val tTaiInteractor: TTaiInteractor,
) : BaseViewModel<WelfareTaskState>(initialState) {
    /**
     * 已经播放过签到动画的item, 避免重复播放, 记录的值是 rewardId
     */
    private val _checkInProgressAnimRecord: MutableSet<Int> = mutableSetOf()
    val checkInProgressAnimRecord: Set<Int> get() = _checkInProgressAnimRecord

    /**
     * 已经播放过Upgrade动画的item, 避免重复播放, 记录的值是 missionId
     */
    private val _upgradeAnimRecord: MutableSet<Int> = mutableSetOf()
    val upgradeAnimRecord: Set<Int> get() = _upgradeAnimRecord

    /**
     * 已经播放过的领取奖励的Card抽出动画, 避免重复播放, 记录的值是 missionId
     */
    private val _obtainRewardCardAnimRecord: MutableSet<Int> = mutableSetOf()
    val obtainRewardCardAnimRecord: Set<Int> get() = _obtainRewardCardAnimRecord
    private var mCountDownTimer: CountDownTimer? = null

    init {
        // 活动的开启条件要求:
        // 1. 设置账密;
        // 2. 绑定邮箱
        accountInteractor.accountLiveData.observeForever { metaUserInfo ->
            val isSetAccountPassword = hasAccountPassword(metaUserInfo)
            val isBindEmail = isBindEmail(metaUserInfo)
            setState {
                copy(
                    meetWelfareTaskRules = isSetAccountPassword && isBindEmail
                )
            }
        }
        viewModelScope.launch {
            tTaiInteractor.getTTaiWithTypeV3<WelfareTaskAnimInfo>(TTaiKV.ID_WELFARE_TASK_ANIM_CONFIG)
                .map { config ->
                    config ?: WelfareTaskAnimInfo.DEFAULT_CONFIG
                }.collect { animInfo ->
                    setState {
                        copy(
                            animInfo = animInfo
                        )
                    }
                }
        }
    }

    fun onPlayShowUpgradeAnim(missionId: Int) {
        _upgradeAnimRecord.add(missionId)
    }

    fun onPlayObtainRewardCardAnim(missionId: Int) {
        _obtainRewardCardAnimRecord.add(missionId)
    }

    private fun hasAccountPassword(userInfo: MetaUserInfo?): Boolean {
        return isParty() || userInfo?.hasBindAccPwd == true
    }

    private fun isBindEmail(userInfo: MetaUserInfo?): Boolean {
        return if (isParty()) {
            true
        } else {
            !userInfo?.bindEmail.isNullOrEmpty()
                    && userInfo.bindEmail != "null"
        }
    }

    /**
     * @param remainingTimeMs 剩余时间, 毫秒
     */
    fun startCountDownTime(remainingTimeMs: Long) {
        viewModelScope.launch(Dispatchers.Main) {
            mCountDownTimer?.cancel()
            mCountDownTimer = null
            setState {
                copy(limitTimeTaskRemainingMs = remainingTimeMs)
            }
            if (remainingTimeMs > 0) {
                mCountDownTimer = object : CountDownTimer(remainingTimeMs, 1000L) {
                    override fun onTick(millisUntilFinished: Long) {
                        setState {
                            copy(limitTimeTaskRemainingMs = millisUntilFinished)
                        }
                    }

                    override fun onFinish() {
                        setState {
                            copy(limitTimeTaskRemainingMs = 0)
                        }
                    }
                }
                mCountDownTimer?.start()
            }
        }
    }

    fun refresh() {
        setState {
            copy(detail = Loading(detail.invoke()))
        }
        getBalance()
        getCampaignDetail()
//        viewModelScope.launch {
//            mockCampaignDetailResponse()
//        }
    }

    fun getBalance() {
        viewModelScope.launch {
            async {
                payInteractor.getBalance { balance ->
                    setState {
                        copy(
                            coinsBalance = balance?.leCoinNum
                        )
                    }
                }
            }
            async {
                payInteractor.getPointsBalance { balance ->
                    setState {
                        copy(
                            pointsBalance = balance?.leCoinNum
                        )
                    }
                }
            }
        }
    }

    fun getCampaignDetail() {
        viewModelScope.launch {
            val result = metaRepository.getCampaignDetail(null)
            if (result.succeeded && result.data != null) {
                val campaignDetail = result.data!!
                val remainingTimeMs = campaignDetail.limitedTimeTask()?.remainingTimeMs ?: 0
                startCountDownTime(remainingTimeMs)
                setState {
                    // 刷新接口后, 需要清空状态记录
                    copy(
                        detail = Success(campaignDetail),
                        checkInRequestSuccess = null,
                        checkInSuccess = null,
                        rewardObtainRequestSuccess = null,
                        rewardObtainSuccess = null,
                    )
                }
            } else {
                setState {
                    copy(
                        detail = Fail(result.exception ?: Exception(), detail.invoke()),
                        toast = toast.toMsg(result.message)
                    )
                }
            }
        }
    }

    /**
     * 活动签到
     */
    fun checkIn(
        activityId: Int?,
        moduleId: Int,
        checkInId: Int,
        rewardId: Int,
        item: CampaignModuleDataCheckIn.CheckInReward,
        viewRect: Rect,
        day: Int,
    ) {
        viewModelScope.launch {
            val result = metaRepository.campaignCheckIn(
                request = CampaignCheckInRequest(
                    moduleId = moduleId,
                    checkinId = checkInId,
                )
            )
            if (result.succeeded) {
                Analytics.track(
                    EventConstants.C_DAILY_SIGN_IN,
                    "day" to day,
                    "checkinid" to checkInId.toString()
                )
                checkInSuccess(
                    WelfareCheckInSuccessInfo(
                        step = WelfareCheckInSuccessInfo.STEP_REQUEST_SUCCESS,
                        activityId = activityId,
                        moduleId = moduleId,
                        checkInId = checkInId,
                        rewardId = rewardId,
                        item = item,
                        viewRect = viewRect
                    )
                )
            } else {
                setState {
                    copy(
                        toast = toast.toMsg(
                            result.message ?: getStringByGlobal(R.string.activity_check_in_failed)
                        )
                    )
                }
            }
        }
    }

    /**
     * 签到成功
     * 当前方法没有去修改 detail 的值, 而是另外起新的值
     * 因为 detail 的字段嵌套太深了, 不好修改和维护
     */
    fun checkInSuccess(checkInInfo: WelfareCheckInSuccessInfo) {
        val oldRecord = oldState.checkInSuccess ?: emptyMap()
        val newRecord = oldRecord.toMutableMap()
        newRecord[checkInInfo.rewardId] = checkInInfo
        setState {
            copy(
                checkInRequestSuccess = checkInInfo,
                checkInSuccess = newRecord,
                coinsBalance = if (checkInInfo.step == WelfareCheckInSuccessInfo.STEP_END && checkInInfo.item.isCoinRewardType()) {
                    (coinsBalance ?: 0) + (checkInInfo.item.rewardCount ?: 0)
                } else {
                    coinsBalance
                },
                pointsBalance = if (checkInInfo.step == WelfareCheckInSuccessInfo.STEP_END && checkInInfo.item.isPointRewardType()) {
                    (pointsBalance ?: 0) + (checkInInfo.item.rewardCount ?: 0)
                } else {
                    pointsBalance
                }
            )
        }
    }

    /**
     * 活动领奖
     */
    fun obtainReward(
        activityId: Int?,
        moduleId: Int,
        missionId: Int,
        item: CampaignModuleDataMission.MissionTarget,
        viewRect: Rect,
    ) {
        viewModelScope.launch {
            val result = metaRepository.receiveReward(
                request = CampaignReceiveRequest(
                    moduleId = moduleId,
                    missionId = missionId,
                )
            )
            Analytics.track(
                EventConstants.C_DAILY_TASK_BUTTON_CLICK,
                "id" to (activityId?.toString() ?: ""),
                "module_id" to moduleId.toString(),
                "click_status" to "claim",
                "task_type" to (item.rule?.type?.toString() ?: ""),
                "task_param" to (item.rule?.param ?: ""),
                "task_target" to (item.rule?.target?.toString() ?: ""),
                "task_id" to (item.missionId?.toString() ?: ""),
                "result" to if (result.succeeded) {
                    "success"
                } else {
                    "fail"
                },
                "fail_reason" to if (result.succeeded) {
                    ""
                } else {
                    result.message ?: ""
                },
            )
            if (result.succeeded) {
                obtainRewardSuccess(
                    WelfareRewardObtainSuccess(
                        step = WelfareRewardObtainSuccess.STEP_REQUEST_SUCCESS,
                        activityId = activityId,
                        moduleId = moduleId,
                        missionId = missionId,
                        item = item,
                        viewRect = viewRect,
                    )
                )
            } else {
                setState {
                    copy(
                        toast = toast.toMsg(
                            result.message ?: getStringByGlobal(R.string.activity_reward_failed)
                        )
                    )
                }
            }
        }
    }

    /**
     * 奖励获取成功
     * 当前方法没有去修改 detail 的值, 而是另外起新的值
     * 因为 detail 的字段嵌套太深了, 不好修改和维护
     */
    fun obtainRewardSuccess(obtainInfo: WelfareRewardObtainSuccess) {
        val oldRecord = oldState.rewardObtainSuccess ?: emptyMap()
        val newRecord = oldRecord.toMutableMap()
        newRecord[obtainInfo.missionId] = obtainInfo
        setState {
            copy(
                rewardObtainRequestSuccess = obtainInfo,
                rewardObtainSuccess = newRecord,
                coinsBalance = if (obtainInfo.step == WelfareCheckInSuccessInfo.STEP_END && obtainInfo.item.reward?.isCoinRewardType() == true) {
                    (coinsBalance ?: 0) + (obtainInfo.item.reward.rewardCount ?: 0)
                } else {
                    coinsBalance
                },
                pointsBalance = if (obtainInfo.step == WelfareCheckInSuccessInfo.STEP_END && obtainInfo.item.reward?.isPointRewardType() == true) {
                    (pointsBalance ?: 0) + (obtainInfo.item.reward.rewardCount ?: 0)
                } else {
                    pointsBalance
                }
            )
        }
    }

    override fun onCleared() {
        super.onCleared()
        mCountDownTimer?.cancel()
        mCountDownTimer = null
    }

    companion object : KoinViewModelFactory<WelfareTaskViewModel, WelfareTaskState>() {
        override fun ComponentCallbacks.initialState(viewModelContext: ViewModelContext): WelfareTaskState? {
            val accountInteractor: AccountInteractor = GlobalContext.get().get()
            val userInfo = accountInteractor.accountLiveData.value
            return WelfareTaskState(
                meetWelfareTaskRules = hasAccountPassword(userInfo) && isBindEmail(userInfo)
            )
        }

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: WelfareTaskState
        ): WelfareTaskViewModel {
            return WelfareTaskViewModel(state, get(), get(), get(), get())
        }

        private fun hasAccountPassword(userInfo: MetaUserInfo?): Boolean {
            return isParty() || userInfo?.hasBindAccPwd == true
        }

        private fun isBindEmail(userInfo: MetaUserInfo?): Boolean {
            return if (isParty()) {
                true
            } else {
                !userInfo?.bindEmail.isNullOrEmpty()
                        && userInfo.bindEmail != "null"
            }
        }
    }

    suspend fun mockCampaignDetailResponse() {
        startCountDownTime(14 * 24 * 60 * 60 * 1000)
        val baseInfo = CampaignInfo(
            activityId = 10001,
            bgColor = "#0F1522",
            bgImg = "https://img.metaapp.io/demo/bg_campaign.png",
            startTime = "2025-01-01 00:00:00",
            endTime = "2025-12-31 23:59:59",
            offlineTime = "2026-01-31 23:59:59",
            style = "default",
            name = "G派福利任务",
            rule = "1. 每日签到可领奖励；2. 完成任务得金币或积分；3. 奖励以实际发放为准。",
            status = 1,
            maintenanceText = "系统维护中...",
            closeText = "活动已结束，感谢参与",
            gameSetting = CampaignGameSetting(
                gameId = "gp_demo",
                gameManufacturer = "Meta Studio",
                gameName = "G Park",
                gamePackageName = "com.meta.gpark",
                gameVersionCode = "2100"
            ),
            shareSetting = CampaignShareSetting(
                title = "来G派领福利！",
                desc = "签到做任务，金币积分拿不停",
                icon = "https://img.metaapp.io/demo/campaign_share.png"
            )
        )

        // 签到模块
        val checkInModule = CampaignModule(
            id = 1000,
            description = "每日签到领福利",
            validDateType = 1,
            endTime = baseInfo.endTime,
            name = "日常签到",
            startTime = baseInfo.startTime,
            style = "card",
            type = CampaignModuleData.TYPE_CHECK_IN,
            customizedData = CampaignModuleDataCheckIn(
                checkinUpgradeConfig = CampaignModuleDataCheckIn.CheckInUpgradeConfig(
                    currentCheckinDetail = CampaignModuleDataCheckIn.CurrentCheckInDetail(
                        checkinId = 9001,
                        checkinType = 1,
                        cycle = com.socialplay.gpark.data.model.task.CampaignCycle(
                            type = "2",
                            value = 1
                        ),
                        checkinRewards = (1..5).map { day ->
                            CampaignModuleDataCheckIn.CheckInReward(
                                id = 10000 + day,
                                day = day,
                                rewardType = if (day % 2 == 0) "pd_coin" else "le_integral",
                                rewardRelateId = "",
                                rewardName = if (day % 2 == 0) "金币" else "积分",
                                rewardCount = if (day % 2 == 0) 50L * day else 20L * day,
                                rewardIcon = "https://img.metaapp.io/demo/coin_${day}.png",
                                date = "2025-01-${10 + day}",
                                receiveStatus = if (day < 3) {
                                    CampaignModuleDataCheckIn.CheckInReward.RECEIVE_STATUS_CLAIMED
                                } else if (day == 3) {
                                    CampaignModuleDataCheckIn.CheckInReward.RECEIVE_STATUS_UNCLAIMED
                                } else {
                                    CampaignModuleDataCheckIn.CheckInReward.RECEIVE_STATUS_UNRECEIVED
                                },
                                dateStatus = if (day < 3) {
                                    CampaignModuleDataCheckIn.CheckInReward.DATE_STATUS_EXPIRED
                                } else if (day == 3) {
                                    CampaignModuleDataCheckIn.CheckInReward.DATE_STATUS_NOW
                                } else {
                                    CampaignModuleDataCheckIn.CheckInReward.DATE_STATUS_FUTURE
                                },
                            )
                        },
                        checkinProfile = CampaignModuleDataCheckIn.CheckInProfile(
                            intervalId = "A-1",
                            days = 1,
                            cycleStart = "2025-01-10 00:00:00",
                            cycleEnd = "2025-01-16 23:59:59",
                        )
                    )
                )
            )
        )

        // 新人限时任务模块
        val newcomerMissionModule = CampaignModule(
            id = 1001,
            description = "新人限时福利任务",
            subtitle = "15天轻松赚",
            validDateType = 2,
            endTime = baseInfo.endTime,
            endTimeMills = 14 * 24 * 60 * 60 * 1000,
            // TODO 英文单词换行显示后, 实际占用的宽度过多
            name = "Limited-time benefits",
            startTime = baseInfo.startTime,
            style = "card",
            type = CampaignModuleData.TYPE_MISSION_NEWCOMER,
            customizedData = CampaignModuleDataMission(
                missions = listOf(
                    CampaignModuleDataMission.MissionDetail(
                        name = "首次创建形象",
                        description = "在编辑器中创建你的第一个形象",
                        groupId = "g1",
                        intro = "完成后可领取",
                        targetType = 1,
                        receiveType = 1,
                        signUpType = 2,
                        timesLimitCycle = 1,
                        timesLimitTotal = 1,
                        missionTargets = listOf(
                            CampaignModuleDataMission.MissionTarget(
                                receiveStatus = CampaignModuleDataMission.MissionTarget.RECEIVE_STATUS_UNRECEIVED,
                                missionId = 5001,
                                missionStatus = CampaignModuleDataMission.MissionTarget.MISSION_STATUS_UNCOMPLETED,
                                reward = CampaignModuleDataMission.Reward(
                                    rewardType = "pd_coin",
                                    rewardRelateId = "",
                                    rewardName = "金币",
                                    rewardCount = 200,
                                    rewardIcon = "https://img.metaapp.io/demo/coin.png",
                                    rewardParam = null
                                ),
                                rule = CampaignModuleDataMission.Rule(
                                    type = 1,
                                    name = "完成1次",
                                    target = 1,
                                    param = ""
                                ),
                                tagNames = listOf("新手必做")
                            )
                        ),
                        cycle = com.socialplay.gpark.data.model.task.CampaignCycle(
                            type = "1",
                            value = 0
                        ),
                        jumper = CampaignModuleDataMission.Jumper(
                            imageUrl = "https://img.metaapp.io/demo/jump_create.png",
                            jumpParam = "editor",
                            jumpType = "router",
                            name = "去创建"
                        ),
                        eventIds = listOf("evt_create_avatar")
                    ),
                    CampaignModuleDataMission.MissionDetail(
                        name = "首次创建形象2",
                        description = "在编辑器中创建你的第一个形象",
                        groupId = "g2",
                        intro = "完成后可领取",
                        targetType = 1,
                        receiveType = 1,
                        signUpType = 2,
                        timesLimitCycle = 1,
                        timesLimitTotal = 1,
                        missionTargets = listOf(
                            CampaignModuleDataMission.MissionTarget(
                                receiveStatus = CampaignModuleDataMission.MissionTarget.RECEIVE_STATUS_UNRECEIVED,
                                missionId = 5002,
                                missionStatus = CampaignModuleDataMission.MissionTarget.MISSION_STATUS_COMPLETED,
                                reward = CampaignModuleDataMission.Reward(
                                    rewardType = "pd_coin",
                                    rewardRelateId = "",
                                    rewardName = "金币",
                                    rewardCount = 200,
                                    rewardIcon = "https://img.metaapp.io/demo/coin.png",
                                    rewardParam = null
                                ),
                                rule = CampaignModuleDataMission.Rule(
                                    type = 1,
                                    name = "完成1次",
                                    target = 1,
                                    param = ""
                                ),
                                tagNames = listOf("新手必做")
                            )
                        ),
                        cycle = com.socialplay.gpark.data.model.task.CampaignCycle(
                            type = "1",
                            value = 0
                        ),
                        jumper = CampaignModuleDataMission.Jumper(
                            imageUrl = "https://img.metaapp.io/demo/jump_create.png",
                            jumpParam = "editor",
                            jumpType = "router",
                            name = "去创建"
                        ),
                        eventIds = listOf("evt_create_avatar")
                    ),

                    CampaignModuleDataMission.MissionDetail(
                        name = "首次创建形象3",
                        description = "在编辑器中创建你的第一个形象",
                        groupId = "g3",
                        intro = "完成后可领取",
                        targetType = 1,
                        receiveType = 1,
                        signUpType = 2,
                        timesLimitCycle = 1,
                        timesLimitTotal = 1,
                        missionTargets = listOf(
                            CampaignModuleDataMission.MissionTarget(
                                receiveStatus = CampaignModuleDataMission.MissionTarget.RECEIVE_STATUS_UNRECEIVED,
                                missionId = 5003,
                                missionStatus = CampaignModuleDataMission.MissionTarget.MISSION_STATUS_COMPLETED,
                                reward = CampaignModuleDataMission.Reward(
                                    rewardType = "pd_coin",
                                    rewardRelateId = "",
                                    rewardName = "金币",
                                    rewardCount = 200,
                                    rewardIcon = "https://img.metaapp.io/demo/coin.png",
                                    rewardParam = null
                                ),
                                rule = CampaignModuleDataMission.Rule(
                                    type = 1,
                                    name = "完成1次",
                                    target = 1,
                                    param = ""
                                ),
                                tagNames = listOf("新手必做")
                            )
                        ),
                        cycle = com.socialplay.gpark.data.model.task.CampaignCycle(
                            type = "1",
                            value = 0
                        ),
                        jumper = CampaignModuleDataMission.Jumper(
                            imageUrl = "https://img.metaapp.io/demo/jump_create.png",
                            jumpParam = "editor",
                            jumpType = "router",
                            name = "去创建"
                        ),
                        eventIds = listOf("evt_create_avatar")
                    ),
                ),
                platCoinNum = 4000,
            )
        )

        // 日常任务模块
        val dailyMissionModule = CampaignModule(
            id = 1002,
            description = "每日任务，完成即可领积分",
            validDateType = 1,
            endTime = baseInfo.endTime,
            name = "日常任务",
            startTime = baseInfo.startTime,
            style = "card",
            type = CampaignModuleData.TYPE_MISSION_DAILY,
            customizedData = CampaignModuleDataMission(
                missions = listOf(
                    CampaignModuleDataMission.MissionDetail(
                        name = "分享活动",
                        description = "分享活动到社交平台",
                        groupId = "g1",
                        intro = "分享即可领取",
                        targetType = 2,
                        receiveType = 1,
                        signUpType = 2,
                        timesLimitCycle = 1,
                        timesLimitTotal = 99,
                        missionTargets = (1..8).map { idx ->
                            CampaignModuleDataMission.MissionTarget(
                                receiveStatus = if (idx < 3) {
                                    CampaignModuleDataMission.MissionTarget.RECEIVE_STATUS_RECEIVED
                                } else {
                                    CampaignModuleDataMission.MissionTarget.RECEIVE_STATUS_UNRECEIVED
                                },
                                missionId = 6000 + idx,
                                missionStatus = if (idx < 4) {
                                    CampaignModuleDataMission.MissionTarget.MISSION_STATUS_COMPLETED
                                } else {
                                    CampaignModuleDataMission.MissionTarget.MISSION_STATUS_UNCOMPLETED
                                },
                                reward = CampaignModuleDataMission.Reward(
                                    rewardType = if (idx % 2 == 0) "le_integral" else "pd_coin",
                                    rewardRelateId = "",
                                    rewardName = if (idx % 2 == 0) "积分" else "金币",
                                    rewardCount = 10L * idx,
                                    rewardIcon = "https://img.metaapp.io/demo/reward_${idx}.png",
                                    rewardParam = null
                                ),
                                rule = CampaignModuleDataMission.Rule(
                                    type = 1,
                                    name = "完成1次",
                                    target = (30 * idx).toLong(),
                                    param = ""
                                ),
                                tagNames = listOf("每日")
                            )
                        }.toList(),
                        cycle = com.socialplay.gpark.data.model.task.CampaignCycle(
                            type = "2",
                            value = 1
                        ),
                        jumper = CampaignModuleDataMission.Jumper(
                            imageUrl = "https://img.metaapp.io/demo/jump_share.png",
                            jumpParam = "share",
                            jumpType = "router",
                            name = "去分享"
                        ),
                        eventIds = listOf("evt_share")
                    ),
                    CampaignModuleDataMission.MissionDetail(
                        name = "分享活动",
                        description = "分享活动到社交平台",
                        groupId = "g2",
                        intro = "分享即可领取",
                        targetType = 1,
                        receiveType = 1,
                        signUpType = 2,
                        timesLimitCycle = 1,
                        timesLimitTotal = 99,
                        missionTargets = listOf(
                            CampaignModuleDataMission.MissionTarget(
                                receiveStatus = CampaignModuleDataMission.MissionTarget.RECEIVE_STATUS_UNRECEIVED,
                                missionId = 6001,
                                missionStatus = CampaignModuleDataMission.MissionTarget.MISSION_STATUS_UNCOMPLETED,
                                reward = CampaignModuleDataMission.Reward(
                                    rewardType = "le_integral",
                                    rewardRelateId = "",
                                    rewardName = "积分",
                                    rewardCount = 30,
                                    rewardIcon = "https://img.metaapp.io/demo/point.png",
                                    rewardParam = null
                                ),
                                rule = CampaignModuleDataMission.Rule(
                                    type = 1,
                                    name = "完成1次",
                                    target = 1,
                                    param = ""
                                ),
                                tagNames = listOf("每日")
                            )
                        ),
                        cycle = com.socialplay.gpark.data.model.task.CampaignCycle(
                            type = "2",
                            value = 1
                        ),
                        jumper = CampaignModuleDataMission.Jumper(
                            imageUrl = "https://img.metaapp.io/demo/jump_share.png",
                            jumpParam = "share",
                            jumpType = "router",
                            name = "去分享"
                        ),
                        eventIds = listOf("evt_share")
                    )
                )
            )
        )

        val response = CampaignDetailResponse(
            baseInfo = baseInfo,
            modules = listOf(checkInModule, newcomerMissionModule, dailyMissionModule),
            userRewardStatus = CampaignUserRewardStatus(
                hasUnreadReward = "false",
                hasPostUnreadReward = "false",
                hasFailReward = "false",
                unreadPostIdList = emptyList()
            ),
            userActivityStatus = CampaignUserActivityStatus(
                stateBits = 17,
                entered = true,
                shared = false,
                authorized = true,
                newUser = false,
                returnUser = false,
                subscribed = false
            )
        )

        delay(300)
        setState {
            copy(
                detail = Success(response),
                checkInRequestSuccess = null,
                checkInSuccess = null,
                rewardObtainRequestSuccess = null,
                rewardObtainSuccess = null,
            )
        }
    }
}