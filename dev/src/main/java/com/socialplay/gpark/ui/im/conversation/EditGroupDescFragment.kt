package com.socialplay.gpark.ui.im.conversation

import android.os.Bundle
import android.os.SystemClock
import android.text.TextWatcher
import android.text.method.ScrollingMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.navArgs
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.databinding.FragmentEditGroupDescBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.util.DoubleLineBreakCallbackFilter
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.LengthCallbackFilter
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.extension.enableAllWithAlpha
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.ifNullOrEmpty

class EditGroupDescFragment :
    BaseFragment<FragmentEditGroupDescBinding>(R.layout.fragment_edit_group_desc) {
    private val args by navArgs<EditGroupDescFragmentArgs>()
    private val vm: EditGroupChatInfoViewModel by fragmentViewModel()
    override val destId: Int = R.id.editGroupDescPage
    private var toastTs = 0L
    private var textWatcher: TextWatcher? = null

    companion object {
        const val MAX_LENGTH = 512
        const val REQUEST_KEY_GROUP_EDIT_DESC = "request_key_group_edit_desc"
        const val KEY_GROUP_EDIT_DESC_RESULT = "key_group_edit_desc_result"
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentEditGroupDescBinding? {
        return FragmentEditGroupDescBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.layoutTitleBar.setOnBackClickedListener {
            navigateUp()
        }
        if (args.enableEdit) {
            binding.tvSave.visible(true)
            binding.tvSave.setOnAntiViolenceClickListener {
                val desc = binding.etInput.text.toString()
                if (desc.isNotEmpty()) {
                    vm.editGroupChatDesc(args.groupId, desc)
                }
            }
            binding.etInput.filters = arrayOf(
                DoubleLineBreakCallbackFilter(),
                LengthCallbackFilter(MAX_LENGTH) {
                    context?.let {
                        val curTs = SystemClock.elapsedRealtime()
                        if (curTs - toastTs > 2000) {
                            toastTs = curTs
                            toast(getString(R.string.up_to_x_chars, MAX_LENGTH))
                        }
                    }
                })
            textWatcher = binding.etInput.doAfterTextChanged { text ->
                val hasContent = !text.isNullOrBlank()
                binding.tvSave.enableAllWithAlpha(hasContent)
                binding.tvWordCount.text = SpannableHelper.Builder()
                    .text("(${text?.length ?: 0}/")
                    .textAppearance(requireContext(), R.style.MetaTextView_S12_PoppinsSemiBold600)
                    .text("${MAX_LENGTH})")
                    .textAppearance(requireContext(), R.style.MetaTextView_S12_PoppinsRegular400)
                    .build()
            }
            vm.onAsync(
                EditGroupChatInfoModelState::editResult,
                deliveryMode = uniqueOnly(),
                onFail = { _, _ ->
                    ToastUtil.showShort(R.string.toast_edit_group_desc_failed)
                },
                onLoading = {

                },
                onSuccess = { groupDetailResult ->
                    if (groupDetailResult.succeeded) {
                        navigateUpWithResult()
                    } else {
                        ToastUtil.showShort(
                            groupDetailResult.message
                                ?: getString(R.string.toast_edit_group_desc_failed)
                        )
                    }
                })
            binding.etInput.setText(args.currentGroupDesc)
        } else {
            binding.etInput.setText(args.currentGroupDesc.ifNullOrEmpty { getString(R.string.default_group_desc) })
            // 禁止编辑
            binding.etInput.keyListener = null
            // 禁止获取焦点
            binding.etInput.isFocusable = false
            // 禁止触摸获取焦点
            binding.etInput.setFocusableInTouchMode(false)
            // 允许滚动
            binding.etInput.movementMethod = ScrollingMovementMethod.getInstance()
        }
    }

    private var showKeyboardFirstTime = true
    override fun onResume() {
        super.onResume()
        if (showKeyboardFirstTime) {
            showKeyboardFirstTime = false
            InputUtil.showSoftBoard(binding.etInput)
        }
    }

    private fun navigateUpWithResult() {
        setFragmentResult(REQUEST_KEY_GROUP_EDIT_DESC, Bundle().apply {
            putBoolean(KEY_GROUP_EDIT_DESC_RESULT, true)
        })
        toast(R.string.group_info_submit_success)
        navigateUp()
    }

    override fun onDestroyView() {
        if (textWatcher != null) {
            binding.etInput.removeTextChangedListener(textWatcher)
            textWatcher = null
        }
        super.onDestroyView()
    }

    override fun invalidate() {

    }

    override fun getPageName() = PageNameConstants.FRAGMENT_EDIT_GROUP_DESC
}