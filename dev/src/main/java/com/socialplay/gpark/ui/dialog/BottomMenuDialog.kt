package com.socialplay.gpark.ui.dialog

import android.os.Bundle
import android.os.Parcelable
import android.view.Gravity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.socialplay.gpark.databinding.DialogBottomMenuBinding
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.ui.core.BaseBottomSheetDialogFragment
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.core.views.spacer
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.parcelize.Parcelize

/**
 * 通用底部菜单的配置数据
 */
@Parcelize
data class MenuInfo(
    val id: Int,
    val title: String,
    val iconRes: Int? = null,
    val showRedDot: Boolean = false,
    val showDetailArrow: Boolean = false,
    val showInMenu: Boolean = true
) : Parcelable {
    companion object {
        const val ID_ADD_FRIEND = 1
        const val ID_CREATE_GROUP = 2
        const val ID_SEARCH_GROUP = 3
    }
}

@Parcelize
data class BottomMenuDialogArgs(
    val title: String? = null,
    val menus: List<MenuInfo>
) : Parcelable

class BottomMenuDialog : BaseBottomSheetDialogFragment() {

    override val binding: DialogBottomMenuBinding by viewBinding(DialogBottomMenuBinding::inflate)

    companion object {
        fun show(
            fragment: Fragment,
            args: BottomMenuDialogArgs,
            onItemClick: (MenuInfo) -> Unit
        ) {
            BottomMenuDialog().apply {
                arguments = args.asMavericksArgs()
                this.onItemClick = onItemClick
            }.show(fragment.childFragmentManager, "BottomMenuDialog")
        }
    }

    private val args: BottomMenuDialogArgs by args()
    private var onItemClick: ((MenuInfo) -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 平板上使用全高，避免横屏时内容显示不全
        if (isPad) {
            heightPercent = 1.0f
        }
    }

    private val controller by lazy {
        simpleController {
            val visibleMenus = args.menus.filter { it.showInMenu }
            val size = visibleMenus.size
            visibleMenus.forEachIndexed { index, menu ->
                if (index > 0 && index < size) {
                    spacer(height = dp(12))
                }
                add {
                    BottomMenuItemModel(menu) {
                        onItemClick?.invoke(menu)
                        dismiss()
                    }.id("BottomMenuItem-${menu.id}")
                }
            }
        }
    }

    override fun init() {
        if(isPad) {
            binding.rootContainer.updateLayoutParams<ConstraintLayout.LayoutParams> {
                val maxWidth = (ScreenUtil.getScreenWidth(requireContext()) * 0.85f).toInt()
                matchConstraintMaxWidth = maxWidth
            }
        }
        skipCollapsed()
        val title = args.title
        if (title.isNullOrEmpty()) {
            binding.tvTitle.gone()
            binding.vTitlePlaceholder.visible()
        } else {
            binding.vTitlePlaceholder.gone()
            binding.tvTitle.visible(true)
            binding.tvTitle.text = title
        }

        binding.root.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }

        binding.rootContainer.setOnAntiViolenceClickListener {
            // 避免触发binding.root的点击事件
        }

        binding.ivClose.setOnAntiViolenceClickListener {
            dismissAllowingStateLoss()
        }

        // 需要主动触发一次构建，避免无 VM 订阅时不自动触发
        binding.rvMenu.setControllerAndBuildModels(controller)
    }

    override fun needCountTime(): Boolean = false
    override fun getPageName(): String = PageNameConstants.FRAGMENT_LOGIN_HELP_DIALOG
    override fun gravity(): Int = Gravity.BOTTOM
}

