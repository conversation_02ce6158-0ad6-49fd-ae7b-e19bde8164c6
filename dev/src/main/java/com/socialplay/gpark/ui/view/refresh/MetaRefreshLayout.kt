package com.socialplay.gpark.ui.view.refresh

import android.content.Context
import android.graphics.ColorFilter
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.util.AttributeSet
import android.view.ViewGroup
import com.airbnb.lottie.LottieAnimationView
import com.airbnb.lottie.LottieDrawable
import com.airbnb.lottie.LottieProperty
import com.airbnb.lottie.SimpleColorFilter
import com.airbnb.lottie.model.KeyPath
import com.airbnb.lottie.value.LottieValueCallback
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.socialplay.gpark.R
import com.socialplay.gpark.util.SimpleVibratorUtil
import com.socialplay.gpark.util.ThreadHelper
import com.socialplay.gpark.util.extension.dp
import timber.log.Timber

/**
 * create by: bin on 2023/2/6
 */
open class MetaRefreshLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyle: Int = 0) : SimpleSwipeRefreshLayout(context, attrs, defStyle) {
    private var circleProgressColorFilter: PorterDuffColorFilter? = null
    private var animationFile: String = "lottie_loading.zip"
    private var gif: Int = -1
    private val lottieAnimationView by lazy {
        LottieAnimationView(context).apply {
            if (animationFile.isEmpty() && gif == -1) {
                throw IllegalStateException("Could not resolve an animation for your pull to refresh layout")
            }

            setFailureListener { Timber.e(it) }

            // 子线程预加载xml时, 这里会出现闪退
            ThreadHelper.runOnUiThreadCatching {
                if (animationFile.isNotEmpty()) {
                    repeatCount = LottieDrawable.INFINITE
                    setAnimation(animationFile)

                    if (!isHomePage()) this.playAnimation()
                    else pauseAnimation()
                } else {
                    Glide.with(this).asGif().load(gif).into(this)
                }
            }

            layoutParams = LayoutParams(ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp(35f)))

            addValueCallback(
                KeyPath("**"),
                LottieProperty.COLOR_FILTER
            ) {
                circleProgressColorFilter
            }
        }
    }

    var canBoom = true

    init {
        context.theme.obtainStyledAttributes(attrs, R.styleable.MetaRefreshLayout, defStyle, 0).let { style ->
            animationFile = style.getString(R.styleable.MetaRefreshLayout_lottie_assets_name) ?: animationFile
            gif = style.getResourceId(R.styleable.MetaRefreshLayout_gif, gif)
            addView(lottieAnimationView)
            style.recycle()
        }

        addProgressListener {
            if (!isHomePage()) {
                resumeAnimation()
            } else {
                if (it > 0.4f) lottieAnimationView.progress = (it - 0.4f) / 16
                else lottieAnimationView.progress = 0f
            }

            if (!canBoom && it < 0.3f) {
                canBoom = true
            }

            if (it > 0.5f && canBoom) {
                canBoom = false
                SimpleVibratorUtil.vibrateTick()
            }

        }

        // 性能监控：下拉刷新控件触摸事件
        setOnTouchListener { _, event ->
            val currentTime = System.currentTimeMillis()
            val actionName = when (event.action) {
                android.view.MotionEvent.ACTION_DOWN -> "ACTION_DOWN"
                android.view.MotionEvent.ACTION_MOVE -> "ACTION_MOVE"
                android.view.MotionEvent.ACTION_UP -> "ACTION_UP"
                android.view.MotionEvent.ACTION_CANCEL -> "ACTION_CANCEL"
                else -> "ACTION_${event.action}"
            }
            
            // 获取调用栈信息
            val stackTrace = Thread.currentThread().stackTrace
            val callerInfo = if (stackTrace.size > 3) {
                val caller = stackTrace[3]
                " [CALLER:${caller.className}.${caller.methodName}]"
            } else ""
            
            Timber.tag("Performance").d("Refresh layout touch event - Action: $actionName, Position: (${event.x}, ${event.y})${callerInfo}")
            false // 不消费事件
        }

    }

    fun setCircleProgressColor(color: Int) {
        circleProgressColorFilter = SimpleColorFilter(color)
    }

    override fun stopRefreshing() {
        super.stopRefreshing()
        if (isHomePage()) {
            resumeAnimation()
        }
    }

    override fun isHomePage() = "pull_to_refresh.zip" == animationFile

    private fun resumeAnimation() {
        if (!lottieAnimationView.isAnimating) {
            lottieAnimationView.resumeAnimation()
            Timber.i("resumeAnimation")
        }
        val gifDrawable = lottieAnimationView.drawable as? GifDrawable
        gifDrawable?.takeIf { !it.isRunning }?.start()
    }

    private fun pauseAnimation() {
        (lottieAnimationView.drawable as? GifDrawable)?.stop()
        lottieAnimationView.pauseAnimation()
        Timber.i("pauseAnimation")
    }

    fun isAnimating(): Boolean {
        return lottieAnimationView.isAnimating
    }

    fun isAnimateFinished(): Boolean {
        return offsetY == 0F
    }

    fun setColorSchemeResources(color: Int) {
        val filter = PorterDuffColorFilter(color, PorterDuff.Mode.SRC_ATOP)
        val callback: LottieValueCallback<ColorFilter> = LottieValueCallback(filter)
        lottieAnimationView.addValueCallback(KeyPath("**"), LottieProperty.COLOR_FILTER, callback)
    }

    override fun endAnimation() {
        super.endAnimation()
        if (offsetY == 0F) {
            pauseAnimation()
        }
    }

    override fun startRefreshing() {
        super.startRefreshing()
        if (!isHomePage()) resumeAnimation()
    }

    override fun onFinishInflate() {
        super.onFinishInflate()

    }

    fun changeAnimationFile(file: String) {
        if(file.isEmpty()) return
        animationFile = file
        lottieAnimationView.setAnimation(file)
        lottieAnimationView.cancelAnimation()
        lottieAnimationView.playAnimation()
    }
}