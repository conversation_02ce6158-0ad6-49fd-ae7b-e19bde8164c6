package com.socialplay.gpark.ui.recommend.choice

import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView

import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.EpoxyModel
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.room.ChatRoomInfo
import com.socialplay.gpark.databinding.AdapterItemRoomBinding
import com.socialplay.gpark.databinding.ItemChoiceTitleMoreBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.setHorizontalScrollPadding
import com.socialplay.gpark.ui.room.RoomCardUtil
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setSize
import com.socialplay.gpark.util.extension.setWidth
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unsetOnClick

import com.socialplay.gpark.util.extension.visible
import org.koin.core.context.GlobalContext

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/01
 *     desc   :
 * </pre>
 */
fun MetaModelCollector.choiceRoomCard(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val rooms = card.roomList
    if (rooms.isNullOrEmpty()) return
    add(
        ChoiceTitleMoreItem(card, spanSize, listener).id("ChoiceRoomCardTitle-$cardPosition")
            .spanSizeOverride { _, _, _ -> spanSize }
    )
    carouselNoSnapWrapBuilder {
        id("ChoiceRoomCardList-$cardPosition")

        setHorizontalScrollPadding()
        hasFixedSize(true)
        initialPrefetchItemCount(1)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        // 在GridLayoutManager中，spanSize通过spanSizeOverride控制
        // Carousel需要占满整行
        spanSizeOverride { _, _, _ -> spanSize }
        rooms.forEachIndexed { position, room ->
            add(
                ChoiceRoomCardItem(
                    room,
                    position,
                    card,
                    cardPosition,
                    false,
                    RoomCardUtil.getRoomCardWidth(),
                    spanSize,
                    listener
                ).id("ChoiceRoomCard-${card.cardId}-$cardPosition-$position-${room.roomId}")
            )
        }
    }
}

data class ChoiceTitleMoreItem(
    val card: ChoiceCardInfo?,
    val spanSize: Int,
    val listener: IChoiceListener?,
    val needRedDotKey: String? = null,
    val needMore: Boolean = true,
    val title: String? = null,
    val paddingBottom: Int = 0.dp
) : ViewBindingItemModel<ItemChoiceTitleMoreBinding>(
    R.layout.item_choice_title_more,
    ItemChoiceTitleMoreBinding::bind
) {
    var vRedDot: View? = null

    override fun ItemChoiceTitleMoreBinding.onBind() {
        // 下版本不需要单独设置了，所以先提前注释掉，提高点性能
//        if (paddingBottom != 0) {
//            root.setMargin(bottom = paddingBottom)
//        }
        <EMAIL> = vRedDot
        val metaKV: MetaKV = GlobalContext.get().get()
        tvCardTitle.text = card?.cardName ?: title ?: ""
        if (needMore) {
            val showRedDotFromHomeFeed = metaKV.appKV.isShowRedDotFromHomeFeed(needRedDotKey)
            if (!showRedDotFromHomeFeed) {
                // 展示过了或者不展示，隐藏起来
                vRedDot.gone()
            } else {
                vRedDot.visible()
            }
            tvCardMore.visible()
            ivArrowRight.visible()
            vClick.setOnAntiViolenceClickListener {
                vRedDot.gone()
                listener?.onMoreClick(card)
                if (showRedDotFromHomeFeed) {
                    metaKV.appKV.isShowRedDotFromHomeFeedClick(needRedDotKey)
                }
            }
        } else {
            vRedDot.gone()
            tvCardMore.gone()
            ivArrowRight.gone()
            tvCardMore.unsetOnClick()
            vClick.unsetOnClick()
        }
    }

    override fun ItemChoiceTitleMoreBinding.onUnbind() {
        tvCardMore.unsetOnClick()
    }

    fun hideRedDot() {
        vRedDot?.gone()
    }
}

data class ChoiceRoomCardItem(
    val item: ChatRoomInfo,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val matchParent: Boolean,
    val fitItemWidth: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<AdapterItemRoomBinding>(
    R.layout.adapter_item_room,
    AdapterItemRoomBinding::bind
) {

    override fun AdapterItemRoomBinding.onBind(payloads: MutableList<Any>) {
        context.toast("payloads")
        onBind()
    }

    override fun AdapterItemRoomBinding.onBind(previouslyBoundModel: EpoxyModel<*>) {
        context.toast("previouslyBoundModel")
        onBind()
    }

    override fun AdapterItemRoomBinding.onBind() {
        if (matchParent) {
            root.setSize(
                RecyclerView.LayoutParams.MATCH_PARENT,
                RecyclerView.LayoutParams.WRAP_CONTENT
            )
            clContent.setWidth(ConstraintLayout.LayoutParams.MATCH_PARENT)
        } else {
            root.setSize(
                RecyclerView.LayoutParams.WRAP_CONTENT,
                RecyclerView.LayoutParams.WRAP_CONTENT
            )
            clContent.setWidth(fitItemWidth)
        }
        root.setMargin(right = dp(10))
        RoomCardUtil.setImageBg(listener.getGlideOrNull(), this, item)
        RoomCardUtil.setRoomName(matchParent, fitItemWidth, context, this, item)
        RoomCardUtil.setRoomTag(context, this, item)
        RoomCardUtil.setRoomPlayerCount(context, this, item)
        RoomCardUtil.setRoomMember(listener.getGlideOrNull(), this, item)
        RoomCardUtil.setRoomStyle(this, item)
        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}