package com.socialplay.gpark.ui.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.CallSuper
import androidx.core.view.postDelayed
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.viewbinding.ViewBinding
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.function.analytics.handle.AppLaunchAnalytics
import com.socialplay.gpark.function.analytics.observer.LifecycleObserver
import com.socialplay.gpark.function.apm.PageMonitor
import com.socialplay.gpark.function.apm.onPageCreate
import com.socialplay.gpark.function.apm.onPageDestroyView
import com.socialplay.gpark.function.apm.onPageResume
import com.socialplay.gpark.function.apm.onPageStart
import com.socialplay.gpark.function.apm.onPageViewCreated
import com.socialplay.gpark.function.apm.page.IPageMonitor
import com.socialplay.gpark.function.apm.page.view.ISpeedLayout
import com.socialplay.gpark.ui.mgs.danmu.advanced.PAGE_ANIMATION_DURATION
import com.socialplay.gpark.util.DeviceUtil
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.extension.OnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.navColor
import com.socialplay.gpark.util.extension.navigateUp
import com.socialplay.gpark.util.extension.setPaddingEx
import timber.log.Timber
import kotlin.math.max

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/05/07
 * desc   :
 * </pre>
 */


abstract class BaseFragment<T: ViewBinding> : Fragment(), IPageMonitor {

    private var _binding: T? = null
    protected open val binding: T get() = _binding!!
    private var isLoadFirstData = false
    protected open val destId: Int = 0
    private var hasNavUp = false

    protected open var navColorRes: Int = R.color.white
    private var nextNavColor: Int = 0
    protected open val isPostInit: Boolean = false

    protected var glide: RequestManager? = null
        get() {
            if (field == null && enableGlide) {
                field = Glide.with(this)
            }
            return field
        }
    private var enableGlide: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        onPageCreate()
        super.onCreate(savedInstanceState)
        AppLaunchAnalytics.handleFragmentCreated(this)
        if (!ignoreUsingTime()) {
            LifecycleObserver(this, getFragmentName())
        }
    }

    abstract fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): T?

    @CallSuper
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = onCreateViewBinding(inflater, container, savedInstanceState)
        val (cancelFSW4HarmonyOs, padStatusBar) = cancelFitsSystemWindows4HarmonyOs()
        if (cancelFSW4HarmonyOs && DeviceUtil.isHarmonyOs()) {
            binding.root.fitsSystemWindows = false
            if (padStatusBar) {
                kotlin.runCatching {
                    binding.root.setPaddingEx(top = max(
                        requireActivity().window.decorView.rootWindowInsets.systemWindowInsetTop,
                        StatusBarUtil.getStatusBarHeight(requireContext())
                    ))
                }
            }
        }
        return binding.root.apply {
            if (this is ISpeedLayout) {
                setPageConfig(
                    this@BaseFragment::class.java.simpleName,
                    <EMAIL>(),
                    <EMAIL>()
                )
            } else {
                if (PageMonitor.getConfig(this@BaseFragment::class.java.simpleName) != null) {
                    Timber.tag("PageMonitor")
                        .e("${this@BaseFragment::class.java.simpleName} not set page monitor layout!")
                }
            }
            setOnClickListener {  }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        onPageViewCreated()
        super.onViewCreated(view, savedInstanceState)
        enableGlide = true
        if (nextNavColor == 0) {
            nextNavColor = getColorByRes(navColorRes)
        }
        if (isPostInit) {
            view.postDelayed(PAGE_ANIMATION_DURATION) {
                initWrapper()
            }
        } else {
            initWrapper()
        }
    }

    private fun initWrapper() {
        try {
            if (!isBindingAvailable()) {
                return
            }
            init()
            if (!isLoadFirstData) {
                isLoadFirstData = true
                loadFirstData()
            }
        } catch (e: Exception) {
            Timber.tag("PageMonitor").e(e, "init error")
        }
    }

    abstract fun init()
    abstract fun loadFirstData()
    abstract fun getFragmentName(): String
    open fun ignoreUsingTime() = false
    override fun onResume() {
        OnAntiViolenceClickListener.resetTime()
        onPageResume()
        super.onResume()
        if (isFirstPage()) {
            AppLaunchAnalytics.handleAppBootTime(requireActivity())
        }
        setStatusBarTextColor(isStatusBarTextDark())
        requireActivity().window.navColor = nextNavColor
    }

    open fun isStatusBarTextDark(): Boolean {
        return true
    }

    open fun setStatusBarTextColor(isDark: Boolean) {
        if (isDark) {
            StatusBarUtil.setLightMode(requireActivity())
        } else {
            StatusBarUtil.setDarkMode(requireActivity())
        }
    }

    override fun onStart() {
        onPageStart()
        super.onStart()
    }

    override fun onDestroyView() {
        onPageDestroyView()
        enableGlide = false
        glide = null
        super.onDestroyView()
        _binding = null
    }

    override fun onDetach() {
        super.onDetach()
        isLoadFirstData = false
    }

    open fun isBindingAvailable(): Boolean {
        return _binding != null
    }

    protected open fun cancelFitsSystemWindows4HarmonyOs() = false to false

    protected fun navigateUp() {
        if (hasNavUp) return
        if (destId != 0) {
            hasNavUp = navigateUp(destId)
            if (hasNavUp) return
        }
        hasNavUp = findNavController().navigateUp()
    }
}