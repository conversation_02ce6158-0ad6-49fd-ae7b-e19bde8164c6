package com.socialplay.gpark.ui.profile.ugc

import android.content.res.Configuration
import android.graphics.Rect
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.epoxy.EpoxyController
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.args
import com.airbnb.mvrx.asMavericksArgs
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.parentFragmentViewModel
import com.airbnb.mvrx.withState
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.editor.ProfileMapTab
import com.socialplay.gpark.data.model.editor.UgcGameInfo
import com.socialplay.gpark.databinding.FragmentProfilePublishedUgcBinding
import com.socialplay.gpark.databinding.PopUpProfileMapsBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseRecyclerViewFragment
import com.socialplay.gpark.ui.core.itemsPerRow
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.editor.create.EditorCreateMineParentFragment
import com.socialplay.gpark.ui.editor.create.EditorCreateV2Fragment
import com.socialplay.gpark.ui.editor.create.EditorCreateViewModel
import com.socialplay.gpark.ui.gamedetail.unify.GameDetailEditViewModel
import com.socialplay.gpark.ui.profile.home.ProfileViewModel
import com.socialplay.gpark.ui.view.PopupWindowCompat
import com.socialplay.gpark.util.PadAdapterUtil
import com.socialplay.gpark.util.extension.compoundDrawables
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.visible
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import org.koin.androidx.viewmodel.ext.android.viewModel

/**
 * Created by bo.li
 * Date: 2024/8/8
 * Desc: profile页-已发布ugc tab
 */
@Parcelize
data class ProfilePublishedUgcFragmentArgs(val isMe: Boolean, val otherUid: String) : Parcelable

class ProfilePublishedUgcFragment :
    BaseRecyclerViewFragment<FragmentProfilePublishedUgcBinding>(R.layout.fragment_profile_published_ugc) {

    private val args by args<ProfilePublishedUgcFragmentArgs>()
    private val viewModel: ProfilePublishedUgcViewModel by fragmentViewModel()
    private val profileViewModel: ProfileViewModel by parentFragmentViewModel()
    private val editorCreateViewModel by viewModel<EditorCreateViewModel>()
    private val sharedViewModel: GameDetailEditViewModel by activityViewModels()

    private var listListener: IProfileUgcItemAction? = null

    private val isMe: Boolean get() = args.isMe

    private val popupBinding by lazy { PopUpProfileMapsBinding.inflate(layoutInflater) }
    private lateinit var popupWindow: PopupWindowCompat
    private lateinit var epoxyVisibilityTracker: EpoxyVisibilityTracker

    private var otherUid: String = ""

    companion object {
        fun newInstance(args: ProfilePublishedUgcFragmentArgs): ProfilePublishedUgcFragment {
            return ProfilePublishedUgcFragment().apply {
                arguments = args.asMavericksArgs()
            }
        }
    }

    override val recyclerView: EpoxyRecyclerView
        get() = binding.rvUgc

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.apiMonitor(
            this,
            ProfilePublishedUgcModelState::loadMore
        )
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentProfilePublishedUgcBinding? {
        return FragmentProfilePublishedUgcBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        otherUid = profileViewModel.oldState.uuid
        viewModel.updateUuid(otherUid)
        if (args.isMe) {
            viewModel.accountInteractor.accountLiveData.observe(viewLifecycleOwner) {
                val uuid = it?.uuid ?: return@observe
                if (otherUid != uuid) {
                    otherUid = uuid
                    viewModel.updateUuid(otherUid)
                }
            }
        }
        listListener = getInitListener()
        epoxyVisibilityTracker = EpoxyVisibilityTracker().apply {
            attach(recyclerView)
        }
        val gridLayoutManager =
            GridLayoutManager(requireContext(), itemsPerRow, GridLayoutManager.VERTICAL, false)
        recyclerView.layoutManager = gridLayoutManager

        binding.loadingUgc.setEmptyBtnClick {
            if (viewModel.oldState.gameType == ProfilePublishedUgcModelState.GAME_TYPE_PGC && isMe) {
                MetaRouter.Web.navigate(
                    this,
                    null,
                    BuildConfig.EDITOR_OFFICIAL_WEBSITE,
                    isWebOutside = true
                )
            } else {
                Analytics.track(EventConstants.PROFILE_UGC_CREATE)
                MetaRouter.MobileEditor.creation(this)
            }
        }
        binding.loadingUgc.setVerticalBias(0.08f)
        binding.layoutWaitingReleased.setOnAntiViolenceClickListener {
            profileViewModel.trackClick("draft", null, null)
            MetaRouter.MobileEditor.creation(
                this,
                initTab = EditorCreateV2Fragment.TAB_MINE,
                categoryId = EditorCreateMineParentFragment.TYPE_DRAFT
            )
        }
        binding.loadingUgc.btnEmpty().apply {
            setBackgroundResource(R.drawable.bg_ffdc1c_round_40)
            setTextColorByRes(R.color.color_1A1A1A)
        }
        initPopup()
        initData()
    }

    private fun initData() {
        viewModel.registerAsyncErrorToast(ProfilePublishedUgcModelState::asyncList)
        viewModel.registerAsyncErrorToast(ProfilePublishedUgcModelState::deleteUgcResult)
        viewModel.registerAsyncErrorToast(ProfilePublishedUgcModelState::pinResult)
        viewModel.setupRefreshLoading(
            ProfilePublishedUgcModelState::asyncList,
            binding.loadingUgc,
            binding.refresh,
            {
                if (viewModel.oldState.gameType == ProfilePublishedUgcModelState.GAME_TYPE_PGC && isMe) {
                    binding.loadingUgc.showEmpty(
                        getString(R.string.profile_map_empty_professional_desc),
                        R.drawable.icon_no_recent_activity,
                        true,
                        getString(R.string.profile_map_empty_professional_btn)
                    )
                } else {
                    binding.loadingUgc.showEmpty(
                        getString(R.string.no_data),
                        R.drawable.icon_no_recent_activity,
                        isMe,
                        getString(R.string.create_v2_build)
                    )
                }
            }
        ) {
            viewModel.refresh()
        }
        viewModel.onEach(ProfilePublishedUgcModelState::list) {
            updateBuildBtnVisible(isResumed && isMe && it.isNotEmpty())
        }
        if (isMe) {
            editorCreateViewModel.creationCountData.observe(viewLifecycleOwner) { creationCountData ->
                val draftCount = creationCountData?.draftCount
                if (draftCount != null && draftCount > 0) {
                    binding.layoutWaitingReleased.visible()
                    binding.tvWaitingReleased.setTextWithArgs(
                        R.string.map_un_published_tips_text,
                        draftCount.toString()
                    )
                } else {
                    binding.layoutWaitingReleased.gone()
                }
            }
        } else {
            binding.layoutWaitingReleased.gone()
        }
        // ViewPage2 内部的 fragment 跳转到下一个 fragment, 没办法通过 setFragmentResultListener 获取 fragment 的结果
        // 所以使用 ViewModel 来获取 fragment 的结果
        lifecycleScope.launch {
            sharedViewModel.pinTopResult.collect { pinTopResult ->
                val goDetailGame = mGoDetailGame
                val goDetailGamePosition = mGoDetailGamePosition
                if (goDetailGame != null && goDetailGamePosition != null && goDetailGame.id == pinTopResult.gameId) {
                    viewModel.refresh()
                    mGoDetailGame = null
                    mGoDetailGamePosition = null
                }
            }
        }
        lifecycleScope.launch {
            sharedViewModel.deleteResult.collect { deleteResult ->
                viewModel.updateDeleteResult(deleteResult.gameId)
            }
        }
        editorCreateViewModel.refresh()
    }

    private var mGoDetailGame: UgcGameInfo.Games? = null
    private var mGoDetailGamePosition: Int? = null

    private fun getInitListener() = object : IProfileUgcItemAction {
        override fun showPopupMore(item: UgcGameInfo.Games, position: Int, anchorView: View) {
            val pinRes = if (item.topOn) {
                popupBinding.tvPinBtn.compoundDrawables(left = R.drawable.ic_profile_maps_unpin)
                R.string.profile_map_operation_unpin_creation
            } else {
                popupBinding.tvPinBtn.compoundDrawables(left = R.drawable.ic_profile_maps_pin)
                R.string.profile_map_operation_pin_creation
            }
            popupBinding.tvPinBtn.setText(pinRes)
            popupBinding.tvPinBtn.setOnAntiViolenceClickListener {
                viewModel.pin(item, position)
                popupWindow.dismiss()
            }
            popupBinding.tvDeleteBtn.setHint(pinRes)
            popupBinding.tvDeleteBtn.setOnAntiViolenceClickListener {
                showDeleteSaveDialog(item)
                popupWindow.dismiss()
            }
            if (item.isPgc) {
                popupBinding.tvDeleteBtn.gone()
                popupBinding.vDivider.gone()
                popupBinding.tvPinBtn.setHint(pinRes)
            } else {
                popupBinding.tvDeleteBtn.visible()
                popupBinding.vDivider.visible()
                popupBinding.tvPinBtn.setHint(R.string.delete_cap)
            }
            popupBinding.cv.measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
            val rect = Rect()
            anchorView.getGlobalVisibleRect(rect)
            val bottom = rect.bottom + -binding.dp(2) + popupBinding.cv.measuredHeight
            binding.refresh.getGlobalVisibleRect(rect)
            val y = if (bottom > rect.bottom) {
                -binding.dp(4) - anchorView.height.coerceAtLeast(anchorView.measuredHeight) - popupBinding.cv.measuredHeight
            } else {
                -binding.dp(2)
            }
            val x = -popupBinding.cv.measuredWidth + anchorView.measuredWidth - 4.dp
            popupWindow.showAsDropDownByLocation(anchorView, x, y)
        }

        override fun goGameDetail(item: UgcGameInfo.Games, position: Int) {
            Analytics.track(
                EventConstants.USERMAPS_LIST_CLICK,
                "type" to when (viewModel.oldState.gameType) {
                    ProfilePublishedUgcModelState.GAME_TYPE_UGC -> 2
                    ProfilePublishedUgcModelState.GAME_TYPE_PGC -> 3
                    else -> 1
                }
            )
            mGoDetailGame = item
            mGoDetailGamePosition = position
            if (item.isUgc) {
                Analytics.track(
                    EventConstants.PROFILE_UGC_LIST,
                    EventParamConstants.KEY_UGCID to item.id,
                    EventParamConstants.KEY_UGCNAME to item.ugcGameName.toString(),
                    EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_UGC_CLICK,
                    EventParamConstants.KEY_RANK to position + 1,
                )
                MetaRouter.MobileEditor.ugcDetail(
                    this@ProfilePublishedUgcFragment,
                    item.id,
                    item.availableGameCode,
                    ResIdBean().setCategoryID(if (isMe) CategoryId.CATEGORY_ID_HOME_PAGE_UGC_MINE else CategoryId.CATEGORY_ID_HOME_PAGE_UGC_OTHERS)
                        .setGameId(item.id)
                )
            } else {
                MetaRouter.GameDetail.navigate(
                    this@ProfilePublishedUgcFragment,
                    item.gameCode,
                    ResIdBean().setCategoryID(
                        if (isMe) {
                            CategoryId.CATEGORY_ID_HOME_PAGE_UGC_MINE
                        } else {
                            CategoryId.CATEGORY_ID_HOME_PAGE_UGC_OTHERS
                        }
                    ),
                    item.packageName
                )
            }
        }

        override fun onItemVisibilityChange(item: UgcGameInfo.Games, position: Int) {
            Analytics.track(
                EventConstants.USERMAPS_LIST_SHOW,
                "type" to when (viewModel.oldState.gameType) {
                    ProfilePublishedUgcModelState.GAME_TYPE_UGC -> 2
                    ProfilePublishedUgcModelState.GAME_TYPE_PGC -> 3
                    else -> 1
                }
            )
            if (item.isUgc) {
                Analytics.track(
                    EventConstants.PROFILE_UGC_LIST,
                    EventParamConstants.KEY_UGCID to item.id,
                    EventParamConstants.KEY_UGCNAME to item.ugcGameName.toString(),
                    EventParamConstants.KEY_TYPE to EventConstants.Type.TYPE_UGC_SHOW,
                    EventParamConstants.KEY_RANK to position + 1,
                )
            }
        }

        override fun onClickTab(item: ProfileMapTab, position: Int) {
            viewModel.updateTabSelection(position, item.type)
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }

    private fun initPopup() {
        popupBinding.root.setOnAntiViolenceClickListener {
            popupWindow.dismiss()
        }
        popupWindow = PopupWindowCompat(
            popupBinding.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            isTouchable = true
            isOutsideTouchable = true
            isFocusable = true
            isClippingEnabled = false
            animationStyle = 0
        }
    }

    /**
     * 展示删除本地工程弹窗
     */
    private fun showDeleteSaveDialog(item: UgcGameInfo.Games) {
        val confirm = SimpleListData(
            getString(R.string.text_confirm),
            bgResource = R.drawable.bg_common_dialog_confirm,
            textColor = R.color.white
        )
        val cancel = SimpleListData(getString(R.string.dialog_cancel))
        ListDialog()
            .list(mutableListOf(confirm, cancel))
            .content(getString(R.string.delete_creation))
            .image(R.drawable.icon_delete)
            .clickCallback {
                when (it) {
                    confirm -> {
                        // 直接删除
                        if (item.isUgc) {
                            Analytics.track(
                                EventConstants.PROFILE_UGC_DELETE,
                                EventParamConstants.KEY_UGCID to item.id,
                                EventParamConstants.KEY_UGCNAME to item.ugcGameName.toString(),
                                EventParamConstants.KEY_RESULT to EventConstants.Result.RESULT_UGC_DELETE_CONFIRM
                            )
                        }
                        viewModel.deletePublishedUgc(item.id)
                    }

                    else -> {
                        // 取消
                        if (item.isUgc) {
                            Analytics.track(
                                EventConstants.PROFILE_UGC_DELETE,
                                EventParamConstants.KEY_UGCID to item.id,
                                EventParamConstants.KEY_UGCNAME to item.ugcGameName.toString(),
                                EventParamConstants.KEY_RESULT to EventConstants.Result.RESULT_UGC_DELETE_CANCEL
                            )
                        }
                    }
                }
            }.show(childFragmentManager, "CreationDeleteDialog")
    }

    override fun epoxyController(): EpoxyController = simpleController(
        viewModel,
        ProfilePublishedUgcModelState::list,
        ProfilePublishedUgcModelState::loadMore
    ) { list, loadMore ->
        // 列表
        list.forEachIndexed { index, game ->
            viewLifecycleOwner
            publishedUgc(game, index, isMe, viewLifecycleOwner, listListener)
        }
        // 加载更多
        if (list.isNotEmpty()) {
            loadMoreFooter(loadMore, idStr = "ProfileMapsFooter", spanSize = 2, showEnd = false) {
                viewModel.loadMore()
            }
        }
    }

    override fun invalidate() {

    }

    private fun updateBuildBtnVisible(isVisible: Boolean) {
        profileViewModel.updateBuildBtnVisible(isVisible)
    }

    override fun onResume() {
        super.onResume()
        withState(viewModel) {
            updateBuildBtnVisible(isMe && it.list.isNotEmpty())
        }
        epoxyVisibilityTracker.clearVisibilityStates()
        epoxyVisibilityTracker.requestVisibilityCheck()
        viewModel.onPageResume()
    }

    override fun onPause() {
        updateBuildBtnVisible(false)
        super.onPause()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (isBindingAvailable()) {
            PadAdapterUtil.configChangedByGrid(recyclerView, itemsPerRow, epoxyController)
        }
    }

    override fun onDestroyView() {
        listListener = null
        popupWindow.dismiss()
        epoxyVisibilityTracker.detach(recyclerView)
        super.onDestroyView()
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_NAME_PROFILE_TAB_UGC
}
