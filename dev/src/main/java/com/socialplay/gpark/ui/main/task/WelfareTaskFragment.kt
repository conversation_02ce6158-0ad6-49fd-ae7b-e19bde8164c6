package com.socialplay.gpark.ui.main.task

import android.animation.ValueAnimator
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.compose.ui.geometry.Rect
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.net.toUri
import androidx.navigation.fragment.navArgs
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.fragmentViewModel
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.model.task.CampaignDetailResponse
import com.socialplay.gpark.data.model.task.CampaignInfo
import com.socialplay.gpark.data.model.task.CampaignModule
import com.socialplay.gpark.data.model.task.CampaignModuleDataCheckIn
import com.socialplay.gpark.data.model.task.CampaignModuleDataMission
import com.socialplay.gpark.databinding.FragmentWelfareTaskBinding
import com.socialplay.gpark.databinding.LayoutWelfareTaskBalanceBinding
import com.socialplay.gpark.databinding.LayoutWelfareTaskBalancePadBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.AnimeImageDialog
import com.socialplay.gpark.ui.dialog.AnimeImageDialogArgs
import com.socialplay.gpark.ui.dialog.CenterCloseDialog
import com.socialplay.gpark.ui.dialog.CenterCloseDialogArgs
import com.socialplay.gpark.ui.feedback.FeedbackType
import com.socialplay.gpark.ui.view.MetaTextView
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.SoundPlayer
import com.socialplay.gpark.util.SpannableReplaceHelper
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.attach
import com.socialplay.gpark.util.extension.doOnEnd
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.screenWidth
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import org.koin.core.context.GlobalContext

private data class BalanceLayoutBinding(
    val root: ViewGroup,
    val layoutCoinsBalance: ConstraintLayout,
    val layoutPointsBalance: ConstraintLayout,
    val tvCoinsBalance: MetaTextView,
    val tvPointsBalance: MetaTextView,
    val ivRules: ImageView,
) {
    companion object {
        fun bind(binding: LayoutWelfareTaskBalanceBinding): BalanceLayoutBinding {
            return BalanceLayoutBinding(
                root = binding.root,
                layoutCoinsBalance = binding.layoutCoinsBalance,
                layoutPointsBalance = binding.layoutPointsBalance,
                tvCoinsBalance = binding.tvCoinsBalance,
                tvPointsBalance = binding.tvPointsBalance,
                ivRules = binding.ivRules,
            )
        }

        fun bind(binding: LayoutWelfareTaskBalancePadBinding): BalanceLayoutBinding {
            return BalanceLayoutBinding(
                root = binding.root,
                layoutCoinsBalance = binding.layoutCoinsBalance,
                layoutPointsBalance = binding.layoutPointsBalance,
                tvCoinsBalance = binding.tvCoinsBalance,
                tvPointsBalance = binding.tvPointsBalance,
                ivRules = binding.ivRules,
            )
        }
    }
}

class WelfareTaskFragment :
    BaseFragment<FragmentWelfareTaskBinding>(R.layout.fragment_welfare_task) {
    companion object {
        /**
         * @param fromTabLayout 是否来自 TabLayout
         *
         * @param source 来源:
         * home_tab 首页页签
         * home_banner 首页banner
         * discover_banner 精选banner
         * profile_pop 个人页弹窗
         * community_tab 社区页签"
         */
        fun newInstance(fromTabLayout: Boolean = true, source: String): WelfareTaskFragment {
            return WelfareTaskFragment().apply {
                arguments = WelfareTaskFragmentArgs(
                    fromTabLayout = fromTabLayout,
                    source = source,
                ).toBundle()
            }
        }
    }

    private val h5PageConfigInteractor: H5PageConfigInteractor =
        GlobalContext.get().get<H5PageConfigInteractor>()

    private val args by navArgs<WelfareTaskFragmentArgs>()
    private val vm: WelfareTaskViewModel by fragmentViewModel()

    private val checkInController by lazy { buildCheckInController() }
    private val limitedTimeController by lazy { buildLimitedTimeController() }
    private val dailyTasksController by lazy { buildDailyTasksController() }

    private var limitedTimeTaskVisibilityTracker: EpoxyVisibilityTracker? = null
    private var dailyTaskVisibilityTracker: EpoxyVisibilityTracker? = null

    private var balanceBinding: BalanceLayoutBinding? = null
    private var pendingScrollY: Int? = null
    private var lastScrollY: Int? = null

    /**
     * 是否应该触发福利任务页的显示埋点
     */
    private var shouldTrackPageShow = false

    /**
     * 是否应该显示账号不满足的弹窗
     */
    private var shouldShowAccountNotMeetDialog = true

    private var soundPlayer: SoundPlayer? = null

    private val listener = object : IWelfareTaskListener {
        override fun clickMore(
            baseInfo: CampaignInfo,
            module: CampaignModule,
            task: CampaignModuleDataMission.MissionDetail,
            item: CampaignModuleDataMission.MissionTarget?,
        ) {
            if (vm.oldState.meetWelfareTaskRules != true) {
                showAccountNotMeetDialog()
                return
            }

            Analytics.track(
                EventConstants.C_DAILY_TASK_BUTTON_CLICK,
                "id" to (baseInfo.activityId?.toString() ?: ""),
                "module_id" to (module.id?.toString() ?: ""),
                "click_status" to "go",
                "task_type" to (item?.rule?.type?.toString() ?: ""),
                "task_param" to (item?.rule?.param ?: ""),
                "task_target" to (item?.rule?.target?.toString() ?: ""),
                "task_id" to (item?.missionId?.toString() ?: ""),
                "result" to "",
                "fail_reason" to "",
            )
            val jumper = task.jumper?:return
            if (jumper.isToast()) {
                toast(jumper.jumpParam)
            } else if (jumper.isScheme() && !jumper.jumpParam.isNullOrEmpty()) {
                val schemeUri = runCatching { jumper.jumpParam.toUri() }.getOrNull() ?: return
                MetaRouter.Scheme.jumpScheme(
                    this@WelfareTaskFragment,
                    schemeUri,
                    getPageName()
                )
            }
        }

        override fun checkIn(
            baseInfo: CampaignInfo,
            module: CampaignModule,
            task: CampaignModuleDataCheckIn.CurrentCheckInDetail,
            item: CampaignModuleDataCheckIn.CheckInReward,
            index: Int,
            viewRect: Rect,
        ) {
            if (vm.oldState.meetWelfareTaskRules != true) {
                showAccountNotMeetDialog()
                return
            }
            val activityId = baseInfo.activityId
            val moduleId = module.id ?: return
            val checkinId = task.checkinId ?: return
            val rewardId = item.id ?: return
            vm.checkIn(
                activityId = activityId,
                moduleId = moduleId,
                checkInId = checkinId,
                rewardId = rewardId,
                item = item,
                viewRect = viewRect,
                day = index + 1,
            )
        }

        override fun obtainReward(
            baseInfo: CampaignInfo,
            module: CampaignModule,
            task: CampaignModuleDataMission.MissionDetail,
            item: CampaignModuleDataMission.MissionTarget,
            viewRect: Rect
        ) {
            if (vm.oldState.meetWelfareTaskRules != true) {
                showAccountNotMeetDialog()
                return
            }
            val activityId = baseInfo.activityId
            val moduleId = module.id ?: return
            val missionId = item.missionId ?: return
            vm.obtainReward(
                activityId = activityId,
                moduleId = moduleId,
                missionId = missionId,
                item = item,
                viewRect = viewRect
            )
        }

        override fun onPlayShowUpgradeAnim(missionId: Int?) {
            if (missionId != null) {
                vm.onPlayShowUpgradeAnim(missionId)
            }
        }
        override fun onPlayReceiveRewardCardAnim(missionId: Int?) {
            if (missionId != null) {
                vm.onPlayObtainRewardCardAnim(missionId)
            }
        }
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentWelfareTaskBinding? {
        val layoutBinding = FragmentWelfareTaskBinding.inflate(inflater, container, false)
        if (isPad) {
            layoutBinding.viewStubBalance.layoutResource = R.layout.layout_welfare_task_balance_pad
            balanceBinding =
                BalanceLayoutBinding.bind(LayoutWelfareTaskBalancePadBinding.bind(layoutBinding.viewStubBalance.inflate()))
        } else {
            layoutBinding.viewStubBalance.layoutResource = R.layout.layout_welfare_task_balance
            balanceBinding =
                BalanceLayoutBinding.bind(LayoutWelfareTaskBalanceBinding.bind(layoutBinding.viewStubBalance.inflate()))
        }
        balanceBinding?.root?.gone()
        return layoutBinding
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initData()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        checkInController.onSaveInstanceState(outState)
        limitedTimeController.onSaveInstanceState(outState)
        dailyTasksController.onSaveInstanceState(outState)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        checkInController.onRestoreInstanceState(savedInstanceState)
        limitedTimeController.onRestoreInstanceState(savedInstanceState)
        dailyTasksController.onRestoreInstanceState(savedInstanceState)
    }

    /**
     * g_coin 点击币
     * g_creit 点击积分
     * rule 规则（顶部）
     * rule_2 规则（底部）
     * contact 联系我们
     */
    private fun trickCommonClick(click: String) {
        Analytics.track(
            EventConstants.C_DAILY_COMMON_CLICK,
            "click" to click,
        )
    }

    private fun initView() {
        visibleList(
            binding.ivBg,
            binding.statusBar,
            binding.titleBar,
            visible = !args.fromTabLayout
        )

        binding.titleBar.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        balanceBinding?.layoutCoinsBalance?.setOnAntiViolenceClickListener {
            trickCommonClick("g_coin")
            MetaRouter.Pay.goBuyCoinsPage(
                requireContext(),
                this,
                getPageName(),
            )
        }
        balanceBinding?.layoutPointsBalance?.setOnAntiViolenceClickListener {
            trickCommonClick("g_creit")
            val pageUrl =
                h5PageConfigInteractor.getH5PageUrl(H5PageConfigInteractor.POINTS_DETAIL_PAGE_URL)
            MetaRouter.Web.navigate(this, url = pageUrl, showTitle = false)
        }
        balanceBinding?.ivRules?.setOnAntiViolenceClickListener {
            trickCommonClick("rule")
            showWelfareTaskRules()
        }

        // 页面底部的说明文本
        val builder = SpannableReplaceHelper.Builder(getString(R.string.acctivity_feedback))
            .replace(getString(R.string.acctivity_feedback_rule))
            .colorRes(R.color.color_4AB4FF)
            .click {
                trickCommonClick("rule_2")
                showWelfareTaskRules()
            }.replace(getString(R.string.acctivity_feedback_contact_us))
            .colorRes(R.color.color_4AB4FF)
            .click {
                trickCommonClick("contact")
                MetaRouter.Feedback.feedback(
                    this,
                    null,
                    "",
                    FeedbackType.SUGGESTION.id,
                    false,
                    needBackGame = false,
                    fromGameId = null
                )
            }

        binding.tvBottomDesc.movementMethod = LinkMovementMethod.getInstance()
        binding.tvBottomDesc.text = builder.build()

        binding.scrollView.setOnScrollChangeListener { _, _, _, _, _ ->
            limitedTimeTaskVisibilityTracker?.requestVisibilityCheck()
            dailyTaskVisibilityTracker?.requestVisibilityCheck()
        }

        limitedTimeTaskVisibilityTracker = EpoxyVisibilityTracker().attach(
            viewLifecycleOwner,
            binding.rvLimitedTimeTasks
        )
        // Item 有 90% 的部分显示出来时
        limitedTimeTaskVisibilityTracker?.partialImpressionThresholdPercentage = 90
        dailyTaskVisibilityTracker = EpoxyVisibilityTracker().attach(
            viewLifecycleOwner,
            binding.rvDailyTasks
        )
        // Item 有 90% 的部分显示出来时
        dailyTaskVisibilityTracker?.partialImpressionThresholdPercentage = 90
    }

    private fun initData() {
        binding.rvCheckInTasks.setController(checkInController)
        binding.rvLimitedTimeTasks.setController(limitedTimeController)
        binding.rvDailyTasks.setController(dailyTasksController)
        vm.onEach(
            WelfareTaskState::meetWelfareTaskRules
        ) { meetWelfareTaskRules ->
            if (meetWelfareTaskRules == false) {
                showAccountNotMeetDialog(force = false)
            }
        }

        vm.setupRefreshLoadingV2(
            WelfareTaskState::detail,
            binding.loadingView,
            binding.refreshLayout
        ) {
            vm.refresh()
        }
        vm.onEach(
            WelfareTaskState::coinsBalance
        ) { coinBalance ->
            balanceBinding?.tvCoinsBalance?.text = if (coinBalance != null && coinBalance >= 0) {
                UnitUtilWrapper.formatCoinCont(coinBalance)
            } else {
                "---"
            }
        }
        vm.onEach(
            WelfareTaskState::pointsBalance
        ) { pointsBalance ->
            balanceBinding?.tvPointsBalance?.text =
                if (pointsBalance != null && pointsBalance >= 0) {
                    UnitUtilWrapper.formatCoinCont(pointsBalance)
                } else {
                    "---"
                }
        }
        vm.onEach(
            WelfareTaskState::limitTimeTaskRemainingMs
        ) { limitTimeTaskRemainingMs ->
            if (limitTimeTaskRemainingMs <= 0) {
                binding.layoutLimitedTime.gone()
            } else {
                binding.layoutLimitedTime.visible()
                binding.tvLeftTime.text = DateUtil.formatRemainingTime(
                    context = requireContext(),
                    limitTimeTaskRemainingMs
                )
            }
        }
        vm.onEach(
            WelfareTaskState::detail,
        ) { detailAsync ->
            visibleList(
                binding.viewStubBalance,
                binding.tvBottomDesc,
                visible = detailAsync is Success
            )

            val detail = detailAsync.invoke()
            balanceBinding?.root?.visible(detail != null)
            if (detail == null) {
                return@onEach
            }
            tryTrackPageShow(detailAsync)

            balanceBinding?.ivRules?.visible(!detail.baseInfo?.rule.isNullOrEmpty())

            // 签到任务
            val checkInTask = detail.checkInTask()
            val checkInRewards =
                (checkInTask?.customizedData as? CampaignModuleDataCheckIn?)?.checkinUpgradeConfig?.currentCheckinDetail?.checkinRewards

            if (checkInTask == null || checkInRewards.isNullOrEmpty()) {
                binding.layoutDailyCheckIn.gone()
            } else {
                binding.layoutDailyCheckIn.visible()
                binding.tvDailyCheckIn.text = checkInTask.name ?: ""
                val ruleDesc = checkInTask.description
                if (ruleDesc.isNullOrEmpty()) {
                    binding.ivCheckInHelp.gone()
                } else {
                    binding.ivCheckInHelp.visible()
                    binding.ivCheckInHelp.setOnAntiViolenceClickListener {
                        CenterCloseDialog.show(
                            this,
                            args = CenterCloseDialogArgs(
                                title = getString(R.string.activity_rule_title),
                                content = ruleDesc,
                                confirmText = getString(R.string.text_confirm),
                                contentTextAppearance = R.style.MetaTextView_S13_PoppinsRegular400
                            ),
                        )
                    }
                }
            }
            // 限时任务
            val limitedTimeTask = detail.limitedTimeTask()
            val limitedTimeTaskData = limitedTimeTask?.customizedData as? CampaignModuleDataMission?
            val limitedTimeMissions = limitedTimeTaskData?.missions
            if (limitedTimeTask == null || limitedTimeTaskData == null || limitedTimeMissions.isNullOrEmpty()) {
                binding.layoutLimitedTime.gone()
            } else {
                binding.layoutLimitedTime.visible()
                binding.tvLimitedTime.text = limitedTimeTask.name ?: ""
                binding.tvEarnDesc.text = limitedTimeTask.subtitle ?: ""
                binding.tvEarnValue.text = "${limitedTimeTaskData.platCoinNum ?: 0}"

                val ruleDesc = limitedTimeTask.description
                if (ruleDesc.isNullOrEmpty()) {
                    binding.ivLimitedTimeHelp.gone()
                } else {
                    binding.ivLimitedTimeHelp.visible()
                    binding.ivLimitedTimeHelp.setOnAntiViolenceClickListener {
                        CenterCloseDialog.show(
                            this,
                            args = CenterCloseDialogArgs(
                                title = getString(R.string.activity_rule_title),
                                content = ruleDesc,
                                confirmText = getString(R.string.text_confirm),
                                contentTextAppearance = R.style.MetaTextView_S13_PoppinsRegular400
                            ),
                        )
                    }
                }
            }
            // 日常任务
            val dailyTask = detail.dailyTask()
            val dailyTaskData = dailyTask?.customizedData as? CampaignModuleDataMission?
            val dailyTaskMissions = dailyTaskData?.missions
            if (dailyTask == null || dailyTaskMissions.isNullOrEmpty()) {
                binding.layoutDailyTasks.gone()
            } else {
                binding.layoutDailyTasks.visible()
                binding.tvDailyTasks.text = dailyTask.name ?: ""
                val ruleDesc = dailyTask.description
                if (ruleDesc.isNullOrEmpty()) {
                    binding.ivDailyTasksHelp.gone()
                } else {
                    binding.ivDailyTasksHelp.visible()
                    binding.ivDailyTasksHelp.setOnAntiViolenceClickListener {
                        CenterCloseDialog.show(
                            this,
                            args = CenterCloseDialogArgs(
                                title = getString(R.string.activity_rule_title),
                                content = ruleDesc,
                                confirmText = getString(R.string.text_confirm),
                                contentTextAppearance = R.style.MetaTextView_S13_PoppinsRegular400
                            ),
                        )
                    }
                }
            }
            // 当数据驱动页面完成后，尝试在布局完成后恢复滚动位置（一次性）
            val y = pendingScrollY
            if (y != null) {
                binding.scrollView.post {
                    binding.scrollView.viewTreeObserver.addOnGlobalLayoutListener(object : android.view.ViewTreeObserver.OnGlobalLayoutListener {
                        override fun onGlobalLayout() {
                            binding.scrollView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                            binding.scrollView.scrollTo(0, y)
                            pendingScrollY = null
                        }
                    })
                }
            }
        }

        vm.onEach(
            WelfareTaskState::rewardObtainRequestSuccess,
            deliveryMode = uniqueOnly()
        ) { rewardObtainRequestSuccess ->
            if(rewardObtainRequestSuccess == null || rewardObtainRequestSuccess.step != WelfareRewardObtainSuccess.STEP_REQUEST_SUCCESS){
                return@onEach
            }
            val reward = rewardObtainRequestSuccess.item.reward?:return@onEach
            if (reward.isCoinRewardType()) {
                val coins = (reward.rewardCount ?: 0).toInt()
                showObtainedCoinDialog(
                    coins = coins
                ) {
                    playObtainCoinSound()
                    // TODO 弹窗消失时, 播放代币轨迹动画
                    startCoinsIncreaseAnimation(coins) {
                        vm.obtainRewardSuccess(rewardObtainRequestSuccess.copy(
                            step = WelfareRewardObtainSuccess.STEP_END
                        ))
                    }
                }
            } else if (reward.isPointRewardType()) {
                val points = (reward.rewardCount ?: 0).toInt()
                showObtainedPointDialog(
                    points = points
                ) {
                    playObtainCoinSound()
                    // TODO 弹窗消失时, 播放积分轨迹动画
                    startPointsIncreaseAnimation(points) {
                        vm.obtainRewardSuccess(rewardObtainRequestSuccess.copy(
                            step = WelfareRewardObtainSuccess.STEP_END
                        ))
                    }
                }
            }
        }
        vm.onEach(
            WelfareTaskState::checkInRequestSuccess,
            deliveryMode = uniqueOnly()
        ) { checkInRequestSuccess ->
            if(checkInRequestSuccess == null || checkInRequestSuccess.step != WelfareRewardObtainSuccess.STEP_REQUEST_SUCCESS){
                return@onEach
            }
            val reward = checkInRequestSuccess.item
            if (reward.isCoinRewardType()) {
                val coins = (reward.rewardCount ?: 0).toInt()
                showObtainedCoinDialog(
                    coins = coins
                ) {
                    playObtainCoinSound()
                    // TODO 弹窗消失时, 播放代币轨迹动画
                    startCoinsIncreaseAnimation(coins) {
                        vm.checkInSuccess(checkInRequestSuccess.copy(
                            step = WelfareCheckInSuccessInfo.STEP_END
                        ))
                    }
                }
            } else if (reward.isPointRewardType()) {
                val points = (reward.rewardCount ?: 0).toInt()
                showObtainedPointDialog(
                    points = points
                ) {
                    playObtainCoinSound()
                    // TODO 弹窗消失时, 播放积分轨迹动画
                    startPointsIncreaseAnimation(points) {
                        vm.checkInSuccess(checkInRequestSuccess.copy(
                            step = WelfareCheckInSuccessInfo.STEP_END
                        ))
                    }
                }
            }
        }
        vm.registerToast(WelfareTaskState::toast)
    }

    private fun showAccountNotMeetDialog(force: Boolean = true) {
        if (force || shouldShowAccountNotMeetDialog) {
            shouldShowAccountNotMeetDialog = false
            Analytics.track(EventConstants.C_DAILY_ACCOUNT_SHOW)
            CenterCloseDialog.show(
                this,
                args = CenterCloseDialogArgs(
                    title = getString(R.string.complete_account_title),
                    content = getString(R.string.complete_account_content),
                    confirmText = getString(R.string.complete_account_button_title),
                    contentTextAppearance = R.style.MetaTextView_S14_PoppinsRegular400,
                    showYellowHeader = false
                ),
                onConfirm = {
                    Analytics.track(EventConstants.C_DAILY_ACCOUNT_CLICK)
                    MetaRouter.Account.accountComplete(this)
                }
            )
        }
    }

    private fun showWelfareTaskRules() {
        val rule = vm.oldState.detail.invoke()?.baseInfo?.rule ?: return
        CenterCloseDialog.show(
            this,
            args = CenterCloseDialogArgs(
                title = getString(R.string.activity_rule_title),
                content = rule,
                confirmText = getString(R.string.text_confirm),
                contentTextAppearance = R.style.MetaTextView_S13_PoppinsRegular400
            ),
        )
    }

    private fun showObtainedCoinDialog(coins: Int, onDismiss: (() -> Unit)? = null) {
        val coinAnimUrl = vm.oldState.animInfo?.coin
        if (coinAnimUrl.isNullOrEmpty()) {
            onDismiss?.invoke()
        } else {
            AnimeImageDialog.show(
                this,
                args = AnimeImageDialogArgs(
                    animeImageUrl = coinAnimUrl,
                    content = "x$coins",
                    contentTextColorRes = R.color.color_FDE58A,
                ),
                onDismiss = onDismiss
            )
        }
    }

    private fun showObtainedPointDialog(points: Int, onDismiss: (() -> Unit)? = null) {
        val pointAnimUrl = vm.oldState.animInfo?.point
        if (pointAnimUrl.isNullOrEmpty()) {
            onDismiss?.invoke()
        } else {
            AnimeImageDialog.show(
                this,
                args = AnimeImageDialogArgs(
                    animeImageUrl = pointAnimUrl,
                    content = "x$points",
                    contentTextColorRes = R.color.color_E2F384,
                ),
                onDismiss = onDismiss
            )
        }
    }

    private fun playObtainCoinSound() {
        soundPlayer?.release()
        soundPlayer = SoundPlayer()
        soundPlayer?.play(requireContext(), R.raw.obtain_coins, 0)
    }

    /**
     * 代币增加的动画
     */
    fun startCoinsIncreaseAnimation(coins: Int, onEnd: (() -> Unit)? = null){
        val coinBalance = vm.oldState.coinsBalance ?: 0
        val animator = ValueAnimator.ofInt(0, coins)
        animator.duration = 1000
        animator.addUpdateListener(this, true) { animation ->
            val value = animation.getAnimatedValue() as Int
            if (isBindingAvailable()) {
                val targetBalance = coinBalance + value
                balanceBinding?.tvCoinsBalance?.text = UnitUtilWrapper.formatCoinCont(targetBalance)
            }
        }
        animator.doOnEnd(this, true){
            onEnd?.invoke()
        }
        animator.start()
    }

    /**
     * 积分增加的动画
     */
    fun startPointsIncreaseAnimation(points: Int, onEnd: (() -> Unit)? = null) {
        val pointsBalance = vm.oldState.pointsBalance ?: 0
        val animator = ValueAnimator.ofInt(0, points)
        animator.duration = 1000
        animator.addUpdateListener(this, true) { animation ->
            val value = animation.getAnimatedValue() as Int
            if (isBindingAvailable()) {
                val targetBalance = pointsBalance + value
                balanceBinding?.tvPointsBalance?.text =
                    UnitUtilWrapper.formatCoinCont(targetBalance)
            }
        }
        animator.doOnEnd(this, true){
            onEnd?.invoke()
        }
        animator.start()
    }

    override fun onResume() {
        super.onResume()
        vm.refresh()
        limitedTimeTaskVisibilityTracker?.clearVisibilityStates()
        limitedTimeTaskVisibilityTracker?.requestVisibilityCheck()
        dailyTaskVisibilityTracker?.clearVisibilityStates()
        dailyTaskVisibilityTracker?.requestVisibilityCheck()

        shouldTrackPageShow = true
        tryTrackPageShow(vm.oldState.detail)
        // 再次尝试恢复滚动（以防数据在更晚的时候才更新完成）
        val y = pendingScrollY ?: lastScrollY
        if (y != null) {
            binding.scrollView.post {
                binding.scrollView.scrollTo(0, y)
                pendingScrollY = null
            }
        }
    }

    override fun onDestroy() {
        limitedTimeTaskVisibilityTracker = null
        limitedTimeTaskVisibilityTracker = null
        super.onDestroy()
    }

    override fun onDestroyView() {
        // 视图销毁前保存当前滚动位置（Fragment 实例在返回栈中会被保留）
        lastScrollY = if (isBindingAvailable()) binding.scrollView.scrollY else lastScrollY
        soundPlayer?.release()
        soundPlayer = null

        binding.rvCheckInTasks.clear()
        binding.rvCheckInTasks.adapter = null
        binding.rvLimitedTimeTasks.clear()
        binding.rvLimitedTimeTasks.adapter = null
        binding.rvDailyTasks.clear()
        binding.rvDailyTasks.adapter = null
        super.onDestroyView()
    }

    private fun tryTrackPageShow(detailAsync: Async<CampaignDetailResponse>) {
        if (detailAsync is Success && isResumed && shouldTrackPageShow) {
            shouldTrackPageShow = false
            val detail = detailAsync.invoke()
            Analytics.track(
                EventConstants.C_DAILY_ACTIVITY_PAGE_SHOW,
                "id" to (detail.baseInfo?.activityId?.toString() ?: ""),
                "source" to args.source.orEmpty(),
                "is_new" to if (detail.userActivityStatus?.newUser == true) {
                    "yes"
                } else {
                    "no"
                },
                "be_allow" to if (vm.oldState.meetWelfareTaskRules == true) {
                    "yes"
                } else {
                    "no"
                }
            )
        }
    }

    private fun buildCheckInController() = simpleController(
        vm,
        WelfareTaskState::detail,
        WelfareTaskState::checkInSuccess,
    ) { detailAsync, checkInSuccess ->
        val detail = detailAsync.invoke() ?: return@simpleController
        val baseInfo = detail.baseInfo ?: return@simpleController
        val checkInTask = detail.checkInTask() ?: return@simpleController
        val checkInData = checkInTask.customizedData ?: return@simpleController
        if (checkInData is CampaignModuleDataCheckIn && checkInData.checkinUpgradeConfig?.currentCheckinDetail != null) {
            welfareCheckIn(
                isPad = isPad,
                baseInfo = baseInfo,
                module = checkInTask,
                task = checkInData.checkinUpgradeConfig.currentCheckinDetail,
                screenWidth = screenWidth,
                checkInSuccess = checkInSuccess,
                checkInAnimRecord = vm.checkInProgressAnimRecord.toSet(),
                lifecycleOwner = this@WelfareTaskFragment,
                listener = listener,
            )
        }
    }

    private fun MetaEpoxyController.buildTasks(
        baseInfo: CampaignInfo,
        module: CampaignModule,
        taskData: CampaignModuleDataMission,
        rewardObtainSuccess: Map<Int, WelfareRewardObtainSuccess>?
    ) {
        val tasks = taskData.missions
        val upgradeAnimRecord = vm.upgradeAnimRecord.toSet()
        val obtainRewardCardAnimRecord = vm.obtainRewardCardAnimRecord.toSet()
        if (!tasks.isNullOrEmpty()) {
            tasks.forEachIndexed { index, task ->
                val missions = task?.missionTargets
                if (!missions.isNullOrEmpty()) {
                    if (missions.size == 1) {
                        val mission = missions[0]
                        if (mission != null) {
                            welfareTaskSingle(
                                baseInfo = baseInfo,
                                module = module,
                                task = task,
                                item = mission,
                                showBottomDivider = index < tasks.size - 1,
                                rewardObtainSuccess = rewardObtainSuccess,
                                listener = listener,
                            )
                        }
                    } else {
                        welfareTaskMulti(
                            baseInfo = baseInfo,
                            module = module,
                            task = task,
                            lifecycleOwner = this@WelfareTaskFragment,
                            rewardObtainSuccess = rewardObtainSuccess,
                            upgradeAnimRecord = upgradeAnimRecord,
                            obtainRewardCardAnimRecord = obtainRewardCardAnimRecord,
                            listener = listener,
                        )
                    }
                }
            }
        }
    }

    private fun buildLimitedTimeController() = simpleController(
        vm,
        WelfareTaskState::detail,
        WelfareTaskState::rewardObtainSuccess,
    ) { detailAsync, rewardObtainSuccess ->
        val detail = detailAsync.invoke() ?: return@simpleController
        val baseInfo = detail.baseInfo ?: return@simpleController
        val limitedTimeTask = detail.limitedTimeTask() ?: return@simpleController
        val taskData = limitedTimeTask.customizedData ?: return@simpleController
        if (limitedTimeTask.validDateType == CampaignModule.VALID_DATE_TYPE_START_BY_ENTER_PAGE) {
            val endTime = limitedTimeTask.endTimeMills ?: 0L
            if (endTime <= 0) {
                // 限时活动结束了
                return@simpleController
            }
        }
        if (taskData is CampaignModuleDataMission) {
            buildTasks(
                baseInfo,
                limitedTimeTask,
                taskData,
                rewardObtainSuccess
            )
        }
    }

    /**
     * 注意: 不要删除 WelfareTaskState::rewardObtainSuccess 的监听
     * 当 WelfareTaskState::rewardObtainSuccess 的内容变化时, 也应该刷新数据
     */
    private fun buildDailyTasksController() = simpleController(
        vm,
        WelfareTaskState::detail,
        WelfareTaskState::rewardObtainSuccess,
    ) { detailAsync, rewardObtainSuccessLocal ->
        val detail = detailAsync.invoke() ?: return@simpleController
        val baseInfo = detail.baseInfo ?: return@simpleController
        val dailyTask = detail.dailyTask() ?: return@simpleController
        val taskData = dailyTask.customizedData ?: return@simpleController
        if (taskData is CampaignModuleDataMission) {
            buildTasks(
                baseInfo,
                dailyTask,
                taskData,
                rewardObtainSuccessLocal
            )
        }
    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_WELFARE_TASK
}