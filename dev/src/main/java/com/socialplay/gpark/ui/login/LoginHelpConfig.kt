package com.socialplay.gpark.ui.login

import androidx.fragment.app.Fragment
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.LOGIN_HELP_DOC
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.dialog.BottomMenuDialog
import com.socialplay.gpark.ui.dialog.BottomMenuDialogArgs
import com.socialplay.gpark.ui.dialog.MenuInfo
import com.socialplay.gpark.ui.feedback.FeedbackType
import org.koin.core.context.GlobalContext

/**
 * <pre>
 * author : qijijie
 * e-mail : <EMAIL>
 * time   : 2025/09/10
 * desc   :
 * </pre>
 */
const val MENU_ID_HELP_DOC = 0
const val MENU_ID_FEEDBACK = 1
const val MENU_ID_FIND_ID = 2
const val MENU_ID_FIND_PASSWORD = 3

fun Fragment.getLoginHelpConfig(showRedDot: Boolean): BottomMenuDialogArgs {
    return BottomMenuDialogArgs(
        title = getString(R.string.login_help_title),
        menus = listOf(
            MenuInfo(
                id = MENU_ID_HELP_DOC,
                showRedDot = showRedDot,
                title = getString(R.string.login_help_files),
                iconRes = R.drawable.ic_login_help_files
            ),
            MenuInfo(
                id = MENU_ID_FEEDBACK,
                showRedDot = showRedDot,
                title = getString(R.string.login_help_feedback),
                iconRes = R.drawable.ic_login_help_feedback
            ),
            MenuInfo(
                id = MENU_ID_FIND_ID,
                showRedDot = showRedDot,
                title = getString(R.string.login_help_find_party_id),
                iconRes = R.drawable.ic_login_help_find_id
            ),
            MenuInfo(
                id = MENU_ID_FIND_PASSWORD,
                showRedDot = showRedDot,
                title = getString(R.string.login_help_find_password),
                iconRes = R.drawable.ic_login_help_find_password
            ),
        )
    )
}

fun Fragment.showLoginHelpDialog(showRedDot: Boolean, gid: String?) {
    BottomMenuDialog.show(
        this,
        getLoginHelpConfig(showRedDot),
    ) {
        when(it.id) {
            MENU_ID_HELP_DOC -> {
                Analytics.track(EventConstants.EVENT_LOGIN_HELP_CLICK) {
                    put("click", "helpfiles")
                }
                val h5PageConfigInteractor = GlobalContext.get().get<H5PageConfigInteractor>()
                val item = h5PageConfigInteractor.getH5PageConfigItem(LOGIN_HELP_DOC)
                MetaRouter.Web.navigate(this, url = item.url, title = item.title)
            }
            MENU_ID_FEEDBACK -> {
                Analytics.track(EventConstants.EVENT_LOGIN_HELP_CLICK) {
                    put("click", "feedback")
                }
                MetaRouter.Feedback.feedback(
                    this,
                    null,
                    "",
                    FeedbackType.ACCOUNT.id,
                    false,
                    needBackGame = false,
                    fromGameId = null
                )
            }
            MENU_ID_FIND_ID -> {
                Analytics.track(EventConstants.EVENT_LOGIN_HELP_CLICK) {
                    put("click", "find_id")
                }
                MetaRouter.Account.findId(this)
            }
            MENU_ID_FIND_PASSWORD -> {
                Analytics.track(EventConstants.EVENT_LOGIN_HELP_CLICK) {
                    put("click", "find_password")
                }
                MetaRouter.Account.passwordForget(this, LoginPageSource.Login, gid)
            }
        }
    }
}
