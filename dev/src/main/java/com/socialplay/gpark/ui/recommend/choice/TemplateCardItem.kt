package com.socialplay.gpark.ui.recommend.choice

import android.graphics.drawable.Drawable
import android.view.View
import androidx.core.view.marginEnd
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.socialplay.gpark.R
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemTemplateBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.setHorizontalScrollPadding
import com.socialplay.gpark.util.PaletteColorUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getString
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.ui.view.*
import com.socialplay.gpark.util.extension.setEndMargin
import org.koin.core.context.GlobalContext

fun MetaModelCollector.templateCardList(
    card: ChoiceCardInfo, cardPosition: Int, spanSize: Int, listener: IChoiceListener
) {
    val games = card.gameListNoNull ?: return
    // 先添加标题和更多按钮
    val choiceTitleMoreItem = ChoiceTitleMoreItem(card, spanSize, listener, getCardRedCacheKey(card), paddingBottom = 2.dp)
    add(
        choiceTitleMoreItem.id("TemplateCardTitle-$cardPosition-${card.cardName.hashCode()}").spanSizeOverride { _, _, _ -> spanSize })

    carouselNoSnapWrapBuilder {
        id("TemplateCardList-$cardPosition")

        setHorizontalScrollPadding()
        hasFixedSize(true)
        initialPrefetchItemCount(1)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        spanSizeOverride { _, _, _ ->
            spanSize
        }
        games.forEachIndexed { index, game ->
            if (index >= 9) {
                return@forEachIndexed
            }
            add(
                TemplateCardItem(
                    item = game, position = index, card = card, cardPosition = cardPosition, spanSize = spanSize, isTemplate = game.isTemplate(), listener = listener, title = choiceTitleMoreItem
                ).id("TemplateCardItem-${card.cardId}-$cardPosition-$index-${game.code}").spanSizeOverride { _, _, _ ->
                    1
                }
            )
        }
    }
}

data class TemplateCardItem(
    val item: ChoiceGameInfo, val position: Int, val card: ChoiceCardInfo, val cardPosition: Int, val spanSize: Int, val isTemplate: Boolean, val listener: IChoiceListener, val title: ChoiceTitleMoreItem
) : ViewBindingItemModel<AdapterChoiceCardItemTemplateBinding>(
    R.layout.adapter_choice_card_item_template, AdapterChoiceCardItemTemplateBinding::bind
) {

    // 生成唯一的视图ID
    private val viewId = "TemplateCardItem_${item.code}_${cardPosition}_${position}_${item.hashCode()}"
    override fun AdapterChoiceCardItemTemplateBinding.onBind() {
        // 点赞
        likeView.setLikeText(UnitUtil.formatKMCount(item.localLikeCount ?: item.likeCount ?: 0))
        // 游玩
        playersView.setLikeText(UnitUtil.formatKMCount(item.playingCount ?: 0))

        // 标题
        tvGameTitle.text = item.displayName
        // 星星数量
        val stars = listOf(ivStar1, ivStar2, ivStar3)
        val rating = item.difficulty?.toInt() ?: 3

        // 展示不同组件
        tagContainer.visible(isTemplate)
        btnCreate.visible(isTemplate)
        sivCreatorAvatar.visible(!isTemplate)
        tvCreatorNickname.visible(!isTemplate)
        playersView.visible(!isTemplate)

        // 封面
        listener.getGlideOrNull()?.run {
            load(item.imageUrl).placeholder(PaletteColorUtil.getPlaceholderDrawable(root.context, item.gameColor))
                .error(PaletteColorUtil.getFailedPlaceholderDrawable(root.context)) // 添加错误占位图
                .into(ivGameIcon)
        }

        if (isTemplate) {
            val layoutParams = root.layoutParams
            layoutParams.width = ScreenUtil.dp2px(context, 196f)
            root.layoutParams = layoutParams

            // 第一位 模板数据
            layoutTemplateTag.setBackgroundResource(R.drawable.bg_home_template_item)
            tvTemplate.text = getString(R.string.game_template)
            // 星星
            for (i in stars.indices) {
                stars[i].visibility = View.VISIBLE
                stars[i].alpha = if (i < rating) 1f else 0.3f
            }
            // 标签
            tagContainer.setTags(item.tagList ?: emptyList())
            // 标题大小
            tvGameTitle.setEndMargin(dp(66))
            // 按钮
            btnCreate.setOnAntiViolenceClickListener { listener.onOtherClick(1, position, card, item) }
        } else {
            val layoutParams = root.layoutParams
            layoutParams.width = ScreenUtil.dp2px(context, 130f)
            root.layoutParams = layoutParams

            // 标题大小
            tvGameTitle.setEndMargin(dp(0))
            layoutTemplateTag.setBackgroundResource(R.drawable.bg_home_template_child_item)
            tvTemplate.text = getString(R.string.game_creation)
            // 星星
            for (i in stars.indices) {
                stars[i].visibility = View.GONE
            }
            listener.getGlideOrNull()?.run {
                load(item.avatar)
                    .placeholder(R.drawable.placeholder_round)
                    .into(sivCreatorAvatar)
            }
            tvCreatorNickname.text = item.nickname

            // 头像和昵称点击事件
            sivCreatorAvatar.setOnAntiViolenceClickListener { listener.onOtherClick(2, position, card, item) }
            tvCreatorNickname.setOnAntiViolenceClickListener { listener.onOtherClick(2, position, card, item) }

            btnCreate.unsetOnClick()
        }
        // 可点击整个卡片
        root.setOnAntiViolenceClickListener {
            val metaKV: MetaKV = GlobalContext.get().get()
            metaKV.appKV.isShowRedDotFromHomeFeedClick(getCardRedCacheKey(card))
            listener.onItemClick(cardPosition, card, position, item, false)
            title.hideRedDot()
        }
    }

    override fun AdapterChoiceCardItemTemplateBinding.onUnbind() {
        root.unsetOnClick()

        // 使用扩展函数取消注册模糊视图
        likeView.disableSmartBlur("${viewId}_like")
        if (!isTemplate) {
            playersView.disableSmartBlur("${viewId}_players")
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}