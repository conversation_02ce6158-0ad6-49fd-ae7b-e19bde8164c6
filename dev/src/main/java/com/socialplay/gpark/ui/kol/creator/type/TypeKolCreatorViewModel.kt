package com.socialplay.gpark.ui.kol.creator.type

import android.content.ComponentCallbacks
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.map
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.community.RecommendUsersResponse
import com.socialplay.gpark.data.model.creator.FollowResult
import com.socialplay.gpark.data.model.creator.KolCreatorInfo
import com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorRequest
import com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorRequest.Companion.TYPE_FOLLOWED
import com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorRequest.Companion.TYPE_RECOMMEND
import com.socialplay.gpark.data.model.creator.morecreator.TypeCreatorResult
import com.socialplay.gpark.data.model.user.RelationType
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.maverick.map
import com.socialplay.gpark.ui.core.views.LoadMoreState
import org.greenrobot.eventbus.EventBus
import org.koin.android.ext.android.get

/**
 * Created by bo.li
 * Date: 2024/8/9
 * Desc:
 */

data class TypeKolCreatorModelState(
    // 请求接口用
    val listType: Int,
    val asyncList: Async<List<KolCreatorInfo>> = Uninitialized,
    val loadMore: Async<LoadMoreState> = Uninitialized,
    val followResult: Async<FollowResult> = Uninitialized,
    val sinceId: String? = null,
    val offset: String? = null
) : MavericksState {
    constructor(args: TypeKolCreatorFragmentArgs) : this(listType = args.type)

    val list: List<KolCreatorInfo> get() = asyncList() ?: emptyList()
}

class TypeKolMoreCreatorViewModel(
    private val repository: IMetaRepository,
    initialState: TypeKolCreatorModelState
) : BaseViewModel<TypeKolCreatorModelState>(initialState) {

    init {
        refresh()
        onAsync(TypeKolCreatorModelState::followResult, onSuccess = { result ->
            setState {
                copy(
                    asyncList = asyncList.map { list ->
                        list.map {
                            if (it.uuid == result.uuid) {
                                it.copy(followUser = result.toFollow)
                            } else {
                                it
                            }
                        }
                    },
                )
            }
        })
    }

    companion object :
        KoinViewModelFactory<TypeKolMoreCreatorViewModel, TypeKolCreatorModelState>() {
        private const val LOAD_RECOMMEND_USERS_PAGE_SIZE = 9

        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: TypeKolCreatorModelState
        ): TypeKolMoreCreatorViewModel {
            return TypeKolMoreCreatorViewModel(get(), state)
        }
    }

    private fun getRequestRelationType(listType: Int): String? {
        return if (listType == TypeCreatorRequest.TYPE_FOLLOWED) {
            TypeCreatorRequest.RELATION_TYPE_FOLLOW
        } else {
            null
        }
    }

    fun refresh() = withState { oldState ->
        if (oldState.asyncList is Loading) return@withState
        when(oldState.listType) {
            TYPE_FOLLOWED -> {
                repository.getTypeCreatorList(
                    oldState.listType,
                    null,
                    getRequestRelationType(oldState.listType)
                ).execute { result ->
                    val nextSinceId = result()?.userList?.lastOrNull()?.sinceId
                    val end =
                        result()?.end == true || result()?.userList.isNullOrEmpty() || nextSinceId.isNullOrEmpty()
                    copy(
                        asyncList = result.map {
                            it.userList?.distinctBy { it.uuid } ?: emptyList()
                        },
                        sinceId = if (result is Success) nextSinceId else null,
                        loadMore = result.map { LoadMoreState(end) },
                    )
                }
            }
            TYPE_RECOMMEND -> {
                repository.getCommunityRecommendUsers(
                    pageSize = LOAD_RECOMMEND_USERS_PAGE_SIZE,
                    offset = null,
                ).execute { result ->
                    val creatorList =
                        result()?.list?.map { recommend -> recommend.toKolCreatorInfo() }
                            ?: emptyList()
                    val end =
                        result()?.hasMore == false || creatorList.isEmpty() || offset.isNullOrEmpty()

                    copy(
                        asyncList = result.map { creatorList.distinctBy { it.uuid } },
                        offset = if (result is Success) result().offset else null,
                        loadMore = result.map { LoadMoreState(end) },
                    )
                }
            }
        }
    }

    fun loadMore() = withState { oldState ->
        if (oldState.asyncList is Loading) return@withState
        when(oldState.listType) {
            TYPE_FOLLOWED -> {
                repository.getTypeCreatorList(
                    oldState.listType,
                    oldState.sinceId,
                    getRequestRelationType(oldState.listType)
                ).execute { result ->
                    val nextSinceId = result()?.userList?.lastOrNull()?.sinceId
                    val end =
                        result()?.end == true || result()?.userList.isNullOrEmpty() || nextSinceId.isNullOrEmpty()
                    copy(
                        asyncList = if (result is Success) {
                            val oldList = asyncList.invoke()
                            result.map { wrapper ->
                                if (oldList.isNullOrEmpty()) {
                                    wrapper.userList ?: emptyList()
                                } else {
                                    oldList + (wrapper.userList ?: emptyList())
                                }.distinctBy { it.uuid }
                            }
                        } else {
                            asyncList
                        },
                        sinceId = if (result is Success) nextSinceId else sinceId,
                        loadMore = result.map { LoadMoreState(end) },
                    )
                }
            }

            TYPE_RECOMMEND -> {
                repository.getCommunityRecommendUsers(
                    pageSize = LOAD_RECOMMEND_USERS_PAGE_SIZE,
                    offset = oldState.offset,
                ).execute { result ->
                    val creatorList =
                        result()?.list?.map { recommend -> recommend.toKolCreatorInfo() }
                            ?: emptyList()
                    val end =
                        result()?.hasMore == false || creatorList.isEmpty() || offset.isNullOrEmpty()

                    copy(
                        asyncList = if(result is Success) {
                            val oldList = asyncList.invoke()
                            result.map {
                                if (oldList.isNullOrEmpty()) {
                                    creatorList
                                } else {
                                    oldList + creatorList
                                }.distinctBy { it.uuid }
                            }
                        } else {
                            asyncList
                        },
                        offset = if (result is Success) result().offset else offset,
                        loadMore = result.map { LoadMoreState(end) },
                    )
                }
            }
        }
    }

    // 加载推荐用户
    private fun loadRecommendUser(
        onOnSuccess: TypeKolCreatorModelState.(RecommendUsersResponse?) -> TypeKolCreatorModelState
    ) {
        if (oldState.asyncList is Loading) return

        setState { copy(asyncList = Loading()) }
        repository.getCommunityRecommendUsers(
            pageSize = LOAD_RECOMMEND_USERS_PAGE_SIZE,
            offset = oldState.offset,
        ).execute { result ->
            when(result) {
                is Success -> onOnSuccess(result())
                is Fail -> copy(asyncList = Fail(result.error))
                is Loading -> copy(asyncList = Loading())
                Uninitialized -> oldState
            }
        }
    }

    // 加载关注用户
    private fun loadFollowedUser(
        sinceId: String?,
        onSuccess: TypeKolCreatorModelState.(TypeCreatorResult?) -> TypeKolCreatorModelState
    ) {
        if (oldState.asyncList is Loading) return

        setState { copy(asyncList = Loading()) }
        repository.getTypeCreatorList(
            oldState.listType,
            sinceId,
            getRequestRelationType(oldState.listType)
        ).execute { result ->
            when(result) {
                is Success -> onSuccess(result())
                is Fail -> copy(asyncList = Fail(result.error))
                else -> oldState
            }
        }
    }

    fun changeFollow(uuid: String, toFollow: Boolean) {
        if (oldState.asyncList is Loading || oldState.followResult is Loading) return
        Analytics.track(EventConstants.EVENT_FOLLOW_CLICK) {
            put(EventParamConstants.KEY_USERID, uuid)
            put(EventParamConstants.KEY_LOCATION, EventParamConstants.LOCATION_KOL_MORE_CREATOR)
            put(
                EventParamConstants.KEY_TYPE,
                if (toFollow) EventParamConstants.TYPE_FOLLOW else EventParamConstants.TYPE_UNFOLLOW
            )
        }
        if (toFollow) {
            repository.relationAddV2(uuid, RelationType.Follow.value)
        } else {
            repository.relationDelV2(uuid, RelationType.Follow.value)
        }.map {
            EventBus.getDefault().post(UserFollowEvent(uuid, toFollow, UserFollowEvent.FROM_KOL_MORE))
            FollowResult(uuid, toFollow)
        }.execute { result ->
            copy(followResult = result)
        }
    }
}