package com.socialplay.gpark.ui.im

import android.graphics.Color
import android.graphics.Rect
import android.graphics.Typeface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.forEachIndexed
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.Navigator
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.mvrx.activityViewModel
import com.airbnb.mvrx.fragmentViewModel
import com.ly123.tes.mgs.metacloud.MetaCloud
import com.ly123.tes.mgs.metacloud.message.MetaConversation
import com.ly123.tes.mgs.metacloud.model.Conversation.ConversationType
import com.meta.box.biz.friend.FriendBiz
import com.meta.box.biz.friend.model.FriendInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.ImInteractor
import com.socialplay.gpark.data.model.ListStatus
import com.socialplay.gpark.data.model.SysHeaderInfo
import com.socialplay.gpark.data.model.editor.EditorNotice
import com.socialplay.gpark.data.model.im.AbsConversationMessage
import com.socialplay.gpark.data.model.im.ConversationMessage
import com.socialplay.gpark.data.model.im.GroupConversationMessage
import com.socialplay.gpark.data.repository.GroupChatRepository
import com.socialplay.gpark.databinding.FragmentConversationListBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventConstants.EVENT_FIRST_PUSH_POST_CLOSE
import com.socialplay.gpark.function.analytics.EventConstants.EVENT_FIRST_PUSH_POST_SHOW
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.aibot.AiBotConversation
import com.socialplay.gpark.ui.aibot.AiBotConversationItem
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.views.MetaEpoxyController
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.BottomMenuDialog
import com.socialplay.gpark.ui.dialog.BottomMenuDialogArgs
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.MenuInfo
import com.socialplay.gpark.ui.home.adapter.HomeLoadMoreFooterAdapter
import com.socialplay.gpark.ui.im.conversation.GroupChatViewModel
import com.socialplay.gpark.ui.im.groupchat.GroupChatConfig
import com.socialplay.gpark.ui.main.MainViewModel
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.util.CustomEpoxyTouchHelper
import com.socialplay.gpark.util.DeleteDragView
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.PopWindowUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.SimpleVibratorUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getDimensionPx
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.extension.tagIds
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.toJSON
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.ext.android.inject
import timber.log.Timber
import kotlin.getValue

/**
 * @author: ning.wang
 * @date: 2021-06-21 4:46 下午
 * @desc:
 */
class ConversationListFragment : BaseFragment<FragmentConversationListBinding>(R.layout.fragment_conversation_list) {
    private val mainViewModel:MainViewModel by activityViewModels()
    private val viewModel: ConversationListViewModel by activityViewModel()
    private val groupChatViewModel: GroupChatViewModel by fragmentViewModel()
    private val conversationListAdapter: ConversationListAdapter = ConversationListAdapter(::glide) {
        UserLabelView.showDescDialog(this, it)
    }
    override val destId: Int = R.id.chat_tab
    private val friendRequestsHeader: FriendRequestsHeader = FriendRequestsHeader()
    private val sysHeaderAdapter: SysHeaderAdapter = SysHeaderAdapter(::glide)
    private val sysListAdapter: SysListAdapter = SysListAdapter(::glide, onClickTopHeader = { item ->
        if (item.isRequestFriend) {
            MetaRouter.IM.goFriendGroupsRequestList(this@ConversationListFragment)
        } else {
            MetaRouter.IM.goSys(
                this@ConversationListFragment,
                item.groupId,
                item.groupContentType,
                item.title,
                (item.unread).toLong(),
                0,
                item.gameId,
                item.tabList.toJSON(),
                -1,
                item.icon
            )
        }
        Analytics.track(
            EventConstants.EVENT_NOTICE_CLICK_SEND_MESSAGE,
            "group_id" to item.groupId
        )
    }).apply {
        onClickFilter = {
            PopWindowUtil.PopupWindowBuilder(requireActivity())
                .setView(R.layout.pop_conversation_filter_list)
                .setElevation(3.0f)
                .create().apply {
                    this.showAsDropDown(it)
                    val linearLayout = this.mContentView as LinearLayout
                    val tv = linearLayout.getChildAt(viewModel.curFilter.ordinal) as TextView
                    tv.setTextColor(Color.parseColor("#1a1a1a"))
                    tv.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                    linearLayout.forEachIndexed { index, view ->
                        view.setOnAntiViolenceClickListener {
                            viewModel.setCurrentFilter(index)
                            setFilter(SysListAdapter.ConversationFilter.entries[index])
                            dissmiss()
                        }
                    }
                }
        }
    }

    private val listStatusAdapter: ListStatusAdapter = ListStatusAdapter()

    private val loadMoreAdapter: HomeLoadMoreFooterAdapter = HomeLoadMoreFooterAdapter {
        viewModel.refreshConversationList()
        viewModel.getUnReadCount()
        viewModel.refreshFriends()
    }

    private val concatAdapter: ConcatAdapter = if (PandoraToggle.isNewMessage) {
        ConcatAdapter(
            sysListAdapter,
            conversationListAdapter,
            listStatusAdapter,
            loadMoreAdapter
        )
    } else {
        ConcatAdapter(
            friendRequestsHeader,
            sysHeaderAdapter,
            conversationListAdapter,
            listStatusAdapter,
            loadMoreAdapter
        )
    }

    private val imInteractor: ImInteractor by inject()

    private var aiConversationController: MetaEpoxyController? = null
    var removeItem: AiBotConversationItem? = null
    var imgCleared: Boolean = true
    var touchHelper: ItemTouchHelper? = null
    var isGotSet: Boolean = false

    private val args by navArgs<ConversationListFragmentArgs>()

    override fun getPageName(): String = PageNameConstants.FRAGMENT_NAME_CONVERSATION_LIST

    private val itemSpacingDecoration = object : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
//            val holder = parent.getChildViewHolder(view) ?: return
//            if (holder.absoluteAdapterPosition == 0) {
//                outRect.top = 10.dp
//            }

            //有数据且是最后一条数据，给他加Spacing，防止被Tab挡住
//            if (holder.bindingAdapter == conversationListAdapter && holder.bindingAdapterPosition == conversationListAdapter.itemCount-1) {
//                outRect.bottom = 82.dp
//            }
        }
    }

    var startCreateTime: Long = 0L
    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentConversationListBinding? {
        startCreateTime = System.currentTimeMillis()
        Timber.tag("SimpleViewCache_S").d("ConversationListFragment onCreateViewBinding")
        return FragmentConversationListBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        init()
    }

    fun init() {
        if (!isBindingAvailable() || context == null) {
            return
        }
        initView()
        initData()
    }


    private fun initView() {
        binding.apply {
            if (args.isFromBottom) {
                binding.root.setPaddingEx(bottom = getDimensionPx(R.dimen.tab_layout_height))
            } else {
                binding.root.setPaddingEx(bottom = 0)
                binding.ivBackBtn.visible()
                binding.ivBackBtn.setOnAntiViolenceClickListener {
                    navigateUp()
                }
            }
            ivClear.visible(PandoraToggle.isNewMessage)
            ivClear.setOnAntiViolenceClickListener {
                viewLifecycleOwner.lifecycleScope.launch {
                    val isClear = viewModel.clearAllUnread()
                    if (isClear) toast(R.string.success) else toast(R.string.account_and_password_set_failed)
                }
            }
            rvConversationList.layoutManager = LinearLayoutManager(requireContext())
            rvConversationList.itemAnimator = null

            rvConversationList.removeItemDecoration(itemSpacingDecoration)
            rvConversationList.addItemDecoration(itemSpacingDecoration)

            ivSearch.setOnAntiViolenceClickListener { view ->
                Analytics.track(EventConstants.CHAT_ADD_CLICK)

                val haveCreateGroupPower = viewModel.haveCreateGroupPower.value
                val groupCreateCount = viewModel.groupChatCount.value?.createCount ?: 0
                BottomMenuDialog.show(
                    fragment = this@ConversationListFragment,
                    args = BottomMenuDialogArgs(
                        title = null,
                        menus = listOf(
                            // 添加好友
                            MenuInfo(
                                id = MenuInfo.ID_ADD_FRIEND,
                                title = getString(R.string.message_center_popup_item_add_friend),
                                iconRes = R.drawable.message_center_popup_icon_add_friend,
                            ),
                            // 创建群聊
                            MenuInfo(
                                id = MenuInfo.ID_CREATE_GROUP,
                                title = getString(R.string.message_center_popup_item_create_group),
                                iconRes = R.drawable.message_center_popup_icon_create_group,
                                showRedDot = haveCreateGroupPower && groupChatViewModel.showCreateGroupChatRedDot() && groupCreateCount <= 0,
                                showInMenu = haveCreateGroupPower
                            ),
                            // 搜索群聊
                            MenuInfo(
                                id = MenuInfo.ID_SEARCH_GROUP,
                                title = getString(R.string.message_center_popup_item_search_group),
                                iconRes = R.drawable.message_center_popup_icon_search_group,
                                showInMenu = false
                            ),
                        )
                    ),
                ){ menuInfo->
                    when(menuInfo.id){
                        MenuInfo.ID_ADD_FRIEND->{
                            Analytics.track(EventConstants.EVENT_ADD_FRIENDS_CLICK) {
                                put("source", "chat")
                            }
                            MetaRouter.IM.goSearchFriend(this@ConversationListFragment)
                        }
                        MenuInfo.ID_CREATE_GROUP->{
                            Analytics.track(EventConstants.GROUP_CREATE_CLICK)
                            val groupCreateCount = viewModel.groupChatCount.value?.createCount
                            if (groupCreateCount == null || groupCreateCount >= GroupChatConfig.MAX_CREATE_GROUP_COUNT) {
                                toast(R.string.toast_create_group_failed_maximum_limit)
                                return@show
                            }
                            groupChatViewModel.clickCreateGroupChatMenu()
                            binding.viewSearchRedDot.visible(false)
                            MetaRouter.IM.goContacts(
                                this@ConversationListFragment,
                                isCreateGroup = true
                            )
                        }
                        MenuInfo.ID_SEARCH_GROUP->{
                            Analytics.track(EventConstants.CHAT_SEARCH_GROUP_CLICK)
                            // TODO 一期不做群搜索
                        }
                        else -> {}
                    }
                }
            }

            ivContacts.setOnAntiViolenceClickListener {
                MetaRouter.IM.goContacts(this@ConversationListFragment)
            }

            refresh.setOnRefreshListener {
                viewModel.refreshConversationList()
                viewModel.getUnReadCount()
                viewModel.refreshFriendsUnreadRequests()
                viewModel.getEditorNoticeUnreadCount()
                viewModel.getPostUnread()
                viewModel.getNotice()
                viewModel.refreshFriends()
                viewModel.loadSys()
                viewModel.getAiBotConversationList(true)
                viewModel.checkCreateGroupPower()
                viewModel.getGroupChatCount()
                viewModel.getGroupPendingRequestCount()
            }

            listStatusAdapter.setEmptyClickListener {
                viewModel.refreshConversationList()
                viewModel.refreshFriends()
                viewModel.getUnReadCount()
            }

            listStatusAdapter.setErrorClickListener {
                viewModel.refreshConversationList()
                viewModel.refreshFriends()
                viewModel.getUnReadCount()
            }

            conversationListAdapter.setOnItemClickListener { view, position ->
                val item = conversationListAdapter.getItem(position)
                when (item) {
                    is ConversationMessage -> {
                        toPrivateChat(item.friendInfo)
                    }

                    is GroupConversationMessage -> {
                        // 跳转群聊页面
                        item.conversation?.also { conversation ->
                            toGroupChat(conversation)
                        }
                    }

                    is AbsConversationMessage.NoticeConversation -> {
                        when (item.notice.type) {
                            EditorNotice.OuterShowNotice.TYPE_EDITOR -> {
                                Analytics.track(EventConstants.NBLAND_NOTICE_CLICK)
                                MetaRouter.Notice.editorNotice(this@ConversationListFragment)
                            }

                            EditorNotice.OuterShowNotice.TYPE_OPERATION -> {
                                MetaRouter.Notice.operationNotice(this@ConversationListFragment)
                            }
                        }
                    }

                    is AbsConversationMessage.SysConversation -> {
                        MetaRouter.IM.goSys(
                            this@ConversationListFragment,
                            item.info.groupId,
                            item.info.groupContentType,
                            item.info.title,
                            (item.info.unread).toLong(),
                            1,
                            item.info.gameId,
                            item.info.tabList.toJSON(),
                            -1,
                            item.info.icon
                        )
                        Analytics.track(
                            EventConstants.EVENT_NOTICE_CLICK_SEND_MESSAGE,
                            "group_id" to item.info.groupId
                        )
                    }
                }
            }

            friendRequestsHeader.setOnItemClickListener { _, _ ->
                MetaRouter.IM.goFriendGroupsRequestList(this@ConversationListFragment)
            }
            sysHeaderAdapter.setOnItemClickListener { _, position ->
                val item = sysHeaderAdapter.getItem(position)
                MetaRouter.IM.goSys(
                    this@ConversationListFragment,
                    item.groupId,
                    item.groupContentType,
                    item.title,
                    (item.unread).toLong(),
                    0,
                    item.gameId,
                    item.tabList.toJSON(),
                    -1,
                    item.icon
                )
                Analytics.track(
                    EventConstants.EVENT_NOTICE_CLICK_SEND_MESSAGE,
                    "group_id" to item.groupId
                )
            }

            conversationListAdapter.setOnItemLongClickListener { view, position ->
                Analytics.track(EventConstants.EVENT_IM_LONG_CLICK_LIST_ITEM_CLICK)
                when (val item = conversationListAdapter.getItem(position)) {
                    is ConversationMessage -> {
                        item.conversation?.let { showPopMenu(view, it) }
                    }

                    else -> {}
                }
                return@setOnItemLongClickListener true
            }
            rvConversationList.adapter = concatAdapter
        }
        initAiBotView()
        initAppNotification()
    }

    private fun toPrivateChat(friendInfo: FriendInfo, navigatorExtras: Navigator.Extras? = null) {
        viewModel.refreshConversationList()
        viewModel.getUnReadCount()
        Analytics.track(EventConstants.EVENT_IM_CHAT_CLICK) { put("from", 1) }

        MetaRouter.IM.gotoConversation(
            fragment = this@ConversationListFragment,
            otherUid = friendInfo.uuid,
            title = friendInfo.name,
            tagIds = friendInfo.tagIds,
            labelInfo = friendInfo.labelInfo,
            navigatorExtras = navigatorExtras
        )
    }

    private fun toGroupChat(
        conversation: MetaConversation,
        navigatorExtras: Navigator.Extras? = null
    ) {
        viewModel.refreshConversationList()
        viewModel.getUnReadCount()
        val groupId = groupChatViewModel.getGroupIdByImId(conversation.targetId)
        if (groupId == GroupChatRepository.DISBANDED_GROUP_ID) {
            // 群已解散
            toast(R.string.toast_cant_jump_group_chat_page_because_disbanded)
            // 将已解散的群聊的未读消息数清除
            MetaCloud.clearMessageUnReadStatus(
                ConversationType.GROUP,
                conversation.targetId
            ) {}
            return
        }
        if (groupId != null) {
            MetaRouter.IM.goGroupChat(
                fragment = this@ConversationListFragment,
                imId = conversation.targetId,
                groupChatId = groupId.toString(),
                groupName = conversation.showName,
                navigatorExtras = navigatorExtras
            )
        }
    }

    private fun initData() {
        lifecycleScope.launch {
            viewModel.sysHeaderList.collect { sysHeaderList ->
                if (sysHeaderList.isNotEmpty()) {
                    listStatusAdapter.setStatus(ListStatus.Success)
                }
                if (PandoraToggle.isNewMessage) {
                    val topList: MutableList<SysHeaderInfo> = mutableListOf()
                    topList.add(
                        SysHeaderInfo(
                            title = getString(R.string.new_friend),
                            isRequestFriend = true,
                            unread = FriendBiz.friendRequestUnreadCount.value
                        )
                    )
                    sysHeaderList.toMutableList().forEach { item ->
                        if (item.topping != false) {
                            topList.add(item)
                        }
                    }
                    sysListAdapter.setList(listOf(topList))
                } else {
                    withContext(Dispatchers.IO) {
                        sysHeaderAdapter.preLoad(context, sysHeaderList, true)
                        withContext(Dispatchers.Main) {
                            sysHeaderAdapter.setList(sysHeaderList)
                        }
                    }
                }
                sysHeaderList.forEach { item ->
                    Analytics.track(
                        EventConstants.EVENT_NOTICE_SEND_MESSAGE_SHOW,
                        "group_id" to item.groupId,
                        "unread_count" to item.unread
                    )
                }
            }
        }
        viewModel.conversationChangedEvent.observe(viewLifecycleOwner) {
            viewModel.refreshConversationList()
            viewModel.getUnReadCount()
        }

        viewModel.apply {
            viewLifecycleOwner.lifecycleScope.launch {
                viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {
                    conversationMessageList.collect { conversations ->
                        withContext(Dispatchers.IO) {
                            conversationListAdapter.preLoad(context, conversations, true)
                            withContext(Dispatchers.Main) {
                                conversationListAdapter.setList(conversations)
                            }
                        }
                        listStatusAdapter.setStatus(
                            if (conversations.isEmpty() && viewModel.sysHeaderList.value.isEmpty()) ListStatus.Empty(
                                getString(R.string.no_messages)
                            ) else ListStatus.Success
                        )
                        binding.refresh.isRefreshing = false

                        val imIds = conversations.filter { absCon ->
                            absCon is GroupConversationMessage
                                    && absCon.conversation?.conversationType == ConversationType.GROUP
                                    && absCon.conversation.targetId.isNotEmpty()
                        }.map { con -> (con as GroupConversationMessage).conversation!!.targetId }
                        groupChatViewModel.fetchGroupIdByImIds(imIds)
                    }
                }
            }

            viewLifecycleOwner.lifecycleScope.launch {
                viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {

                    friendAndGroupRequestUnreadCountFlow.collect {
                        if (PandoraToggle.isNewMessage) {
                            viewModel.loadSys()
                        } else {
                            withContext(Dispatchers.IO) {
                                friendRequestsHeader.preLoad(context, listOf(it), true)
                                withContext(Dispatchers.Main) {
                                    friendRequestsHeader.setFriendRequestUnreadCount(it)
                                }
                            }
                        }
                    }
                }
            }

            imInteractor.IMConnectionStatus.observe(viewLifecycleOwner) {
                Timber.i("rongConnectionStatus:" + it)
                val isShow = !it || !NetUtil.isNetworkAvailable()
                binding.groupConnectionStatus.visible(isShow)
                if (isShow) {
                    binding.groupNotificationTips.gone()
                } else {
                    updateNotificationView(viewModel.oldState.needShowNotification)
                }
                if (it) {
                    viewModel.refreshConversationList()
                    viewModel.getUnReadCount()
                }
            }
        }
        viewModel.onEach(ConversationListState::needShowNotification) { result ->
            updateNotificationView(result)
            Timber.d("needShowNotification_change")
        }
        if (groupChatViewModel.showCreateGroupChatRedDot()) {
            viewModel.showCreateGroupChatRedDot.asLiveData().observe(viewLifecycleOwner) { show ->
                binding.viewSearchRedDot.visible(groupChatViewModel.showCreateGroupChatRedDot() && show)
            }
        } else {
            binding.viewSearchRedDot.gone()
        }
    }

    private fun initAppNotification() {
        if (PandoraToggle.isChatPushNotification) {
            binding.appNotificationOpen.setOnAntiViolenceClickListener {
                isGotSet = true
                PermissionRequest.goNotification(requireActivity())
            }
            binding.appNotificationClose.setOnAntiViolenceClickListener {
                binding.groupNotificationTips.gone()
                viewModel.updateNotificationStatus(false)
                Analytics.track(EVENT_FIRST_PUSH_POST_CLOSE)
            }
            binding.tvNotification.requestFocus()
        }
    }

    private fun updateNotificationView(isShow: Boolean) {
        if (!PandoraToggle.isChatPushNotification) return
        if (binding.groupConnectionStatus.isVisible) {
            binding.groupNotificationTips.gone()
        } else {
            binding.groupNotificationTips.visible(isShow)
            Analytics.track(EVENT_FIRST_PUSH_POST_SHOW)
        }
    }


    /**
     * 添加aiBot会话列表
     */
    private fun initAiBotView() {
        if (PandoraToggle.isOpenAiBot) {
            binding.dragDeleteView.visible()
            viewModel.onEach(ConversationListState::count) {
                binding.tvAiTitle.visible(it > 0)
                binding.ryAiBot.visible(it > 0)
            }
            binding.ryAiBot.layoutManager =
                LinearLayoutManager(requireContext(), RecyclerView.HORIZONTAL, false)
            aiConversationController = buildAiConversationController()
            binding.ryAiBot.setController(aiConversationController!!)

            binding.dragDeleteView.setOnListener(
                binding.ryAiBot,
                object : DeleteDragView.DragListener {
                    override fun onRemoveItem() {
                        removeItem?.let { showDeleteConfirmDialog(it) }
                    }

                    override fun onActionUp() {
                        removeItem = null

                    }

                })
            val target = CustomEpoxyTouchHelper.initDragging(aiConversationController!!)
                .withRecyclerView(binding.ryAiBot)
                .withDirections(ItemTouchHelper.DOWN or ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT)
                .withTarget(AiBotConversationItem::class.java)
            touchHelper = target.andCallbacks(
                viewLifecycleOwner,
                object : CustomEpoxyTouchHelper.DragCallbacks<AiBotConversationItem>() {
                    override fun onDragStarted(
                        model: AiBotConversationItem,
                        itemView: View,
                        adapterPosition: Int
                    ) {
                        if (!imgCleared) return
                        imgCleared = false
                        removeItem = model
                        SimpleVibratorUtil.vibrateClick()
                        binding.dragDeleteView.setDragView(itemView)

                    }

                    override fun onModelMoved(
                        fromPosition: Int,
                        toPosition: Int,
                        modelBeingMoved: AiBotConversationItem,
                        itemView: View
                    ) {

                    }

                    override fun clearView(model: AiBotConversationItem, itemView: View) {
                        imgCleared = true

                    }
                }
            )
            viewModel.getAiBotConversationList(true)
        }
    }

    private fun buildAiConversationController() = simpleController(
        viewModel,
        ConversationListState::aiConversationList,
        ConversationListState::loadMore,
    ) { list, loadMore ->
        val width = if (list.size <= 4) {
            75.dp.toFloat()
        } else {
            (((ScreenUtil.screenWidth - (16.dp * 5)) / 5) * 1.1).toFloat()
        }
        list.forEachIndexed { index, conversation ->
            AiBotConversation(index, width, conversation, ::glide) {
                MetaRouter.AiBot.gotoConversation(
                    this@ConversationListFragment,
                    it.botId,
                    it.id,
                    "2"
                )
            }
        }
        loadMoreFooter(loadMore, spanSize = 5, showEnd = false, width = width) {
            viewModel.getAiBotConversationList(false)
        }
    }

    private fun showDeleteConfirmDialog(modelBeingMoved: AiBotConversationItem) {
        SimpleVibratorUtil.vibrateClick()

        ConfirmDialog.Builder(this)
            .content(getString(R.string.conversation_remove_msg_resure))
            .cancelBtnTxt(getString(R.string.cancel), lightBackground = false)
            .confirmBtnTxt(getString(R.string.friend_chat_delete), lightBackground = true)
            .image(R.drawable.dialog_icon_cry)
            .isRed(true)
            .cancelCallback {
                binding.dragDeleteView.getDragView()
                    ?.let {
                        it.alpha = 1f
                        touchHelper?.onChildViewDetachedFromWindow(it)
                    }
                binding.dragDeleteView.removeView()
                removeItem = null
            }
            .confirmCallback {
                binding.dragDeleteView.getDragView()
                    ?.let {
                        it.alpha = 0f
                        touchHelper?.onChildViewDetachedFromWindow(it)
                    }
                viewModel.deleteAiConversation(modelBeingMoved)
                binding.dragDeleteView.removeView()
                removeItem = null
            }
            .navigate()
    }


    private fun showPopMenu(view: View, metaConversation: MetaConversation?) {
        metaConversation ?: return
        PopWindowUtil.PopupWindowBuilder(requireActivity())
            .setView(R.layout.pop_im_conversation_menu)
            .size(
                LinearLayout.LayoutParams.WRAP_CONTENT.toFloat(),
                LinearLayout.LayoutParams.WRAP_CONTENT.toFloat()
            )
            .setFocusable(true)
            .setTouchable(true)
            .setOutsideTouchable(true)
            .create().apply {
                showAtLocation(view)
                getView<TextView>(R.id.menu_top)?.let {
                    it.text =
                        if (metaConversation.isTop == true) requireContext().getString(R.string.im_conversation_menu_cancel_top)
                        else requireContext().getString(R.string.im_conversation_menu_top)
                    it.setOnAntiViolenceClickListener {
                        val opType = if (metaConversation.isTop == true) "untop" else "top"
                        Analytics.track(EventConstants.EVENT_LONG_CLICK_CONVERSATION_LIST_TOP) {
                            put("type", opType)
                        }
                        viewModel.setConversationToTop(metaConversation)
                        viewModel.refreshConversationList()
                        viewModel.getUnReadCount()
                        dissmiss()
                    }
                }
                getView<TextView>(R.id.menu_remove)?.setOnAntiViolenceClickListener {
                    Analytics.track(EventConstants.EVENT_LONG_CLICK_CONVERSATION_LIST_DELETE)
                    showDeleteConversationConfirm(metaConversation)
                    dissmiss()
                }
            }
    }

    private fun showDeleteConversationConfirm(metaConversation: MetaConversation) {
        ConfirmDialog.Builder(this)
            .content(resources.getString(R.string.conversation_remove_msg_resure))
            .cancelBtnTxt(resources.getString(R.string.dialog_cancel), lightBackground = false)
            .confirmBtnTxt(
                resources.getString(R.string.im_conversation_menu_remove),
                lightBackground = true
            )
            .isRed(true)
            .confirmCallback {
                viewModel.removeConversation(metaConversation)
                viewModel.refreshConversationList()
                viewModel.getUnReadCount()
            }.navigate()
    }

    override fun onResume() {
        super.onResume()
        Analytics.track(EventConstants.EVENT_IM_MESSAGES_TAB_SHOW)
        viewModel.getUnReadCount()
        viewModel.getEditorNoticeUnreadCount()
        viewModel.getNotice()
        viewModel.loadSys()
        viewModel.checkNotificationPermission(requireContext(), isGotSet)
        viewModel.checkCreateGroupPower()
        viewModel.getGroupChatCount()
        viewModel.getGroupPendingRequestCount()
        isGotSet = false

        Timber.tag("SimpleViewCache_S").d("ConversationListFragment onResume 可视耗时: ${System.currentTimeMillis() - startCreateTime}")
    }

    override fun onDestroyView() {
        // 退出会话列表页面时, 刷新一下未读消息数
        mainViewModel.getSysUnread()

        binding.rvConversationList.adapter = null
        binding.refresh.setOnRefreshListener(null)
        binding.dragDeleteView.unDestroyView(binding.ryAiBot)
        touchHelper = null
        super.onDestroyView()
    }

    override fun invalidate() {

    }

}