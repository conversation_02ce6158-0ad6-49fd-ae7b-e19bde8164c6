package com.socialplay.gpark.ui.account

import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.FragmentLogoutComfirmDialogBinding
import com.socialplay.gpark.function.analytics.PageNameConstants.FRAGMENT_LOGOUT_DIALOG
import com.socialplay.gpark.ui.core.BaseBottomSheetDialogFragment
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.util.ClipBoardUtil
import com.socialplay.gpark.util.ShortToast.showToast
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.setFragmentResultByActivity
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class LogoutDialogFragment : BaseBottomSheetDialogFragment() {
    companion object {
        private const val PARAM_ID = "param_id"

        private const val REQUEST_KEY = "logoutConfirmDialog_request_key"
        private const val KEY_CANCEL = 0
        private const val KEY_CONFIRM = 1

        fun show(
            fragment: Fragment,
            id: String,
            onCancel: () -> Unit,
            onLogOut: () -> Unit = {}
        ) {
            val bundle = Bundle().apply { putString(PARAM_ID, id) }
            val dialog = LogoutDialogFragment().apply { arguments = bundle }
            val activity = fragment.activity ?: return
            setFragmentResult(activity, fragment, onCancel, onLogOut)
            dialog.show(fragment.childFragmentManager, "LogoutConfirmDialog${System.currentTimeMillis()}")
        }

        fun setFragmentResult(
            activity: FragmentActivity,
            lifecycleOwner: LifecycleOwner,
            onCancel: () -> Unit,
            onLogOut: () -> Unit
        ) {
            activity.supportFragmentManager.setFragmentResultListener(REQUEST_KEY, lifecycleOwner) { key, result ->
                if(key != REQUEST_KEY) return@setFragmentResultListener
                when(result.getInt(REQUEST_KEY, KEY_CANCEL)) {
                    ConfirmDialog.CONFIRM -> onLogOut.invoke()
                    ConfirmDialog.CANCEL -> onCancel.invoke()
                }
            }
        }
    }

    override val binding: FragmentLogoutComfirmDialogBinding by viewBinding(FragmentLogoutComfirmDialogBinding::inflate)
    private var result: Int = KEY_CANCEL

    override fun isClickOutsideDismiss(): Boolean {
        return true
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 平板上使用全高，避免横屏时内容显示不全
        if (isPad) {
            heightPercent = 1.0f
        }
    }

    override fun init() {
        skipCollapsed()
        val id = arguments?.getString(PARAM_ID)
        val text = SpannableHelper.Builder()
            .text(getString(R.string.logout_uuid_tips)).colorRes(R.color.color_999999)
            .text(id).colorRes(R.color.color_D87607).bold(true)
            .build()
        binding.tvId.text = text
        binding.tvId.setOnClickListener {
            lifecycleScope.launch(Dispatchers.IO) {
                ClipBoardUtil.setClipBoardContent(id, requireContext())
            }
            showToast(requireContext(), getString(R.string.copy_success))
        }

        binding.ivClose.setOnAntiViolenceClickListener {
            result = KEY_CANCEL
            dismissAllowingStateLoss()
        }

        binding.tvConfirmBtn.setOnAntiViolenceClickListener {
            result = KEY_CONFIRM
            dismissAllowingStateLoss()
        }

        binding.bottomContainer.setOnAntiViolenceClickListener {
            dismiss()
        }
    }

    override fun needCountTime(): Boolean = false

    override fun getPageName(): String {
        return FRAGMENT_LOGOUT_DIALOG
    }

    override fun gravity(): Int {
        return Gravity.BOTTOM
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        setFragmentResultByActivity(REQUEST_KEY, bundleOf(REQUEST_KEY to result))
    }
}