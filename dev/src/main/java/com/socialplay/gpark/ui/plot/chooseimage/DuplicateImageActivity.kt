package com.socialplay.gpark.ui.plot.chooseimage

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Parcelable
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.bin.cpbus.CpEventBus
import com.luck.picture.lib.basic.PictureSelectionModel
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.meta.biz.ugc.model.DuplicateImageCallbackMsg
import com.meta.biz.ugc.protocol.UGCProtocolSender
import com.meta.biz.ugc.protocol.constants.ProtocolSendConstant
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.UploadFileInteractor
import com.socialplay.gpark.data.interactor.UploadFileInteractor.Companion.BITRATE
import com.socialplay.gpark.data.model.event.ts.DuplicateImageEvent
import com.socialplay.gpark.function.download.DownloadFileProvider
import com.socialplay.gpark.function.startup.StartupProcessType
import com.socialplay.gpark.function.startup.core.StartupContext
import com.socialplay.gpark.function.videocompression.ISiliCompressor
import com.socialplay.gpark.util.BitmapRecycleUtil
import com.socialplay.gpark.util.FileUtil
import com.socialplay.gpark.util.Md5Util.md5
import com.socialplay.gpark.util.bitmap.BitmapUtil
import com.socialplay.gpark.util.extension.likelyPath
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.singleOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.parcelize.Parcelize
import org.koin.core.context.GlobalContext
import timber.log.Timber
import java.io.File


/**
 * Created by bo.li
 * Date: 2024/6/3
 * Desc: 游戏引擎公共-选择图片，裁剪
 */
@Parcelize
data class DuplicateImageActivityArgs(
    val ratioWidth: Int,
    val ratioHeight: Int,
    val mimeType: List<String>?,
    val maxFileSize: Long,
    val folderPath: String,
    val messageId: Int,
    val fromProcess: String,
    val bizCode: String?,
    val compressVideo: Boolean?,
    val maxVideoSecond: Int?,
    val minVideoSecond: Int?,
) : Parcelable

class DuplicateImageActivity : BaseChooseImageActivity() {
    private var args: DuplicateImageActivityArgs? = null

    private val uploadFileInteractor: UploadFileInteractor by lazy { GlobalContext.get().get() }

    // 是否来自m进程
    private var fromProcessM: Boolean = false

    companion object {
        const val EXTRA_KEY_PARAMS_DUPLICATE = "duplicate_image_extra_params"
    }

    override fun initArgs() {
        args = intent.getParcelableExtra(EXTRA_KEY_PARAMS_DUPLICATE)
        fromProcessM = args?.fromProcess == StartupProcessType.M.desc
        if (args == null) {
            callbackError("activity parsing params is null", 1005)
            finish()
            return
        }
        binding.fcvClip.isVisible = false
    }

    override fun getSelectMimeType(): Int {
        val hasVideo = args?.mimeType?.any { PictureMimeType.isHasVideo(it) } ?: false
        val hasImage = args?.mimeType?.any { PictureMimeType.isHasImage(it) } ?: false
        return when {
            hasVideo && hasImage -> SelectMimeType.ofAll()
            hasVideo -> SelectMimeType.ofVideo()
            else -> SelectMimeType.ofImage()
        }
    }

    override fun assemblePictureBuilder(builder: PictureSelectionModel): PictureSelectionModel {
        return builder.setMimeType().setMaxFileSize().setVideoFilter()
            .isPreviewImage(true)
            .setSkipCropMimeType(PictureMimeType.ofGIF())
    }

    private fun PictureSelectionModel.setMimeType(): PictureSelectionModel {
        return args?.mimeType?.toTypedArray()?.let {
            if (it.contains(PictureMimeType.ofGIF())) {
                isGif(true)
            }
            setQueryOnlyMimeType(*it)
        } ?: this
    }

    private fun PictureSelectionModel.setMaxFileSize(): PictureSelectionModel {
        return args?.maxFileSize?.takeIf { it > 0 }?.let {
            setSelectMaxFileSize(it)
        } ?: this
    }

    private fun PictureSelectionModel.setVideoFilter(): PictureSelectionModel {
        args?.maxVideoSecond?.takeIf { it > 0 }?.also {
            setFilterVideoMaxSecond(it)
            setRecordVideoMaxSecond(it)
        }
        args?.minVideoSecond?.takeIf { it > 0 }?.also {
            setFilterVideoMinSecond(it)
            setRecordVideoMinSecond(it)
        }
        return this
    }

    override fun callbackError(message: String?, code: Int) {
        callUEResult(null, message, code)
    }

    private fun callUEResult(path: String?, message: String?, code: Int) {
        if (fromProcessM && StartupContext.get().processType != StartupProcessType.M) {
            CpEventBus.post(
                DuplicateImageEvent(
                    args?.messageId ?: 0,
                    path,
                    message,
                    code
                )
            )
        } else {
            UGCProtocolSender.sendProtocol(
                ProtocolSendConstant.BRIDGE_ACTION_DUPLICATE_IMAGE,
                args?.messageId ?: 0,
                DuplicateImageCallbackMsg(
                    message = message ?: "",
                    code = code,
                    imagePath = path,
                )
            )
        }
        finish()
    }

    override fun onSelectResult(result: ArrayList<LocalMedia>) {
        args?.let {
            val selectMedia = result.firstOrNull()
            val path = selectMedia?.likelyPath
            if (selectMedia == null || path.isNullOrEmpty()) {
                callbackError("user cancelled", 2003)
                return
            }
            lifecycleScope.launch(Dispatchers.Main) {
                val (destPath, msg) = dispatchProcessSelectResult(selectMedia)
                binding.lv.hide()
                if (destPath.isNullOrEmpty()) {
                    callbackError("image processing failed, message:${msg}", 1006)
                } else {
                    callUEResult(destPath, null, 200)
                }
            }
        }
    }

    private suspend fun dispatchProcessSelectResult(selectMedia: LocalMedia): Pair<String?, String?> {
        return if (PictureMimeType.isHasImage(selectMedia.mimeType)) {
            binding.lv.showLoading(loadingTips = getString(R.string.processing_image))
            if (PictureMimeType.isHasGif(selectMedia.mimeType)) {
                processGif(selectMedia)
            } else {
                processImage(selectMedia)
            }
        } else if (PictureMimeType.isHasVideo(selectMedia.mimeType)) {
            binding.lv.showLoading(loadingTips = getString(R.string.processing_video))
            processVideo(selectMedia)
        } else {
            null to "not support mimeType:${selectMedia.mimeType}"
        }
    }

    // 图片文件扩展名，包含点
    private fun getImageFileExtensionWithDot(file: File): String {
        val extension = "." + file.extension.lowercase()
        return when (extension) {
            PictureMimeType.JPG.lowercase() -> PictureMimeType.JPG
            PictureMimeType.JPEG.lowercase() -> PictureMimeType.JPEG
            PictureMimeType.PNG.lowercase() -> PictureMimeType.PNG
            PictureMimeType.WEBP.lowercase() -> PictureMimeType.WEBP
            else -> PictureMimeType.PNG
        }
    }

    private fun getImageCompressFormat(extension: String): Bitmap.CompressFormat {
        return when (extension) {
            PictureMimeType.JPG, PictureMimeType.JPEG -> Bitmap.CompressFormat.JPEG
            PictureMimeType.PNG -> Bitmap.CompressFormat.PNG
            PictureMimeType.WEBP -> Bitmap.CompressFormat.WEBP
            else -> Bitmap.CompressFormat.PNG
        }
    }

    private suspend fun processImage(media: LocalMedia): Pair<String?, String?> {
        val imgPath = media.likelyPath ?: ""
        return withContext(Dispatchers.IO) {
            var tempBm: Bitmap? = null
            var processingBitmap: Bitmap? = null
            var cacheFile: File? = null
            return@withContext kotlin.runCatching {
                val originFile = File(imgPath)
                val imageDotExtension = getImageFileExtensionWithDot(originFile)
                if (!originFile.exists()) {
                    return@withContext null to "file $imgPath not exist"
                }
                cacheFile = getCachePhotoPath("duplicateImageCache${imageDotExtension}")
                if (cacheFile!!.exists()) {
                    cacheFile?.delete()
                }
                cacheFile?.parentFile?.mkdirs()
                FileUtil.copyFile(originFile, cacheFile)
                val ratioWidth = args?.ratioWidth ?: 0
                val ratioHeight = args?.ratioHeight ?: 0
                if (ratioWidth > 0 && ratioHeight >= 0) {
                    tempBm = BitmapUtil.compressBitmapForWidth(cacheFile!!.path, ratioWidth)
                    processingBitmap = BitmapUtil.centerCrop(tempBm, ratioWidth, ratioHeight)
                } else {
                    processingBitmap = BitmapFactory.decodeFile(cacheFile?.path)
                }
                val destFile = getDestMediaPath(".jpeg")
                val result: Pair<String?, String?> = if (destFile != null) {
                    BitmapUtil.saveToLocal(processingBitmap, destFile, 90, getImageCompressFormat(imageDotExtension))
                    destFile.path to null
                } else {
                    val uploadFile = getCachePhotoPath("${System.currentTimeMillis()}${imageDotExtension}")
                    BitmapUtil.saveToLocal(processingBitmap, uploadFile, 99, getImageCompressFormat(imageDotExtension))
                    val uploadResult = uploadFile(uploadFile)
                    uploadFile.takeIf { it.exists() }?.delete()
                    uploadResult ?: (null to "not found bizCode and local path")
                }
                BitmapRecycleUtil.safeRecycle(tempBm)
                BitmapRecycleUtil.safeRecycle(processingBitmap)
                cacheFile?.delete()
                result
            }.getOrElse {
                kotlin.runCatching {
                    BitmapRecycleUtil.safeRecycle(tempBm)
                    BitmapRecycleUtil.safeRecycle(processingBitmap)
                    cacheFile?.delete()
                }
                null to it.message
            }
        }
    }

    private suspend fun processGif(media: LocalMedia): Pair<String?, String?> {
        val imgPath = media.likelyPath ?: ""
        return withContext(Dispatchers.IO) {
            return@withContext kotlin.runCatching {
                val originFile = File(imgPath)
                if (!originFile.exists()) {
                    return@withContext null to "file $imgPath not exist"
                }
                val destFile = getDestMediaPath(".gif")
                val result: Pair<String?, String?> = if (destFile != null) {
                    FileUtil.copyFile(originFile, destFile)
                    destFile.path to null
                } else {
                    val uploadFile = getCachePhotoPath("${System.currentTimeMillis()}.gif")
                    FileUtil.copyFile(originFile, uploadFile)
                    val uploadResult = uploadFile(uploadFile)
                    uploadFile.takeIf { it.exists() }?.delete()
                    uploadResult ?: (null to "not found bizCode and local path")
                }
                result
            }.getOrElse {
                null to it.message
            }
        }
    }

    private suspend fun processVideo(media: LocalMedia): Pair<String?, String?> = runCatching {
        val path = media.likelyPath ?: return@runCatching null to "file not exist"
        val originFile = File(path)
        if (!originFile.exists()) {
            return@runCatching null to "file $path not exist"
        }
        val cacheFile = getCachePhotoPath("duplicateVideoCache.mp4")
        if (cacheFile.exists()) {
            cacheFile.delete()
        }
        cacheFile.parentFile?.mkdirs()
        FileUtil.copyFile(originFile, cacheFile)
        val compressFile = compressVideo(cacheFile, media.width, media.height)
        val destFile = getDestMediaPath(".mp4")
        val result: Pair<String?, String?> = if (destFile != null) {
            compressFile.copyTo(destFile, true)
            destFile.absolutePath to null
        } else {
            uploadFile(compressFile) ?: (null to "not found bizCode and local path")
        }
        result
    }.getOrElse {
        null to it.message
    }

    private suspend fun compressVideo(file: File, width: Int, height: Int): File =
        withContext(Dispatchers.IO) {
            if (args?.compressVideo == true) return@withContext file
            val originWidth = width.takeIf { it > 0 } ?: -1
            val originHeight = height.takeIf { it > 0 } ?: -1
            val compressedFilePath = FileUtil.createMp4FileInBox()
            ISiliCompressor.compressVideo(
                this@DuplicateImageActivity, file.absolutePath, compressedFilePath,
                originWidth, originHeight, BITRATE
            )
            val compressFile = File(compressedFilePath)
            //压缩出错或者压缩后变大直接使用原视频
            return@withContext if (compressFile.length() <= 1024 || compressFile.length() > file.length()) {
                //视频压缩后长度为0表示压缩出错了
                Timber.d("编辑器视频压缩太小了 %s", "七牛云上传后再次压缩变成0kb")
                file
            } else {
                Timber.d("编辑器视频压缩后 大小:%s", +compressFile.length() / 1024)
                compressFile
            }
        }

    /**
     * 返回结果：
     * 成功 to 原因
     */
    private suspend fun uploadFile(file: File, delete: Boolean = true) = runCatching {
        val bizCode = args?.bizCode ?: return@runCatching null
        val result = uploadFileInteractor.uploadSingle(bizCode, file, true).singleOrNull()
        if (result?.succeeded == true && delete) {
            file.delete()
            result.data?.url to null
        } else {
            null to (result?.message ?: "upload failed")
        }
    }.getOrElse {
        null to (it.message ?: "upload error")
    }

    private fun getCachePhotoPath(name: String): File {
        return File("${DownloadFileProvider.chooseImgCacheDir}/${name}")
    }

    private fun getDestMediaPath(nameSuffix: String): File? {
        val folderPath = args?.folderPath
        if (folderPath.isNullOrEmpty()) return null
        val fileName = folderPath.md5()
        return File("${args?.folderPath}/${fileName}${nameSuffix}")
    }
}