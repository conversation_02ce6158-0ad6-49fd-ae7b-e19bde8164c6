package com.socialplay.gpark.ui.login

import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.TextPaint
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.text.style.ClickableSpan
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnFocusChangeListener
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.activity.addCallback
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import com.airbnb.mvrx.fragmentViewModel
import com.google.android.material.textfield.TextInputEditText
import com.meta.pandora.Pandora
import com.meta.verse.lib.loader.env.HostEnv.isParty
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor
import com.socialplay.gpark.data.model.LOGIN_TYPE_GPARK_ID
import com.socialplay.gpark.data.model.LoginPageSource
import com.socialplay.gpark.data.model.LoginSource
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.event.AuthorizeResultEvent
import com.socialplay.gpark.data.model.login.LoginCompleteEvent
import com.socialplay.gpark.data.model.mgs.ViolateMessage
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.databinding.FragmentLoginBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.LoginCompleteRouter
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.dialog.LoadingDialogFragment
import com.socialplay.gpark.ui.view.InterceptClickEventLinkMovementMethod
import com.socialplay.gpark.util.ExpandCollapseHelper
import com.socialplay.gpark.util.ExpandCollapseHelper.Companion.KEY_SAVED_STATE
import com.socialplay.gpark.util.InputUtil
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.SoftKeyboardUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.popBackStack
import com.socialplay.gpark.util.extension.registerEventBus
import com.socialplay.gpark.util.extension.resumeGameById
import com.socialplay.gpark.util.extension.setFontFamily
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unregisterEventBus
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.android.ext.android.inject
import timber.log.Timber

/**
 * @author: ning.wang
 * @date: 2021-09-24 12:04 下午
 * @desc:
 */
class LoginFragment : BaseFragment<FragmentLoginBinding>(R.layout.fragment_login) {
    private val viewModel: LoginViewModel by fragmentViewModel()
    private val args by navArgs<LoginFragmentArgs>()
    private val h5PageConfig by inject<H5PageConfigInteractor>()
    private var expandHelper: ExpandCollapseHelper? = null

    private var loadingDialogFragment: LoadingDialogFragment? = null
    private var isThirdAuthorizeLoggedIn = false
    private var isSmallScreen = false

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentLoginBinding {
        return FragmentLoginBinding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.setArgs(args)
        Analytics.track(EventConstants.EVENT_LOGIN_PAGE_SHOW) {
            put(EventConstants.KEY_LOGIN_SOURCE, args.source ?: LoginSource.Unknown.source)
        }

//        if (args.source != LoginSource.ThirdAppAuthorize.source && !EventBus.getDefault().isRegistered(this)) {
//            EventBus.getDefault().register(this)
//        }
    }

    @Subscribe
    fun onEvent(event: LoginCompleteEvent) {
        try {
            popBackStack()
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        outState.putBundle(KEY_SAVED_STATE, expandHelper?.onSaveInstanceState())
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initExpandState(savedInstanceState)
        registerEventBus()
        initView()
        initObverse()
        adjustUIForScreenDensity()
    }

    private fun initExpandState(savedInstanceState: Bundle?) {
        if(!isParty()) return
        expandHelper = ExpandCollapseHelper(binding.clLoginByPhone)
        savedInstanceState?.let {
            expandHelper?.onRestoreInstanceState(it)
        }
    }

    private fun initView() = binding.apply {
        Timber.i("source====${args.source}")
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            goBack(false)
        }

        visibleList(tvSwitchLoginWay, ivSwitchLoginWay, vSwitchLoginWay, visible = !isParty())
        ivSwitchLoginWay.setImageResource(if(isParty()) R.drawable.ic_mobile_phone else R.drawable.ic_login_way_switch)

        // 协议富文本
        val agreementContent = getAgreementStringBuilder()
        tvAgreement.text = agreementContent
        tvAgreement.movementMethod = InterceptClickEventLinkMovementMethod(tvAgreement)
        tvAgreement.isClickable = false
        tvAgreement.isContextClickable = false

        // 协议同意
        vHotCheck.setOnClickListener {
            viewModel.toggleAgreeLogin(!cbAgree.isChecked)
        }

        // 第三方登录方式
        val items: List<LoginItemData> = viewModel.fetchLoginOthers()
        addLoginItems(items)

        // 登录按钮
        tvLogin.setOnAntiViolenceClickListener {
            InputUtil.hideKeyboard(it)

            Analytics.track(EventConstants.EVENT_LOGIN_PAGE_LOGIN_CLICK) {
                put(EventConstants.KEY_LOGIN_WAY, LoginWay.Account.way)
            }
            checkCanLogin {
                viewModel.authAccount()
            }
        }

        // 账户输入清空按钮
        ivClearAccount.setOnAntiViolenceClickListener {
            etAccount.setText("")
            vAccountBlock.visible(false)
        }

        // 密码输入清空按钮
        ivClearPassword.setOnAntiViolenceClickListener {
            etPassword.setText("")
        }

        // 密码可见性按钮
        ivPasswordVisibility.setOnClickListener {
            viewModel.togglePasswordVisibility()
        }

        // 账户输入框
        etAccount.addTextChangedListener {
            viewModel.postAccountValueChanged(it?.toString())
            updateClearAccountVisibility()
            setFont(etAccount)
        }
        etAccount.onFocusChangeListener = OnFocusChangeListener { _, focus ->
            updateAccountUI(focus, viewModel.oldState.loginMode)
            updateClearAccountVisibility()
            updateFocusChanged()
        }

        // 密码输入框
        etPassword.addTextChangedListener {
            viewModel.postPasswordValueChanged(it?.toString())
            updateClearPasswordVisibility()
            setFont(etPassword)
        }
        etPassword.onFocusChangeListener = OnFocusChangeListener { _, focus ->
            updatePasswordUI(focus)
            updateFocusChanged()
            updateClearPasswordVisibility()
        }

        // 导航按钮
        tbl.setOnBackAntiViolenceClickedListener {
            goBack(false)
        }

        // 电话号登录按钮
        vPhoneLoginView.setOnAntiViolenceClickListener {
            MetaRouter.Login.loginByPhone(this@LoginFragment, loginSource = args.source ?: "")
        }

        // 忘记密码按钮
        tvForgotPassword.setOnAntiViolenceClickListener {
            Analytics.track(EventConstants.EVENT_LOGIN_CLICK_FORGOT)
            MetaRouter.Account.passwordForget(this@LoginFragment, LoginPageSource.Login, args.gid)
        }

        // 年龄限制标识
        if (EnvConfig.isParty()) {
            ivAgeRestriction.visible(true)
            ivAgeRestriction.setImageResource(LoginSpecialWrapper.getAgeRestrictionIconRes())
            ivAgeRestriction.setOnAntiViolenceClickListener {
                LoginSpecialWrapper.showAgeRestrictionDialog(requireActivity(), this@LoginFragment)
            }
        } else {
            ivAgeRestriction.visible(false)
        }

        // 登录帮助按钮
        tvLoginHelp.setOnAntiViolenceClickListener {
            showLoginHelpDialog(false, args.gid)
        }

        // 默认来这里都先隐藏键盘
        SoftKeyboardUtil.hideSoftKeyboard(this@LoginFragment)
    }

    private fun initObverse() {
        // 监听密码可见性
        viewModel.onEach(
            LoginViewModelState::passwordVisibility
        ) {
            binding.apply {
                if (it) {
                    ivPasswordVisibility.setImageResource(R.drawable.icon_login_visible_password)
                    etPassword.transformationMethod = HideReturnsTransformationMethod.getInstance()
                } else {
                    ivPasswordVisibility.setImageResource(R.drawable.icon_login_hiden_password)
                    etPassword.transformationMethod = PasswordTransformationMethod.getInstance()
                }
                etPassword.setSelection(etPassword.length())
            }
        }

        // 账号密码是否有效
        viewModel.onEach(
            LoginViewModelState::isAccountAndPasswordValid
        ) {
            binding.tvLogin.enableWithAlpha(it)
        }

        // 登录状态
        viewModel.onEach(
            LoginViewModelState::signStatus
        ) {
            when(it) {
                is LoginState.Loading -> {
                    println("LoginState.Loading")
                }

                is LoginState.Succeeded -> {
                    dismissLoading()
                    goBack(true, it.userInfo?.firstBind == true, it.userInfo)
                }

                is LoginState.Failed -> {
                    dismissLoading()
                    val msg = it.message
                    if (it.violateMessage != null) {
                        showViolateDialogByInfo(it.violateMessage)
                    } else if (msg.isNotBlank()) {
                        toast(msg)
                    }
                }

                is LoginState.UserCanceled -> {
                    dismissLoading()
                }

                null -> {
                    println("null")
                }
            }
        }

        if(!EnvConfig.isParty()) {
            // 只有Gpark才需要切换登录模式
            viewModel.onEach(
                LoginViewModelState::loginMode
            ) {
                when (it) {
                    LoginViewModel.MODE_ACCOUNT_EMAIL -> {
                        updateAccountUI(
                            binding.etAccount.hasFocus(),
                            LoginViewModel.MODE_ACCOUNT_EMAIL
                        )
                        binding.tvSwitchLoginWay.setText(R.string.login_by_gpark_id)
                    }

                    LoginViewModel.MODE_GPARK_ID -> {
                        updateAccountUI(
                            binding.etAccount.hasFocus(),
                            LoginViewModel.MODE_GPARK_ID
                        )
                        binding.tvSwitchLoginWay.setText(R.string.login_by_account_email)
                    }

                    else -> {
                        return@onEach
                    }
                }

                val currentAccount = viewModel.oldState.currentAccount.orEmpty()
                if (binding.etAccount.text?.toString() != currentAccount) {
                    binding.etAccount.setText(currentAccount)
                }
            }
        } else {
            binding.vSwitchLoginWay.setOnClickListener {
                MetaRouter.Login.loginByPhone(
                    this@LoginFragment,
                    loginSource = args.source ?: ""
                )
            }
        }

        // 登录信息更新
        viewModel.onEach(
            LoginViewModelState::continueAccountInfo,
            LoginViewModelState::lastLoginType,
            LoginViewModelState::isContinueLogin
        ) { info, lastLoginType, isContinueLogin ->
            val loginType = info?.loginType ?: lastLoginType
            updateContinueLoginUI(isContinueLogin, loginType, info)
        }

        // 协议同意状态
        viewModel.onEach(
            LoginViewModelState::isAgreeLogin
        ) {
            binding.cbAgree.isChecked = it
        }
    }

    /**
     * 根据屏幕密度动态调整UI，解决在低分辨率设备上的UI重叠问题
     */
    private fun adjustUIForScreenDensity() {
        val displayMetrics = resources.displayMetrics
        val density = displayMetrics.densityDpi

        // 针对xhdpi(320dpi)及以下设备进行特殊处理
        if (density <= 320) {
            isSmallScreen = true
            // 调整文字大小
            binding.ivLoginAccountTip.textSize = resources.getDimension(R.dimen.login_title_text_size) / displayMetrics.density
            binding.tvAgreement.textSize = resources.getDimension(R.dimen.login_agreement_text_size) / displayMetrics.density

            // 调整间距和高度
            val loginButtonParams = binding.tvLogin.layoutParams
            loginButtonParams.height = resources.getDimensionPixelSize(R.dimen.login_button_height)
            binding.tvLogin.layoutParams = loginButtonParams

            val inputAccountParams = binding.inputAccount.layoutParams as ViewGroup.MarginLayoutParams
            inputAccountParams.height = resources.getDimensionPixelSize(R.dimen.login_input_height)
            inputAccountParams.topMargin = resources.getDimensionPixelSize(R.dimen.login_vertical_spacing)
            binding.inputAccount.layoutParams = inputAccountParams

            val inputPasswordParams = binding.inputPassword.layoutParams as ViewGroup.MarginLayoutParams
            inputPasswordParams.height = resources.getDimensionPixelSize(R.dimen.login_input_height)
            inputPasswordParams.topMargin = resources.getDimensionPixelSize(R.dimen.login_vertical_spacing)
            binding.inputPassword.layoutParams = inputPasswordParams

            // 调整底部协议区域的边距
            val agreementParams = binding.clAgreement.layoutParams as ViewGroup.MarginLayoutParams
            agreementParams.bottomMargin = resources.getDimensionPixelSize(R.dimen.dp_24)
            binding.clAgreement.layoutParams = agreementParams

            // 确保内容可滚动
            binding.rlContentArea.isFillViewport = true
        }
    }

    // 更新登录UI
    private fun updateContinueLoginUI(
        continueLogin: Boolean,
        loginType: String?,
        info: ContinueAccountInfo?
    ) = binding.apply {
        if (EnvConfig.isParty()) {
            tvSwitchLoginWay.setText(R.string.login_by_phone)
            vSwitchLoginWay.setOnClickListener {
                MetaRouter.Login.loginByPhone(
                    this@LoginFragment,
                    loginSource = args.source ?: ""
                )
            }
        } else {
            when(loginType) {
                // 当继续登录信息不为空时,切换登录模式到继续登录信息的对应登录模式
                LoginWay.Account.way -> {
                    if(info != null && continueLogin) {
                        viewModel.updateMode(LoginViewModel.MODE_ACCOUNT_EMAIL)
                        binding.etAccount.setText(info.loginKey)
                    }
                }

                LOGIN_TYPE_GPARK_ID -> {
                    if(info != null && continueLogin) {
                        viewModel.updateMode(LoginViewModel.MODE_GPARK_ID)
                        binding.etAccount.setText(info.loginKey)
                    }
                }
            }

            viewLifecycleOwner.lifecycleScope.launchWhenResumed {
                delay(100)
                InputUtil.showSoftBoard(binding.etAccount)
            }

            // 切换登录方式只有Gpark且当前继续登录不可用才可切换
            if(info == null) {
                vSwitchLoginWay.setOnClickListener {
                    viewModel.switchMode()
                }
            }
        }

        // 继续登录UI组
        groupContinueLogin.visible(visible = continueLogin)
        groupLogo.visible(visible = !continueLogin)
        // 切换登录方式在继续登录时不可见
        if(continueLogin) visibleList(ivSwitchLoginWay, tvSwitchLoginWay, vSwitchLoginWay, visible = false)
        // 手机号登录，目前只在233派对才支持,且当前不是继续登录的状态才显示手机号登录按钮
        visibleList(binding.clLoginByPhone, visible = !continueLogin && EnvConfig.isParty())

        // 如继续登录信息有效,则需禁止etAccount的输入
        if (continueLogin) {
            binding.tvContinueLogin.text = info?.nickname
            glide?.run {
                load(info?.portrait).placeholder(R.drawable.placeholder_corner_360)
                    .into(binding.ivContinueLogin)
            }
            binding.etAccount.setText(info?.loginKey)
            viewLifecycleOwner.lifecycleScope.launchWhenResumed {
                delay(100)
                InputUtil.showSoftBoard(binding.etPassword)
            }
            updateAccountUI(true, viewModel.oldState.loginMode)
        }
    }

    // 更新账户输入框样式
    private fun updateAccountUI(focus: Boolean, mode: Int) = binding.apply {
        if (focus) {
            inputAccount.setBackgroundResource(
                if (viewModel.oldState.isContinueLogin) R.drawable.bg_white_corner_12
                else R.drawable.bg_white_stroke_f49d0c_corner_12
            )
            inputAccount.setHint(
                if (mode == LoginViewModel.MODE_GPARK_ID) {
                    R.string.gpark_id
                } else {
                    R.string.intl_account_or_email
                }
            )
        } else {
            inputAccount.setBackgroundResource(R.drawable.bg_white_corner_12)
            if (etAccount.text.toString().isBlank()) {
                inputAccount.setHint(
                    if (mode == LoginViewModel.MODE_GPARK_ID) {
                        R.string.intl_enter_gpark_id
                    } else {
                        R.string.intl_enter_account_or_email
                    }
                )
            } else {
                inputAccount.setHint(
                    if (mode == LoginViewModel.MODE_GPARK_ID) {
                        R.string.gpark_id
                    } else {
                        R.string.intl_account_or_email
                    }
                )
            }
        }
    }

    // 更新密码输入相关UI
    private fun updatePasswordUI(focus: Boolean) = binding.apply {
        if (focus) {
            inputPassword.setBackgroundResource(R.drawable.bg_white_stroke_f49d0c_corner_12)
            inputPassword.hint = getString(R.string.intl_password)
        } else {
            inputPassword.setBackgroundResource(R.drawable.bg_white_corner_12)
            if (etPassword.text.toString().isBlank()) {
                inputPassword.hint = getString(R.string.intl_enter_password)
            } else {
                inputPassword.hint = getString(R.string.intl_password)
            }
        }
    }

    // 当账号输入框或密码输入框得到焦点时折叠电话登录按钮
    private fun updateFocusChanged() {
        val focused = binding.etAccount.hasFocus() || binding.etPassword.hasFocus()
        if(isParty()) {
            if(focused) {
                visibleList(
                    binding.tvSwitchLoginWay,
                    binding.ivSwitchLoginWay,
                    binding.vSwitchLoginWay,
                    visible = true
                )
                expandHelper?.collapse()
            } else {
                visibleList(
                    binding.tvSwitchLoginWay,
                    binding.ivSwitchLoginWay,
                    binding.vSwitchLoginWay,
                    visible = false
                )
            }
        }
    }

    // 更新账户清空按钮可见性
    private fun updateClearAccountVisibility() {
        val isNoContent = binding.etAccount.text.isNullOrEmpty()
        val hasFocus = binding.etAccount.hasFocus()

        binding.ivClearAccount.visible(hasFocus && !isNoContent)
    }

    // 更新密码清空按钮可见性
    private fun updateClearPasswordVisibility() {
        val isNoContent = binding.etPassword.text.isNullOrEmpty()
        val hasFocus = binding.etPassword.hasFocus()

        binding.ivClearPassword.visible(hasFocus && !isNoContent)
        binding.ivPasswordVisibility.visible(hasFocus)
    }

    // 登录执行前置条件检查
    private fun checkCanLogin(cont: () -> Unit) {
        if (!EnvConfig.isParty()) {
            // GPark 直接通过
            cont()
            return
        }
        if (!NetUtil.isNetworkAvailable()) {
            toast(R.string.net_unavailable)
            return
        }
        if (binding.cbAgree.isChecked) {
            cont()
            return
        }
        LoginSpecialWrapper.showProtocolDialogBottomFragment(requireActivity(), this) {
            if (!isAdded || isDetached) return@showProtocolDialogBottomFragment // 检查 Fragment 是否已附加
            viewModel.toggleAgreeLogin(true)
            binding.cbAgree.isChecked = true
            Pandora.send(EventConstants.EVENT_CONTRACT_ACCESS) {
                put("state", 0)
            }
            cont()
        }
    }

    // 添加第三方登录Icon
    private fun addLoginItems(items: List<LoginItemData>) {
        val iconSize = 44.dp
        items.forEachIndexed { index, loginItemData ->
            val imageView = ImageView(requireContext())
            val layoutParams = LinearLayout.LayoutParams(
                iconSize,
                iconSize
            )
            layoutParams.leftMargin = if (index == 0) 0 else 20.dp
            imageView.layoutParams = layoutParams
            imageView.setImageResource(loginItemData.loginIcon)
            imageView.setOnAntiViolenceClickListener { it2 ->
                InputUtil.hideKeyboard(it2)
                Analytics.track(EventConstants.EVENT_LOGIN_PAGE_LOGIN_CLICK) {
                    put(EventConstants.KEY_LOGIN_WAY, loginItemData.loginType.way)
                }
                checkCanLogin {
                    showLoading()
                    viewModel.auth(
                        loginItemData.loginType,
                        requireActivity(),
                        args.source ?: LoginSource.Unknown.source,
                        if (args.onlyLogin) LoginType.ExistAccount else LoginType.Unknown
                    )
                }
            }
            binding.llLoginByOtherSdk.addView(imageView)
        }
    }

    private fun setFont(editText: TextInputEditText) {
        if (editText.text?.toString()?.isBlank() == true) {
            editText.setFontFamily(R.font.poppins_regular_400)
        } else {
            editText.setFontFamily(R.font.poppins_semi_bold_600)
        }
    }

    override fun onResume() {
        super.onResume()

        // 防止因用户点击取消打开第三方引用导致loading不消失
        lifecycleScope.launch {
            delay(1000)
            dismissLoading()
        }

        if(!binding.etAccount.hasFocus() && !binding.etPassword.hasFocus()) {
            expandHelper?.restoreState()
        }
        if (isThirdAuthorizeLoggedIn) {
            isThirdAuthorizeLoggedIn = false
            navigateUpToTarget(true)
        }
    }

    override fun onPause() {
        InputUtil.hideKeyboard(binding.root)
        super.onPause()
    }

    private fun isThirdAppAuthorize(): Boolean {
        return args.source == LoginSource.ThirdAppAuthorize.source
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onThirdAuthoredLoginEvent(event: AuthorizeResultEvent) {
        //第三方授权登录成功后通知，解决p12登录成功后，返回app还停留在登录页面问题
        isThirdAuthorizeLoggedIn = event.status == AuthorizeResultEvent.SUCCESS
    }

    private fun navigateUpToTarget(
        isLoginSucceed: Boolean = false,
        isFirstBinding: Boolean = false,
        userInfo: MetaUserInfo? = null
    ) {
        if (isThirdAppAuthorize()) {
            backToAuthorizeApp(isLoginSucceed)
        } else {
            backToTargetPage(isLoginSucceed, isFirstBinding, userInfo)
        }
    }

    private fun backToTargetPage(isLoginSucceed: Boolean, isFirstBinding: Boolean, userInfo: MetaUserInfo? = null) {
        if (requireActivity() is LoginActivity) {
            //如果是LoginActivity就直接干掉
            requireActivity().finish()
        } else if (isLoginSucceed && args.successToMain) {
//            if (isFirstBinding) {
//                // Gpark逻辑，保持原样
//                MetaRouter.Startup.createAvatar(this, true)
//            } else if (PandoraToggle.hasSelectMode) {
//                MetaRouter.Startup.selectMode(this)
//            } else {
            LoginCompleteRouter.router(requireActivity(), this, userInfo)
//            }
        } else {
            // 上一页
            navigateUp()
        }
    }

    private fun backToAuthorizeApp(isLoginSucceed: Boolean = false) {
        EventBus.getDefault().post(
            AuthorizeResultEvent(
                if (isLoginSucceed) {
                    AuthorizeResultEvent.SUCCESS
                } else {
                    AuthorizeResultEvent.CANCEL
                }
            )
        )
    }

    private fun goBack(isLoginSucceed: Boolean, isFirstBinding: Boolean = false, userInfo: MetaUserInfo? = null) {
        val gameId = args.gid
        if (!gameId.isNullOrEmpty()) {
            resumeGameById(gameId)
        }
        navigateUpToTarget(isLoginSucceed, isFirstBinding = isFirstBinding, userInfo = userInfo)
    }



    private fun showViolateDialogByInfo(violateMessage: ViolateMessage) {
        violateMessage.toArgs()?.let { MetaRouter.Report.violateRulesDialog(this, it) }
    }

    private fun getAgreementStringBuilder(): Spannable {
        return LoginSpecialWrapper.getAgreementStringBuilder(this, h5PageConfig)
    }

    inner class SpanClick(val call: () -> Unit) : ClickableSpan() {
        override fun onClick(widget: View) {
            call.invoke()
        }

        override fun updateDrawState(ds: TextPaint) {
            ds.isUnderlineText = false
            ds.color = Color.parseColor("#53535E")
        }

    }

    private fun showLoading() {
        loadingDialogFragment = MetaRouter.Dialog.loading(childFragmentManager, msg = getString(R.string.logging))
    }

    private fun dismissLoading() {
        loadingDialogFragment?.dismissAllowingStateLoss()
        loadingDialogFragment = null
    }

    override fun onDestroyView() {
        dismissLoading()
        super.onDestroyView()
        unregisterEventBus()
    }

    override fun getPageName(): String {
        return PageNameConstants.FRAGMENT_NAME_LOGIN
    }

}