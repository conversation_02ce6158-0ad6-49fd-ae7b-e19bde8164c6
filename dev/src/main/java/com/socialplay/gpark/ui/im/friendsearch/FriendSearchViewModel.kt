package com.socialplay.gpark.ui.im.friendsearch

import android.content.ComponentCallbacks
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.ViewModelContext
import com.socialplay.gpark.data.IMetaRepository
import com.socialplay.gpark.data.base.ApiDataException
import com.socialplay.gpark.data.base.code
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.interactor.AccountInteractor
import com.socialplay.gpark.data.model.account.UserFollowEvent
import com.socialplay.gpark.data.model.community.UserFeedUser
import com.socialplay.gpark.data.model.friend.FriendSearchInfo
import com.socialplay.gpark.data.model.qrcode.QrCodeCreateRequest
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.ui.core.BaseViewModel
import com.socialplay.gpark.ui.core.KoinViewModelFactory
import com.socialplay.gpark.ui.core.views.LoadMoreState
import com.socialplay.gpark.util.ToastData
import com.socialplay.gpark.util.ToastError
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.get

/**
 *  <AUTHOR> taiyuan
 *  time   : 2021/6/23
 *  desc   :
 */

data class FriendSearchExtraState(
    val creatorList: Async<List<UserFeedUser>> = Uninitialized,
    val creatorListLoadMore: Async<LoadMoreState> = Uninitialized,
    val offset: String? = null,
    val qrCodeUrl: String? = null,
    val userInfo: MetaUserInfo? = null,
    val toastMsg: ToastData = ToastData.EMPTY,
    val shareProfileData: Async<ShareRawData.UserExtra> = Uninitialized,
) : MavericksState

class FriendSearchViewModel(
    initialState: FriendSearchExtraState,
    val metaRepository: IMetaRepository,
    val accountInteractor: AccountInteractor
) : BaseViewModel<FriendSearchExtraState>(initialState) {
    private val _searchKeyFlow = MutableStateFlow("")
    val searchKeyFlow: StateFlow<String> = _searchKeyFlow
    private val _clearSearch = MutableStateFlow(true)

    @ExperimentalCoroutinesApi
    @FlowPreview
    val friendSearch: Flow<PagingData<FriendSearchInfo>> =
        _searchKeyFlow.debounce(200)
            .flatMapLatest {
                metaRepository.searchFriends(_clearSearch.value, it, SEARCH_PAGE_SIZE).cachedIn(viewModelScope)
            }
            .catch {

            }

    init {
        EventBus.getDefault().register(this)
        accountInteractor.accountLiveData.observeForever { userInfo ->
            setState {
                copy(
                    userInfo = userInfo
                )
            }
        }
    }

    override fun onCleared() {
        EventBus.getDefault().unregister(this)
        super.onCleared()
    }

    fun searchNewKey(keyWord: String) = viewModelScope.launch {
        _clearSearch.value = false
        _searchKeyFlow.value = keyWord.trim()
    }

    fun clearSearchResult() {
        _clearSearch.value = true
        _searchKeyFlow.value = ""
    }

    fun refresh() {
        fetchQrCode()
        getRecommendUserList()
    }

    fun fetchQrCode() = viewModelScope.launch {
        if (!oldState.qrCodeUrl.isNullOrBlank()) return@launch
        metaRepository.qrCodeCreateFlow(QrCodeCreateRequest.homePage()).collect { result ->
            if (result.succeeded && result.data != null) {
                setState {
                    copy(
                        qrCodeUrl = result.data!!.url
                    )
                }
            }
        }
    }

    fun getRecommendUserList() {
        if (oldState.creatorList is Loading) return

        setState { copy(creatorList = Loading()) }
        metaRepository.getCommunityRecommendUsers(
            pageSize = SEARCH_PAGE_SIZE,
            offset = null,
        ).execute {
            when(it) {
                is Success -> {
                    val recommendUser = it().list ?: emptyList()
                    val offset = it().offset
                    val end =
                        it().hasMore == false || recommendUser.isEmpty() || offset.isNullOrEmpty()

                    val newList = recommendUser.distinctBy { user -> user.uid }
                    copy(
                        creatorList = Success(newList),
                        offset = offset,
                        creatorListLoadMore = Success(LoadMoreState(end))
                    )
                }
                else -> oldState
            }
        }
    }

    fun loadMoreCreatorInfoList() {
        if (oldState.creatorList is Loading || oldState.creatorListLoadMore is Loading) {
            return
        }
        metaRepository.getCommunityRecommendUsers(
            pageSize = SEARCH_PAGE_SIZE,
            offset = oldState.offset,
        ).execute {
            when(it) {
                is Success -> {
                    val recommendUsers = it().list ?: emptyList()
                    val end = it().hasMore == false || recommendUsers.isEmpty() || offset.isNullOrEmpty()

                    val oldList = oldState.creatorList.invoke()
                    val newList = if(oldList.isNullOrEmpty()) recommendUsers else oldList + recommendUsers
                    copy(
                        creatorList = Success(newList.distinctBy { user -> user.uid }),
                        offset = it().offset,
                        creatorListLoadMore = Success(LoadMoreState(end)),
                    )
                }
                else -> oldState
            }
        }
    }

    fun changeFollow(
        uuid: String,
        follow: Boolean,
        from: String,
    ){
        EventBus.getDefault().post(
            UserFollowEvent(
                uuid,
                follow,
                from
            )
        )
        viewModelScope.launch {
            try {
                val succeed = if (follow) {
                    metaRepository.followUser(uuid)
                } else {
                    metaRepository.unfollowUser(uuid)
                }.invoke()
                if (succeed) {
                    // 这里不刷新, 通过 onUserFollowEvent 方法刷新
                    return@launch
                } else {
                    throw ApiDataException(Boolean::class)
                }
            } catch (e: Throwable) {
                setState {
                    copy(
                        toastMsg = ToastError(e)
                    )
                }
            }
            // 关注请求失败, 恢复之前的关注状态
            EventBus.getDefault().post(
                UserFollowEvent(
                    uuid,
                    !follow,
                    from
                )
            )
        }
    }

    @Subscribe
    fun onUserFollowEvent(event: UserFollowEvent) {
        updateFollowStatus(event.uuid, event.followStatus)
    }

    private fun updateFollowStatus(uuid:String, followStatus: Boolean) = withState { s ->
        if (s.creatorList !is Success) {
            return@withState
        }
        val oldCreatorList = s.creatorList.invoke()
        val newList = oldCreatorList.toMutableList()
        oldCreatorList.forEachIndexed { index, item ->
            if (item.uid == uuid) {
                newList[index] = item.copy(
                    followStatus = followStatus
                )
            }
        }

        setState {
            copy(
                creatorList = Success(newList)
            )
        }
    }

    fun getUserExtra4Share() = withState { s ->
        val uuid = s.userInfo?.uuid ?: return@withState
        metaRepository.getRecentPlayGameListV4(uuid, 1, 20).combine(
            metaRepository.qrCodeCreateFlow(
                QrCodeCreateRequest.homePage()
            )
        ) { recentGames, qrCode ->
            val url = qrCode.data?.url
            if (recentGames.code != 200 || !qrCode.succeeded || url.isNullOrBlank()) {
                null
            } else {
                ShareRawData.UserExtra(
                    recentGames.data?.validList.orEmpty(),
                    url
                )
            }
        }.map {
            check(it != null)
            it
        }.execute {
            copy(shareProfileData = it)
        }
    }

    companion object : KoinViewModelFactory<FriendSearchViewModel, FriendSearchExtraState>() {
        private const val SEARCH_PAGE_SIZE = 20
        override fun ComponentCallbacks.create(
            viewModelContext: ViewModelContext,
            state: FriendSearchExtraState
        ): FriendSearchViewModel {
            return FriendSearchViewModel(state, get(), get())
        }
    }
}