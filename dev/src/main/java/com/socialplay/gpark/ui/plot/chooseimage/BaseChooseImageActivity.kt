package com.socialplay.gpark.ui.plot.chooseimage

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.PermissionChecker
import androidx.lifecycle.lifecycleScope
import com.luck.picture.lib.basic.PictureSelectionModel
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.style.PictureSelectorStyle
import com.luck.picture.lib.style.PictureWindowAnimationStyle
import com.socialplay.gpark.R
import com.socialplay.gpark.databinding.ActivityPlotChooseImageBinding
import com.socialplay.gpark.ui.base.BaseActivity
import com.socialplay.gpark.ui.permission.Permission
import com.socialplay.gpark.ui.permission.PermissionRequest
import com.socialplay.gpark.util.FileUtil
import com.socialplay.gpark.util.StatusBarUtil
import com.socialplay.gpark.util.ToastUtil
import com.socialplay.gpark.util.glide.GlideEngine
import com.socialplay.gpark.util.glide.LubanCompressEngine
import com.socialplay.gpark.util.property.viewBinding
import kotlinx.coroutines.launch
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * Created by bo.li
 * Date: 2024/6/3
 * Desc: 拉起图片选择器，做后续操作
 */
abstract class BaseChooseImageActivity : BaseActivity() {
    override var canLandscape = true
    override val binding by viewBinding(ActivityPlotChooseImageBinding::inflate)

    private lateinit var selectImageLauncher: ActivityResultLauncher<Intent>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        StatusBarUtil.setTransparent(this)
        initArgs()
        selectImageLauncher =
            registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
                if (it.resultCode == Activity.RESULT_OK) {
                    val result = PictureSelector.obtainSelectorList(it.data)
                    onSelectResult(result)
                } else {
                    callbackError("user cancelled", 2003)
                    finish()
                }
            }
        lifecycleScope.launch {
            val result = choosePicture()
            if (!result) {
                callbackError("can not access gallery", 2005)
                finish()
            }
        }
    }

    override fun setOrientation() {
        // 解决游戏内切换竖屏，回游戏后会拉伸的问题
//        super.setOrientation()
    }

    private suspend fun choosePicture(): Boolean {
        val enabled = suspendCoroutine<Boolean> { continuation ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU &&
                PermissionChecker.checkSelfPermission(
                    this,
                    Manifest.permission.READ_MEDIA_IMAGES
                ) != PermissionChecker.PERMISSION_GRANTED
            ) {
                PermissionRequest.with(this)
                    .permissions(Permission.LOCAL_MEDIA)
                    .enableGoSettingDialog()
                    .granted {
                        continuation.resume(true)
                    }
                    .denied {
                        continuation.resume(false)
                    }
                    .branch(PermissionRequest.SCENE_ALBUM)
                    .request()
            } else {
                continuation.resume(true)
            }
        }
        val selectMimeType = getSelectMimeType()
        if (!FileUtil.havePhotoPath(this, selectMimeType)) {
            ToastUtil.showShort(getString(R.string.base_permission_photo_error))
            return false
        }
        if (!enabled) return false

        val style = PictureSelectorStyle().apply {
            windowAnimationStyle = PictureWindowAnimationStyle(
                com.luck.picture.lib.R.anim.ps_anim_up_in,
                com.luck.picture.lib.R.anim.ps_anim_down_out
            )
        }

        val builder = PictureSelector.create(this)
            .openGallery(selectMimeType)
            .setMaxSelectNum(1)
            .setImageEngine(GlideEngine)
            .setSelectorUIStyle(style)
            .setCompressEngine(LubanCompressEngine())
        assemblePictureBuilder(builder).forResult(selectImageLauncher)
        return true
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    // Set the content to appear under the system bars so that the
                    // content doesn't resize when the system bars hide and show.
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
//                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_FULLSCREEN)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                window.attributes.layoutInDisplayCutoutMode =
                    WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
            }
        }
    }

    override fun onDestroy() {
        selectImageLauncher.unregister()
        super.onDestroy()
    }

    abstract fun initArgs()

    open fun getSelectMimeType(): Int = SelectMimeType.ofImage()

    open fun assemblePictureBuilder(builder: PictureSelectionModel): PictureSelectionModel {
        return builder
    }

    abstract fun callbackError(message: String?, code: Int)
    abstract fun onSelectResult(result: ArrayList<LocalMedia>)
}