package com.socialplay.gpark.ui.account.guide

import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.socialplay.gpark.EnvConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.LoginState
import com.socialplay.gpark.data.base.ViolateCheckException
import com.socialplay.gpark.data.kv.MetaKV
import com.socialplay.gpark.data.model.LoginType
import com.socialplay.gpark.data.model.LoginTypes
import com.socialplay.gpark.data.model.LoginWay
import com.socialplay.gpark.data.model.login.LoginCompleteEvent
import com.socialplay.gpark.data.model.mgs.ViolateMessage
import com.socialplay.gpark.data.model.user.ContinueAccountInfo
import com.socialplay.gpark.data.model.user.MetaUserInfo
import com.socialplay.gpark.databinding.FragmentGuideLoginBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.pandora.PandoraToggle
import com.socialplay.gpark.function.router.LoginCompleteRouter
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.startup.core.StartupContext
import com.socialplay.gpark.ui.compliance.MetaProtocol
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.statusbar.StatusBarStateProvider
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.login.LoginSpecialWrapper
import com.socialplay.gpark.ui.login.LoginViewModel
import com.socialplay.gpark.ui.login.LoginViewModelState
import com.socialplay.gpark.ui.login.showLoginHelpDialog
import com.socialplay.gpark.ui.main.HomeImageShowAnalytics
import com.socialplay.gpark.ui.main.UpdateDialog
import com.socialplay.gpark.ui.reportBlock.ViolateRulesDialogArgs
import com.socialplay.gpark.util.QuitAppUtil
import com.socialplay.gpark.util.SpannableHelper
import com.socialplay.gpark.util.extension.backgroundTintListByColor
import com.socialplay.gpark.util.extension.collectWithLifecycleOwner
import com.socialplay.gpark.util.extension.isAtLeastCreated
import com.socialplay.gpark.util.extension.popBackStack
import com.socialplay.gpark.util.extension.registerEventBus
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.unregisterEventBus
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import org.greenrobot.eventbus.Subscribe
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

/**
 * Created by bo.li
 * Date: 2024/1/3
 * Desc:
 */
@Parcelize
data class GuideLoginFragmentArgs(
    /**
     * 登录来源 @see com.socialplay.gpark.data.model.LoginSource
     */
    val source: String,
    val continueAccountInfo: ContinueAccountInfo? = null,
) : Parcelable

class GuideLoginFragment : BaseFragment<FragmentGuideLoginBinding>(R.layout.fragment_guide_login), StatusBarStateProvider {

    override fun isStatusBarDarkText() = false
    private var isHelpDialogShowing: Boolean = false

    override var navColorRes = R.color.color_0E0922

    private val viewModel: GuideLoginViewModel by fragmentViewModel()
    private val loginViewModel: LoginViewModel by fragmentViewModel()
    private val metaKV by inject<MetaKV>()

    private val backPressedCallback by lazy { getBackPressed() }
    private val args by args<GuideLoginFragmentArgs>()

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentGuideLoginBinding? {
        return FragmentGuideLoginBinding.inflate(inflater, container, false)
    }

    private fun getBackPressed() = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            activity?.let {
                QuitAppUtil.checkClickBackPressed(it)
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Timber.d("check_report guide onViewCreated ${args}")
        binding.tvReturning.text =
            SpannableHelper.Builder().text(getString(R.string.intl_already_have_an_account))
                .text(getString(R.string.intl_log_in))
                .color(ContextCompat.getColor(requireContext(), R.color.white)).underline().build()
        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            backPressedCallback
        )
        initEventClick()
        initView()
        initData()
        HomeImageShowAnalytics.onEnterGuideLogin()
        checkIsAgreedProtocol()
        DialogShowManager.triggerGuideLoginScene(this)
        registerEventBus()
//        UpdateDialog.showFromGuideLogin(this, viewModel)
    }

    @Subscribe
    fun onEvent(event: LoginCompleteEvent) {
        try {
            popBackStack()
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    private fun initView() {
        binding.ivTitle.visible(EnvConfig.isParty())
        binding.tvTitle.visible(!EnvConfig.isParty())
        binding.ivLoginHelp.apply {
            setOnAntiViolenceClickListener {
                showLoginHelpDialog(false,null)
            }
        }

        if (EnvConfig.isParty()) {
            binding.ivAgeRestriction.visible(true)
            binding.ivAgeRestriction.setImageResource(LoginSpecialWrapper.getAgeRestrictionIconRes())
            binding.ivAgeRestriction.setOnAntiViolenceClickListener {
                LoginSpecialWrapper.showAgeRestrictionDialog(requireActivity(), this@GuideLoginFragment)
            }
        } else {
            binding.ivAgeRestriction.visible(false)
        }
    }

    private fun checkIsAgreedProtocol() {
        if (MetaProtocol.needLegal()) {
            MetaProtocol.showProtocolDialog(
                requireActivity(),
                callback = {
                    agreeProtocol()
                },
            )
        }
    }


    private fun agreeProtocol() {
        metaKV.protocol.saveProtocolAgree()
        GlobalScope.launch(Dispatchers.Main) {
            val project = StartupContext.get().getProject("onAgreeProtocol")
            project.start()
            project.await()
            kotlin.runCatching {
                if (isAtLeastCreated) {
                    viewModel.getContinueAccountIfPossible()
                }
            }
        }
    }

    private fun initData() {
        loginViewModel.init(args.source, true, LoginViewModel.LOCATION_GUIDE_LOGIN)
        viewModel.registerAsyncErrorToast(GuideLoginModelState::enterGuidePage, { error -> error !is ViolateCheckException })
        viewModel.registerAsyncErrorToast(GuideLoginModelState::enterMainPage, { error -> error !is ViolateCheckException })
        viewModel.onEach(GuideLoginModelState::continueAccount) {
            updateContinueUI(it)
        }
        viewModel.onAsync(
            GuideLoginModelState::enterGuidePage,
            deliveryMode = uniqueOnly(),
            onLoading = { _ ->
                binding.loading.showLoading()
            },
            onFail = { _, _ ->
                binding.loading.hide()
            }) { _ ->
            binding.loading.hide()
            goGuidance()
        }
        viewModel.onAsync(
            GuideLoginModelState::enterMainPage,
            deliveryMode = uniqueOnly(),
            onLoading = { _ ->
                binding.loading.showLoading()
            },
            onFail = { _, _ ->
                binding.loading.hide()
            }) { _ ->
            binding.loading.hide()
            goBack()
        }

        loginViewModel.onEach(
            LoginViewModelState::signStatus
        ) {
            when (it) {
                is LoginState.Loading -> {
                    binding.loading.showLoading()
                }

                is LoginState.Succeeded -> {
                    binding.loading.hide()
                    goBack(it.userInfo?.firstBind == true, it.userInfo)
                }

                is LoginState.Failed -> {
                    binding.loading.hide()
                    toast(it.message)
                    if (it.violateMessage != null) {
                        showViolateDialogByInfo(it.violateMessage)
                    }
                }

                is LoginState.UserCanceled -> {
                    binding.loading.hide()
                }

                else -> {}
            }
        }

        viewModel.onEach(GuideLoginModelState::violateInfo) {
            it?.toArgs()?.let { args ->
                viewModel.clearViolateOperation()
                showViolateDialogByInfo(args)
            }
        }
    }

    private fun showViolateDialogByInfo(violateMessage: ViolateMessage) {
        violateMessage.toArgs()?.let { MetaRouter.Report.violateRulesDialog(this, it) }
    }

    private fun showViolateDialogByInfo(violateMessage: ViolateRulesDialogArgs) {
        MetaRouter.Report.violateRulesDialog(this, violateMessage)
    }

    private fun goBack(isFirstBinding: Boolean = false, userInfo: MetaUserInfo? = null) {
//        if (isFirstBinding) {
//            // Gpark逻辑，保持原样
//            MetaRouter.Startup.createAvatar(this, true)
//        } else if (PandoraToggle.hasSelectMode) {
//            MetaRouter.Startup.selectMode(this)
//        } else {
        LoginCompleteRouter.router(requireActivity(), this, userInfo)
//        }
    }

    private fun goGuidance() {
        MetaRouter.Startup.createAvatar(this, true)
    }

    private fun updateContinueUI(it: ContinueAccountInfo?) {
        val hasContinueAccount = it != null && LoginTypes.checkValid(it.loginType, it.loginKey)
        visibleList(
            binding.vMaskOld,
            binding.vContinue,
            binding.tvContinue,
            binding.ivContinue,
            binding.vLineOrL,
            binding.vLineOrR,
            binding.tvOr,
            visible = hasContinueAccount
        )
        binding.vMaskNew.isVisible = !hasContinueAccount
        binding.tvContinue.text = it?.nickname
        binding.vNewPlayer.background = ContextCompat.getDrawable(
            requireContext(),
            if (hasContinueAccount) R.drawable.bg_round_48_stroke_1 else R.drawable.bg_round_48
        )
        binding.tvNewPlayer.setTextColor(
            ContextCompat.getColor(
                requireContext(),
                if (hasContinueAccount) R.color.white else R.color.neutral_color_1
            )
        )
        glide?.run {
            load(it?.portrait).placeholder(R.drawable.placeholder_corner_360)
                .into(binding.ivContinue)
        }

        if (EnvConfig.isParty()) {
            // 派对的情况下，不能注册，变成手机号登录
            binding.tvNewPlayer.text = getString(R.string.login_by_phone)
        } else {
            binding.tvNewPlayer.text = getString(R.string.intl_new_player)
        }
    }

    private fun initEventClick() {
        binding.vReturning.setOnAntiViolenceClickListener {
            // 其他登录
            Analytics.track(EventConstants.EVENT_LOGINPAGE_OTHER_ACCOUNT_BUTTON_CLICK)
            MetaRouter.Login.login(
                this,
                loginSource = args.source,
                onlyLogin = true,
                successToMain = true,
                continueAccountInfo = null,
                lastLoginType = viewModel.lastLoginType
            )
        }
        binding.vNewPlayer.setOnAntiViolenceClickListener {
            if (EnvConfig.isParty()) {
                MetaRouter.Login.loginByPhone(this, args.source)
            } else {
                // 游客登录，新手引导
                Analytics.track(EventConstants.EVENT_LOGINPAGE_NEW_ACCOUNT_BUTTON_CLICK)
                if (PandoraToggle.enableGuidanceV5) {
                    loginViewModel.auth(LoginWay.getDefaultSignWay(), requireActivity(), args.source, LoginType.CreateAccount)
                } else if (PandoraToggle.enableGuidanceV4) {
                    MetaRouter.Account.signUp(this, args.source)
                } else {
                    viewModel.visitorLogin(LoginType.CreateAccount)
                }
            }
        }
        binding.vContinue.setOnAntiViolenceClickListener {
            withState(viewModel) {
                // 去登录页
                Analytics.track(EventConstants.EVENT_LOGINPAGE_FORMAL_ACCOUNT_BUTTON_CLICK)
                it.continueAccount?.let { it1 -> continueLogin(it1) }
            }
        }
    }

    private fun continueLogin(continueInfo: ContinueAccountInfo) {
        if (!loginViewModel.continueLogin(this, continueInfo)) {
            when (continueInfo.loginType) {
                LoginSpecialWrapper.visitorWay -> {
                    viewModel.visitorLogin(LoginType.LastAccount)
                }

                else -> {
                    toast(R.string.not_currently_support)
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        unregisterEventBus()
    }

    override fun invalidate() {

    }

    override fun getPageName(): String = PageNameConstants.FRAGMENT_GUIDE_LOGIN

    override fun isFirstPage() = true
}