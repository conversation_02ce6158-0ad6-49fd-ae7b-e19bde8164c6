package com.socialplay.gpark.ui.im.conversation

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.navArgs
import com.airbnb.mvrx.fragmentViewModel
import com.bumptech.glide.Glide
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.data
import com.socialplay.gpark.data.base.message
import com.socialplay.gpark.data.base.succeeded
import com.socialplay.gpark.data.model.SimpleListData
import com.socialplay.gpark.data.model.groupchat.GroupChatDetailInfo
import com.socialplay.gpark.databinding.FragmentGroupChatProfileBinding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.dialog.ListDialog
import com.socialplay.gpark.ui.im.groupchat.GroupChatConfig
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.util.NetUtil
import com.socialplay.gpark.util.extension.enableWithAlpha
import com.socialplay.gpark.util.extension.popBackStack
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.ifNullOrEmpty

class GroupChatProfileFragment :
    BaseFragment<FragmentGroupChatProfileBinding>(R.layout.fragment_group_chat_profile) {
    private val vm: GroupChatProfileViewModel by fragmentViewModel()
    private val editViewModel: EditGroupChatInfoViewModel by fragmentViewModel()
    private val args by navArgs<GroupChatProfileFragmentArgs>()

    companion object {
        const val REQUEST_KEY_GROUP_EDIT = "request_key_group_chat_profile"
        const val KEY_GROUP_EDIT_RESULT = "key_group_chat_profile_result"
    }

    private fun popWithResult() {
        setFragmentResult(REQUEST_KEY_GROUP_EDIT, Bundle().apply {
            putParcelable(KEY_GROUP_EDIT_RESULT, vm.groupDetail?.simplifiedMembers())
        })
        navigateUp()
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentGroupChatProfileBinding? {
        return FragmentGroupChatProfileBinding.inflate(inflater, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        vm.updateGroupId(args.groupId)
        binding.ivTitleBack.setOnAntiViolenceClickListener {
            popWithResult()
        }
        binding.layoutMembers.setOnAntiViolenceClickListener {
            val groupDetail = vm.groupDetail ?: return@setOnAntiViolenceClickListener
            MetaRouter.IM.goGroupMembersFragment(
                this@GroupChatProfileFragment,
                groupDetail
            ) { groupDetailEditResult ->
                if (groupDetailEditResult != null) {
                    vm.updateGroupChatDetail(groupDetailEditResult)
                }
            }
        }
        binding.layoutManagementInvitation.setOnAntiViolenceClickListener {
            val groupDetail = vm.groupDetail ?: return@setOnAntiViolenceClickListener
            val groupId = groupDetail.id ?: return@setOnAntiViolenceClickListener
            val currentJoinType = groupDetail.joinType ?: -1
            val currentInviteType = groupDetail.inviteType ?: -1
            if (vm.isGroupManager() == true) {
                MetaRouter.IM.goGroupJoinInviteRulesEditDialog(
                    this@GroupChatProfileFragment,
                    groupId,
                    currentJoinType,
                    currentInviteType
                ) { newGroupDetail ->
                    if (newGroupDetail != null) {
                        vm.updateGroupChatDetail(newGroupDetail)
                    }
                }
            }
        }
        binding.switchNotifications.setOnAntiViolenceClickListener {
            val isChecked = binding.switchNotifications.isChecked
            vm.editGroupChatNotification(
                args.groupId,
                isChecked
            )
        }
        if (GroupChatConfig.enableGroupChatAgreement) {
            binding.layoutAgreement.visible(true)
            binding.layoutAgreement.setOnAntiViolenceClickListener {
                MetaRouter.Web.navigate(
                    context,
                    this,
                    getString(R.string.setting_item_title_group_chat_agreement),
                    GroupChatConfig.groupChatAgreementUrl,
                    showTitle = true,
                    showStatusBar = true
                )
            }
        } else {
            binding.layoutAgreement.visible(false)
        }
        binding.layoutReport.setOnAntiViolenceClickListener {
            // 举报群聊
            MetaRouter.Report.reportChatGroup(
                this,
                args.groupId,
            ) { submit ->
                if (submit) {
                    ReportReasonDialog.showReportSuccessDialog(
                        this,
                        ReportSuccessDialogAnalyticsParams.GroupChat(args.groupId)
                    )
                }
            }
        }
        binding.layoutLeaveGroup.setOnAntiViolenceClickListener {
            val isOwner = vm.isGroupOwner() ?: return@setOnAntiViolenceClickListener
            showLeaveGroupDialog(isOwner)
        }

        vm.onAsync(
            GroupChatProfileModelState::groupDetail,
            onFail = { _, _ ->
                binding.loadingView.showError()
            },
            onLoading = {
                binding.loadingView.showLoading()
            },
            onSuccess = { groupDetail ->
                binding.loadingView.hide()
                Glide.with(this)
                    .load(groupDetail.icon)
                    .placeholder(R.drawable.icon_item_group_chat_avatar)
                    .into(binding.ivGroupAvatar)
                binding.tvGroupName.text = groupDetail.name ?: ""
                binding.ivGroupNameEdit.visible(vm.isGroupManager() == true)
                binding.tvGroupId.setTextWithArgs(
                    R.string.group_chat_profile_page_group_id,
                    groupDetail.id ?: ""
                )
                binding.tvMembersCount.text = groupDetail.memberCount.toString()
                binding.tvDescContent.text = groupDetail.describe.ifNullOrEmpty { getString(R.string.default_group_desc) }

                val joinText = when (groupDetail.joinType) {
                    GroupChatDetailInfo.JOIN_TYPE_EVERYONE -> {
                        getString(R.string.group_chat_join_type_everyone)
                    }

                    GroupChatDetailInfo.JOIN_TYPE_APPLY -> {
                        getString(R.string.group_chat_join_type_apply)
                    }

                    else -> {
                        ""
                    }
                }
                val inviteText =
                    when (groupDetail.inviteType) {
                        GroupChatDetailInfo.INVITE_TYPE_EVERYONE -> {
                            getString(R.string.group_chat_invite_type_everyone)
                        }

                        GroupChatDetailInfo.INVITE_TYPE_MANAGER -> {
                            getString(R.string.group_chat_invite_type_manager)
                        }

                        else -> {
                            ""
                        }
                    }
                binding.tvManagementInvitationContent.text = "$joinText, $inviteText"
                binding.ivManagementInvitationArrow.visible(vm.isGroupManager() == true)
                binding.switchNotifications.isChecked = groupDetail.notifications ?: false

                onAuditChanged(groupDetail.auditing ?: true)
            },
        )

        vm.onAsync(
            GroupChatProfileModelState::leaveGroupResult,
            uniqueOnly(),
            onFail = { _, _ ->
                if (!NetUtil.isNetworkAvailable()) {
                    toast(R.string.net_unavailable)
                }
            },
            onLoading = {
            },
            onSuccess = { leaveResult ->
                if (leaveResult.succeeded && leaveResult.data == true) {
                    toast(R.string.toast_leave_group_chat_success)
                    popBackStack(R.id.group_chat_fragment, inclusive = true)
                } else {
                    if (!NetUtil.isNetworkAvailable()) {
                        toast(R.string.net_unavailable)
                    } else {
                        toast(leaveResult.message)
                    }
                }
            },
        )
        vm.onAsync(
            GroupChatProfileModelState::disbandGroupResult,
            onFail = { _, _ ->
                if (!NetUtil.isNetworkAvailable()) {
                    toast(R.string.net_unavailable)
                }
            },
            onLoading = {
            },
            onSuccess = { disbandResult ->
                if (disbandResult.succeeded && disbandResult.data == true) {
                    toast(R.string.toast_disband_group_chat_success)
                    popBackStack(R.id.group_chat_fragment, inclusive = true)
                } else {
                    if (!NetUtil.isNetworkAvailable()) {
                        toast(R.string.net_unavailable)
                    } else {
                        toast(disbandResult.message)
                    }
                }
            },
        )
        vm.onAsync(
            GroupChatProfileModelState::editGroupNotificationResult,
            onFail = { _, _ ->
                if (!NetUtil.isNetworkAvailable()) {
                    toast(R.string.net_unavailable)
                }
            },
            onLoading = {
            },
            onSuccess = { editResult ->
                if (!(editResult.succeeded && editResult.data == true)) {
                    if (!NetUtil.isNetworkAvailable()) {
                        toast(R.string.net_unavailable)
                    } else {
                        toast(editResult.message)
                    }
                }
            },
        )

        vm.onEach(
            GroupChatProfileModelState::isAuditing,
        ) {
            // 目前暂时令群名称和群描述有一个处于审核状态时，就不允许更新另一个，直至二者都没有处于审核状态
            onAuditChanged(it)
        }
        vm.getGroupChatDetailInfo(args.groupId)
    }

    override fun onResume() {
        super.onResume()
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            popWithResult()
        }
    }

    private fun showLeaveGroupDialog(isOwner: Boolean) {
        val confirmText = if (isOwner) {
            getString(R.string.dialog_button_disband_group)
        } else {
            getString(R.string.dialog_button_leave_group)
        }
        val content = if (isOwner) {
            getString(R.string.dialog_content_disband_group)
        } else {
            getString(R.string.dialog_content_leave_group)
        }
        ListDialog().list(
            mutableListOf(
                SimpleListData(
                    confirmText,
                    R.drawable.selector_button_error,
                    R.color.white
                ),
                SimpleListData(getString(R.string.dialog_cancel))
            )
        ).content(content).clickCallback {
            if (it?.text.equals(getString(R.string.dialog_cancel))) {
                // 点击取消按钮
            } else if (it?.text.equals(getString(R.string.dialog_button_leave_group))) {
                Analytics.track(
                    EventConstants.GROUP_LEAVE_GROUP_FINISH,
                    "group_id" to args.groupId.toString()
                )
                // 离开群聊
                vm.leaveGroupChat(args.groupId)
            } else if (it?.text.equals(getString(R.string.dialog_button_disband_group))) {
                Analytics.track(
                    EventConstants.GROUP_LEAVE_GROUP_FINISH,
                    "group_id" to args.groupId.toString()
                )
                // 解散群聊
                vm.disbandGroupChat(args.groupId)
            }
        }.show(childFragmentManager, "LeaveGroupDialog")
    }

    private fun onAuditChanged(isAuditing: Boolean) {
        binding.ivDescDetail.enableWithAlpha(!isAuditing, 0.5f)
        binding.ivGroupNameEdit.enableWithAlpha(!isAuditing, 0.2f)
        if(!isAuditing) {
            binding.layoutDesc.setOnAntiViolenceClickListener {
                val isGroupManager = vm.isGroupManager() == true
                val groupDetail = vm.groupDetail ?: return@setOnAntiViolenceClickListener
                val groupId = groupDetail.id ?: return@setOnAntiViolenceClickListener
                val currentDesc = groupDetail.describe ?: ""
                MetaRouter.IM.goGroupDescEditFragment(
                    this@GroupChatProfileFragment,
                    groupId,
                    currentDesc,
                    isGroupManager
                ) { isAuditing ->
                    vm.updateAuditing(isAuditing)
                }
            }

            binding.viewGroupNameClick.setOnAntiViolenceClickListener {
                if (vm.isGroupManager() == true) {
                    val groupDetail = vm.groupDetail ?: return@setOnAntiViolenceClickListener
                    val groupId = groupDetail.id ?: return@setOnAntiViolenceClickListener
                    val groupName = groupDetail.name ?: ""
                    MetaRouter.IM.goGroupNameEditDialog(
                        this@GroupChatProfileFragment,
                        groupId,
                        groupName
                    ) { isAuditing ->
                        vm.updateAuditing(isAuditing)
                    }
                }
            }
        } else {
            binding.layoutDesc.setOnAntiViolenceClickListener {
                toast(R.string.group_info_urgent_review)
            }
            binding.viewGroupNameClick.setOnAntiViolenceClickListener {
                toast(R.string.group_info_urgent_review)
            }
        }
    }

    override fun invalidate() {

    }

    override fun getPageName() = PageNameConstants.FRAGMENT_GROUP_PROFILE
}