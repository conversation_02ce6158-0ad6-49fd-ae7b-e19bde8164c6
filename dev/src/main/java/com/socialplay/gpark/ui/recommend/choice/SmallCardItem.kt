package com.socialplay.gpark.ui.recommend.choice

import android.graphics.drawable.Drawable
import android.view.View
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.choice.ChoiceGameInfo
import com.socialplay.gpark.databinding.AdapterChoiceCardItemSmallBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.core.views.setHorizontalScrollPadding
import com.socialplay.gpark.util.PaletteColorUtil
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.unsetOnClick
import com.socialplay.gpark.ui.view.BlurEffectManager
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/01
 *     desc   :
 * </pre>
 */
fun MetaModelCollector.choiceSmallCard(
    card: ChoiceCardInfo, cardPosition: Int, spanSize: Int, listener: IChoiceListener
) {
    val games = card.gameListNoNull
    if (games.isNullOrEmpty()) return
    val addTitleTimestamp = System.currentTimeMillis()
    add(ChoiceTitleMoreItem(card, spanSize, listener, needMore = false).id("ChoiceSmallCardTitle-choiceSmallCard-$cardPosition-${card.cardName.hashCode()}").spanSizeOverride { _, _, _ -> spanSize })
    Timber.tag("Performance").d("choiceSmallCard add title time: ${System.currentTimeMillis() - addTitleTimestamp}")
    val addListTimestamp = System.currentTimeMillis()
    carouselNoSnapWrapBuilder {
        id("ChoiceSmallCardList-choiceSmallCard-$cardPosition-${card.cardName.hashCode()}")
        // 使用专门的横向滑动padding设置
        setHorizontalScrollPadding()
        hasFixedSize(true)
        initialPrefetchItemCount(1)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        spanSizeOverride { _, _, _ ->
            spanSize
        }
        games.forEachIndexed { position, game ->
            add(
                ChoiceSmallCardItem(
                    game, position, card, cardPosition, spanSize, listener
                ).id("ChoiceSmallCardGame-${card.cardId}-$cardPosition-$position-${game.code}").spanSizeOverride { _, _, _ ->
                    1
                }
            )
        }
        Timber.tag("Performance").d("choiceSmallCard add list time: ${System.currentTimeMillis() - addListTimestamp}")
    }
}

data class ChoiceSmallCardItem(
    val item: ChoiceGameInfo, val position: Int, val card: ChoiceCardInfo, val cardPosition: Int, val spanSize: Int, val listener: IChoiceListener
) : ViewBindingItemModel<AdapterChoiceCardItemSmallBinding>(
    R.layout.adapter_choice_card_item_small, AdapterChoiceCardItemSmallBinding::bind
) {

    override fun AdapterChoiceCardItemSmallBinding.onBind() {
        metaLikeView.setLikeText(UnitUtil.formatKMCount(item.localLikeCount ?: item.likeCount ?: 0))

        listener.getGlideOrNull()?.run {
            load(item.iconUrl)
                .placeholder(PaletteColorUtil.getPlaceholderDrawable(root.context, item.gameColor))
                .error(PaletteColorUtil.getFailedPlaceholderDrawable(root.context)) // 添加错误占位图
                .into(ivGameIcon)
        }
        tvGameTitle.text = item.displayName?.trim() ?: ""
        // 将16进制的颜色设置给ivNameBg
        ivNameBg.setImageDrawable(PaletteColorUtil.getAssetPlaceholderDrawable(item.gameColor, item.displayName.orEmpty()))

        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
    }

    override fun AdapterChoiceCardItemSmallBinding.onUnbind() {
        root.unsetOnClick()
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}