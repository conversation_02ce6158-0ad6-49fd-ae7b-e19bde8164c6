package com.socialplay.gpark.ui.base

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.os.Parcel
import android.view.LayoutInflater
import androidx.annotation.CallSuper
import androidx.appcompat.app.AppCompatActivity
import androidx.viewbinding.ViewBinding
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.function.locale.MetaLanguages
import com.socialplay.gpark.ui.locale.LanguageSettingFragment
import com.socialplay.gpark.util.extension.LifecycleCallback
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.property.ViewBindingLifecycleOwner
import com.socialplay.gpark.util.property.ViewBindingLifecycleOwnerProvider
import timber.log.Timber

/**
 * Created by yaqi.liu on 2021/5/7
 */
abstract class BaseActivity : AppCompatActivity(), ViewBindingLifecycleOwnerProvider {

    protected abstract val binding: ViewBinding
    private var viewBindingLifecycleOwner: ViewBindingLifecycleOwner? = null
    private val onNewIntentCallback: LifecycleCallback<OnNewIntentIntentCallback> by lazy { LifecycleCallback() }
    protected var needSetContent = true
    protected open var canLandscape = false


    @CallSuper
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setOrientation()
        viewBindingLifecycleOwner = ViewBindingLifecycleOwner()
        if (needSetContent) setContentView(binding.root)
    }


    /**
     * 设置屏幕方向
     * 实现策略：
     * 1. AndroidManifest.xml中设置默认为portrait（手机强制竖屏）
     * 2. 代码中为平板设备动态设置为unspecified（跟随用户设备朝向）
     *
     * 这样确保：
     * - 手机设备：始终强制竖屏
     * - 平板设备：可以自由旋转，跟随用户设备朝向
     */
    protected open fun setOrientation() {
        requestedOrientation = if (isPad && canLandscape) {
            // 平板设备：允许自由旋转，跟随用户设备朝向
            ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        } else {
            // 手机设备：强制竖屏（与AndroidManifest.xml中的设置保持一致）
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        // 当前 Activity 中的fragment太多时, 会让outState的数据过大, 从而触发 java.lang.RuntimeException: android.os.TransactionTooLargeException: data parcel size 553692 bytes
        // ViewPager 配合 FragmentStateAdapter 嵌套多个 Fragment 时, 也会使 outState 有比较大的增长
        // 还应当避免 fragment 循环跳转, 每条一层 fragment, 都会使 outState 增大
        // 可以参考 MetaRouter.Profile.other() 的方法, 通过 navOptions 来处理循环跳转, 复用返回栈中已有的 fragment
        if (BuildConfig.DEBUG) {
            Timber.tag("BaseActivity")
                .d("onSaveInstanceState-bundleSize=${getBundleSize(outState)}")
        }
    }

    /**
     * 计算 Bundle 内存占用大小
     */
    private fun getBundleSize(bundle: Bundle?): Int {
        val parcel = Parcel.obtain()
        parcel.writeBundle(bundle)
        val size = parcel.dataSize()
        parcel.recycle()
        return size
    }

    /**
     * 目前剔除广告AdActivity
     */
    open fun isNeedAnalytics(): Boolean = true

    @CallSuper
    override fun onNewIntent(intent: Intent?) {
        onNewIntentCallback.dispatch {
            invoke(intent)
        }
        super.onNewIntent(intent)
    }

    fun observerOnNewIntentCallback(callback: OnNewIntentIntentCallback) {
        onNewIntentCallback.observe(this, callback)
    }

    override fun onDestroy() {
        super.onDestroy()
        viewBindingLifecycleOwner?.onDestroyView()
        viewBindingLifecycleOwner = null
    }

    override fun viewBindingLayoutInflater(): LayoutInflater {
        return layoutInflater
    }

    override fun viewBindingLifecycleOwner(): ViewBindingLifecycleOwner {
        return viewBindingLifecycleOwner ?: error("view not create or destroy")
    }

    override fun attachBaseContext(newBase: Context?) {
        Timber.tag(LanguageSettingFragment.TAG).d("attachBaseContext $newBase ${this.javaClass}")
        super.attachBaseContext(MetaLanguages.attachContext(newBase))
    }
}

typealias OnNewIntentIntentCallback = (intent: Intent?) -> Unit