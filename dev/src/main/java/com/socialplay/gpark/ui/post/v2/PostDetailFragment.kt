package com.socialplay.gpark.ui.post.v2

import android.animation.ValueAnimator
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.os.bundleOf
import androidx.core.view.ViewCompat
import androidx.fragment.app.setFragmentResult
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.args
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.withState
import com.bumptech.glide.RequestManager
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.SimpleExoPlayer
import com.google.android.material.appbar.CustomAppBarBehavior
import com.meta.box.biz.friend.internal.model.FriendStatus
import com.meta.box.biz.friend.internal.model.toLocalStatus
import com.meta.box.biz.friend.model.LabelInfo
import com.socialplay.gpark.R
import com.socialplay.gpark.data.base.ApiResultCodeException
import com.socialplay.gpark.data.interactor.FriendInteractor
import com.socialplay.gpark.data.model.post.AddPostCommentReplyTarget
import com.socialplay.gpark.data.model.post.MomentLocalTSStartUp
import com.socialplay.gpark.data.model.post.OpinionRequestBody
import com.socialplay.gpark.data.model.post.PostCardInfo
import com.socialplay.gpark.data.model.post.PostComment
import com.socialplay.gpark.data.model.post.PostCommentListRequestBody
import com.socialplay.gpark.data.model.post.PostMediaResource
import com.socialplay.gpark.data.model.post.PostMomentCard
import com.socialplay.gpark.data.model.post.PostReply
import com.socialplay.gpark.data.model.post.PostStyleCard
import com.socialplay.gpark.data.model.post.PostTag
import com.socialplay.gpark.data.model.post.PostUgcDesignCard
import com.socialplay.gpark.data.model.reportBlock.ReportType
import com.socialplay.gpark.data.model.share.ShareFeature
import com.socialplay.gpark.data.model.share.ShareRawData
import com.socialplay.gpark.data.model.video.PlayerVideoResource
import com.socialplay.gpark.data.model.video.VideoFeedShowInfo
import com.socialplay.gpark.databinding.FragmentPostDetailV2Binding
import com.socialplay.gpark.function.analytics.Analytics
import com.socialplay.gpark.function.analytics.EventConstants
import com.socialplay.gpark.function.analytics.EventParamConstants
import com.socialplay.gpark.function.analytics.PageNameConstants
import com.socialplay.gpark.function.analytics.resid.CategoryId
import com.socialplay.gpark.function.analytics.resid.ResIdBean
import com.socialplay.gpark.function.community.FeedImageVideoUtil
import com.socialplay.gpark.function.editor.RoleGameTryOn
import com.socialplay.gpark.function.exoplayer.SharedVideoPlayerControllerInteractor
import com.socialplay.gpark.function.exoplayer.VideoPlayerCacheInteractor
import com.socialplay.gpark.function.mw.TSLaunchWrapper
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchOptionAppendTsStartUp
import com.socialplay.gpark.function.mw.launch.bean.TSLaunchParams
import com.socialplay.gpark.function.post.CommunityUtil
import com.socialplay.gpark.function.router.MetaRouter
import com.socialplay.gpark.function.share.IGlobalShareCallback
import com.socialplay.gpark.function.share.platform.ShareHelper
import com.socialplay.gpark.ui.account.AccPwdV7Dialog
import com.socialplay.gpark.ui.account.AccPwdV7DialogArgs
import com.socialplay.gpark.ui.core.BaseFragment
import com.socialplay.gpark.ui.core.views.empty
import com.socialplay.gpark.ui.core.views.loadMoreFooter
import com.socialplay.gpark.ui.core.views.simpleController
import com.socialplay.gpark.ui.dialog.ConfirmDialog
import com.socialplay.gpark.ui.dialog.DialogShowManager
import com.socialplay.gpark.ui.editor.detail.comment.CommentMorePopup
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentExpandItem
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentItem
import com.socialplay.gpark.ui.editor.detail.comment.ugcCommentLoading
import com.socialplay.gpark.ui.editor.detail.comment.ugcReplyItem
import com.socialplay.gpark.ui.editor.tab.FullScreenEditorActivityArgs
import com.socialplay.gpark.ui.imgpre.v2.OpenPreviewBuilder
import com.socialplay.gpark.ui.notice.NotificationPermissionManager
import com.socialplay.gpark.ui.post.PostHelper
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialog
import com.socialplay.gpark.ui.post.comment.ArticleCommentInputDialogParams
import com.socialplay.gpark.ui.post.feed.CommunityFeedItemSingleImage
import com.socialplay.gpark.ui.reportBlock.ReportReasonDialog
import com.socialplay.gpark.ui.reportBlock.ReportSuccessDialogAnalyticsParams
import com.socialplay.gpark.ui.share.GlobalShareDialog
import com.socialplay.gpark.ui.view.FollowView
import com.socialplay.gpark.ui.view.InterceptClickEventLinkMovementMethod
import com.socialplay.gpark.ui.view.UserLabelView
import com.socialplay.gpark.ui.view.video.FeedVideoHelper
import com.socialplay.gpark.util.DateUtil.formatCreateDate
import com.socialplay.gpark.util.ScreenUtil
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.extension.addListener
import com.socialplay.gpark.util.extension.addModelBuildListener
import com.socialplay.gpark.util.extension.addOnOffsetChangedListener
import com.socialplay.gpark.util.extension.addUpdateListener
import com.socialplay.gpark.util.extension.cancelAnimationIfAnimating
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.getColorByRes
import com.socialplay.gpark.util.extension.getDrawableByRes
import com.socialplay.gpark.util.extension.getLocationInWindow
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.invisible
import com.socialplay.gpark.util.extension.isPad
import com.socialplay.gpark.util.extension.registerAdapterDataObserver
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setTextColorByRes
import com.socialplay.gpark.util.extension.setTextWithArgs
import com.socialplay.gpark.util.extension.sp
import com.socialplay.gpark.util.extension.toast
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList
import com.socialplay.gpark.util.extension.visiblePercent
import kotlinx.parcelize.Parcelize
import org.koin.android.ext.android.inject
import timber.log.Timber

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2023/09/26
 *     desc   :
 * </pre>
 */
@Parcelize
data class PostDetailFragmentArgs(
    val postId: String,
    val targetCommentId: String?,
    val targetReplyId: String?,
    val videoProgress: Long,
    val source: String?,
    val fromTagDetailInfo: PostTag?
) : Parcelable

class PostDetailFragment :
    BaseFragment<FragmentPostDetailV2Binding>(R.layout.fragment_post_detail_v2), Player.Listener,
    IGlobalShareCallback {

    companion object {
        const val KEY_ACTION_POST = "key_action_post"
        const val KEY_PARAM_POST_ID = "key_post_id"
        const val KEY_PARAM_IS_DELETE = "key_is_delete" // 是否已删除
        const val KEY_PARAM_OPINION = "key_opinion" // 观点
        const val KEY_PARAM_LIKE_COUNT = "key_like_count" // 点赞数
        const val KEY_PARAM_COMMENT_COUNT = "key_comment_count" // 评论数
        const val KEY_PARAM_SHARE_COUNT = "key_share_count" // 分享数

        const val FEAT_DELETE = 1
        const val FEAT_REPORT = 2
        const val FEAT_EDIT = 3
    }

    private val vm: PostDetailViewModel by fragmentViewModel()
    private val args: PostDetailFragmentArgs by args()

    private val videoCacheInteractor: VideoPlayerCacheInteractor by inject()

    private var commentAuthorHeight = 0

    private var blueColor = 0

    private val dp10 = 10.dp
    private val dp14 = 14.dp

    private val commentMorePopup: CommentMorePopup by lazy { CommentMorePopup(layoutInflater) }

    private val mediaController by lazy { buildMediaController() }
    private val gameController by lazy { buildGameController() }

    private val commentController by lazy { buildCommentController() }
    private val tsLaunchWrapper by lazy { TSLaunchWrapper(this) }

    private val friendInteractor by inject<FriendInteractor>()

    private var isDelete = false

    private var videoProgress = -1L
    private var autoPlayed = false
    private var videoTime = 0L

    override val destId: Int = R.id.post_detail

    private var scrollToTop = false
    private var needLocate = false
    private var locatePosition = 0
    private var shouldPlayLikeAnim = false

    private val itemListener = object : IPostDetailListener {
        override fun openMedia(media: PostMediaResource, position: Int) {
            if (media.isImage) {
                val imageUrls = withState(vm) { it.detail.invoke()?.imageUrls }
                if (!imageUrls.isNullOrEmpty()) {
                    Analytics.track(EventConstants.EVENT_POST_VIEW_PICTURE)
                    OpenPreviewBuilder(this@PostDetailFragment)
                        .setRecyclerView(binding.rvImg) { _, _ ->
                            if (imageUrls.size == 1) {
                                R.id.ivImage
                            } else {
                                R.id.iv_fixed
                            }
                        }
                        .setImageUrls(imageUrls.toList())
                        .setClickPosition(position)
                        .setShowSaveBtn(true)
                        .show()
                }
            } else if (media.isVideo) {
                val playerController by inject<SharedVideoPlayerControllerInteractor>()
                val toRestoreUri = MediaItem.fromUri(media.resourceValue)
                playerController.setMediaItem(toRestoreUri)
                playerController.changeMuteState(false)
                MetaRouter.Video.fullScreenPlayer(
                    this@PostDetailFragment,
                    "post_detail",
                    bundleOf("postId" to args.postId)
                )
            }
        }

        override fun openGame(game: PostCardInfo) {
            CommunityUtil.goGameDetail(
                this@PostDetailFragment,
                game,
                CategoryId.COMMUNITY_GAME_CARD
            )
        }

        override fun goMoment(moment: PostMomentCard) {
            tsLaunchWrapper.listenLaunchFailed { _, throwable ->
                toast(throwable.message)
            }
            tsLaunchWrapper.callLaunch {
                val info = createTSGameDetailInfo(
                    moment.gameId ?: "",
                    "",
                    moment.templateName ?: ""
                )
                val resIdBean = ResIdBean.newInstance().setCategoryID(CategoryId.POST_DETAIL_PLOT_CARD)
                    .setGameId(moment.gameId).setTypeID("${moment.templateId}")
                val tsLaunchParams = TSLaunchParams(
                    info,
                    resIdBean,
                    option = TSLaunchOptionAppendTsStartUp()
                )
                tsLaunchParams.tsStartUp = moment.extendParams ?: ""
                tsLaunchParams.localTsStartUp =
                    MomentLocalTSStartUp("3", "${moment.templateId}").toMap()
                tsLaunchParams.setGameUniqueKey("${moment.gameId}_${moment.templateId}")
                launchPlot(requireContext(), tsLaunchParams)
            }
        }

        override fun goOutfit(outfit: PostStyleCard) {
            MetaRouter.MobileEditor.fullScreenRole(
                requireContext(),
                FullScreenEditorActivityArgs(
                    categoryId = CategoryId.OUTFIT_SHARE_POST_DETAIL,
                    tryOn = RoleGameTryOn.create(
                        tryOnUserId = vm.detail?.uid.orEmpty(),
                        from = RoleGameTryOn.FROM_COMMUNITY_POST,
                        roleId = outfit.roleId,
                        allowTryOn = true
                    )
                )
            )
        }

        override fun goUgcDesign(ugcDesign: PostUgcDesignCard) {
            MetaRouter.UgcDesign.detail(
                this@PostDetailFragment,
                ugcDesign.itemId,
                CategoryId.UGC_DESIGN_POST_DETAIL
            )
        }

        override fun goUserPage(uid: String?) {
            if (!uid.isNullOrBlank()) {
                MetaRouter.Profile.other(this@PostDetailFragment, uid, "postDetail")
            }
        }

        override fun operateComment(
            view: View,
            comment: PostComment,
            commentPosition: Int,
            showRedDot: Boolean
        ) {
            vm.setMoreId(commentId = comment.commentId)
            handleOperationHelper(
                view,
                comment = comment,
                showRedDot = showRedDot,
                commentPosition = commentPosition
            )
        }

        override fun likeComment(comment: PostComment, commentPosition: Int) {
            val isLike = !comment.isLike
            Analytics.track(
                EventConstants.EVENT_POST_REPLY_LIKE_CLICK,
                "type" to (if (isLike) 1 else 2),
                "location" to 1
            )
            vm.likeComment(comment, commentPosition)
            if (isLike) {
                DialogShowManager.triggerLike(this@PostDetailFragment)
            }
        }

        override fun reply2Comment(comment: PostComment, commentPosition: Int) {
            showReplyInput(AddPostCommentReplyTarget(comment, commentPosition))
        }

        override fun operateReply(
            view: View,
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int,
            isAuthorReply: Boolean
        ) {
            vm.setMoreId(replyId = reply.replyId)
            handleOperationHelper(
                view,
                reply = reply,
                commentPosition = commentPosition,
                replyPosition = replyPosition,
                isAuthorReply = isAuthorReply
            )
        }

        override fun likeReply(
            reply: PostReply,
            replyPosition: Int,
            commentPosition: Int,
            isAuthorReply: Boolean
        ) {
            val isLike = !reply.isLike
            Analytics.track(
                EventConstants.EVENT_POST_REPLY_LIKE_CLICK,
                "type" to (if (isLike) 1 else 2),
                "location" to 1
            )
            vm.likeReply(reply, replyPosition, commentPosition, isAuthorReply)
        }

        override fun reply2Reply(reply: PostReply, commentPosition: Int) {
            showReplyInput(AddPostCommentReplyTarget(reply, reply.commentId, commentPosition))
        }

        override fun loadMoreReply(
            comment: PostComment,
            commentPosition: Int
        ) {
            if (comment.isCollapse) {
                Analytics.track(
                    EventConstants.EVENT_POST_SHOW_REPLIES_CLICK, mapOf(
                        "location" to 1,
                    )
                )
            }
            vm.loadMoreReplies(comment, commentPosition)
        }

        override fun collapseReply(
            comment: PostComment,
            commentPosition: Int
        ) {
            vm.collapseReply(comment, commentPosition, true)
        }

        override fun goCommentListPage() {}
        override fun previewImage(
            mediaList: List<String>?,
            imageViews: List<ImageView>,
            imagePosition: Int
        ) {
            OpenPreviewBuilder(this@PostDetailFragment)
                .setImageUrls(mediaList ?: emptyList())
                .setClickViews(imageViews)
                .setClickPosition(imagePosition)
                .show()
        }

        override fun showComment(
            comment: PostComment,
            commentPosition: Int
        ) {
        }

        override fun clickLabel(data: Pair<Int, LabelInfo?>) {
            UserLabelView.showDescDialog(this@PostDetailFragment, data)
        }

        override fun isMe(uuid: String?): Boolean {
            return vm.isMe(uuid)
        }

        override fun isCreator(uid: String?): Boolean {
            return vm.isCreator(uid)
        }

        override fun iAmCreator(): Boolean {
            return vm.iAmCreator
        }

        override fun onAssetsCardVisible(card: Any) {
            if (card is PostStyleCard) {
                Analytics.track(
                    EventConstants.POST_LIST_OUTFIT_SHOW,
                    "resid" to vm.detail?.postId.orEmpty(),
                    "shareid" to card.roleId,
                    "tag" to vm.detail?.tagList?.map { tag -> tag.tagId }?.joinToString(",")
                        .orEmpty(),
                    "source" to "1"
                )
            }
        }

        override fun getGlideOrNull(): RequestManager? {
            return glide
        }
    }
    private fun handleOperationHelper(
        view: View,
        comment: PostComment? = null,
        showRedDot: Boolean = false,
        reply: PostReply? = null,
        commentPosition: Int = 0,
        replyPosition: Int = 0,
        isAuthorReply: Boolean = false
    ){
        val isMe = vm.isMe(comment?.uid ?: reply?.uid)
        val isCreator = vm.isCreator(vm.myUuid)
        commentMorePopup.operateComment(
            fragment = this@PostDetailFragment,
            rootLayout = binding.root,
            view = view,
            comment = comment,
            reply = reply,
            showRedDot = showRedDot,
            isMe = isMe,
            isCreator = isCreator,
            onCopyClick = {
                val reviewId: String = comment?.commentId ?: reply?.replyId.orEmpty()
                val reviewType: Long = if (comment != null) 1L else 2L
                Analytics.track(
                    EventConstants.LIBRARY_REVIEW_COPY_CLICK,
                    "metrialidid" to args.postId,
                    "reviewid" to reviewId,
                    "reviewtype" to reviewType
                )
            },
            onPinClick = {
                if (comment != null) {
                    vm.pinComment(comment, commentPosition, showRedDot)
                }
            },
            onUnpinClick = {
                if (comment != null) {
                    vm.pinComment(comment, commentPosition, showRedDot)
                }
            },
            onReportClick = {
                if (comment != null) {
                    goReport(comment.commentId, ReportType.UgcClothingComment)
                } else if (reply != null) {
                    goReport(reply.replyId, ReportType.UgcClothingReply)
                }
            },
            onDeleteCancel = {},
            onDeleteConfirm={
                if (comment != null) {
                    vm.deleteComment(comment, commentPosition)
                } else if (reply != null) {
                    vm.deleteReply(
                        reply, replyPosition, commentPosition, isAuthorReply
                    )
                }
            }
        )
    }

    override fun onCreateViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): FragmentPostDetailV2Binding? {
        return FragmentPostDetailV2Binding.inflate(inflater, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm.apiMonitor(
            this,
            PostDetailState::detail
        )
        mediaController.onRestoreInstanceState(savedInstanceState)
        gameController.onRestoreInstanceState(savedInstanceState)
        commentController.onRestoreInstanceState(savedInstanceState)
        commentController.setFilterDuplicates(true)
    }

    override fun onResume() {
        super.onResume()
        playVideo()
    }

    override fun onPause() {
        pauseVideo()
        super.onPause()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mediaController.onSaveInstanceState(outState)
        gameController.onSaveInstanceState(outState)
        commentController.onSaveInstanceState(outState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        commentAuthorHeight = maxOf(36.dp, 39.sp)
        blueColor = view.getColorByRes(R.color.color_4AB4FF)

        initPopup()

        binding.abl.addOnOffsetChangedListener(viewLifecycleOwner) { _, _ ->
            if (!autoPlayed) {
                playVideo()
            }
        }
        binding.tbl.setOnBackAntiViolenceClickedListener {
            navigateUp()
        }
        binding.lv.setRetry {
            vm.initData()
        }
        binding.ivMore.setOnAntiViolenceClickListener {
            showMoreDialog(true)
        }
        binding.vAuthorClick.setOnAntiViolenceClickListener {
            withState(vm) { s ->
                s.detail.invoke()?.also { post ->
                    if (post.canGoRoom) {
                        CommunityUtil.showRoomDialog(
                            this,
                            post.userStatus!!,
                            post.uid.orEmpty(),
                            post.nickname,
                            CategoryId.COMMUNITY_GAME_CARD
                        )
                    } else {
                        itemListener.goUserPage(post.uid)
                    }
                }
            }
        }
        binding.tvFollowBtn.setOnAntiViolenceClickListener {
            vm.followUser()
        }
        if (isPad) {
            val params = binding.includeAttitude.root.layoutParams as ConstraintLayout.LayoutParams
            params.matchConstraintMaxWidth = dp(320)
            params.marginStart = dp(0)
            params.marginEnd = dp(0)
        }
        binding.includeAttitude.layerLike.setOnAntiViolenceClickListener {
            shouldPlayLikeAnim = true
            vm.likePost()
            val isLike = !OpinionRequestBody.isLike(vm.oldState.opinion)
            if (isLike) {
                DialogShowManager.triggerLike(this@PostDetailFragment)
            }
        }
        binding.includeAttitude.layerShare.setOnAntiViolenceClickListener {
            showMoreDialog(false)
        }
        binding.ivOfficial.setListener(viewLifecycleOwner) {
            UserLabelView.showDescDialog(this, it)
        }
        binding.bottomCommentInput.setOnReplyClickListener {
            showReplyDialog(AddPostCommentReplyTarget(args.postId, vm.oldState.authorName))
        }
        binding.bottomCommentInput.ivEmojiBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.postId, vm.oldState.authorName),
                showEmoji = true
            )
        }
        binding.bottomCommentInput.ivImageBtn.setOnAntiViolenceClickListener {
            showReplyDialog(
                AddPostCommentReplyTarget(args.postId, vm.oldState.authorName),
                showImage = true
            )
        }
        binding.bottomCommentInput.setOnCommentClickListener {
            if (!tryScrollToComment()) {
                showReplyDialog(AddPostCommentReplyTarget(args.postId, vm.oldState.authorName))
            }
        }
        binding.bottomCommentInput.setOnLikeClickListener {
            shouldPlayLikeAnim = true
            val isLike = OpinionRequestBody.isLike(vm.oldState.opinion)
            if (!isLike) {
                DialogShowManager.triggerLike(this@PostDetailFragment)
            }
            vm.likePost()
        }

        binding.rvImg.layoutManager = GridLayoutManager(requireContext(), 6)
        binding.rvImg.setController(mediaController)
        binding.rvGame.layoutManager = LinearLayoutManager(requireContext())
        binding.rvGame.setController(gameController)
        binding.rvComment.layoutManager = LinearLayoutManager(requireContext())
        binding.rvComment.setController(commentController)
        commentController.adapter.registerAdapterDataObserver(
            viewLifecycleOwner,
            object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
                    if (positionStart == 0) {
                        binding.rvComment.scrollToPosition(0)
                    }
                }
            }
        )
        commentController.addModelBuildListener(viewLifecycleOwner) {
            if (needLocate) {
                needLocate = false
                scrollToTop = false
                binding.abl.setExpanded(false, true)
                binding.rvComment.smoothScrollToPosition(
                    locatePosition.coerceIn(
                        0,
                        commentController.adapter.itemCount - 1
                    )
                )
            } else if (scrollToTop) {
                scrollToTop = false
                binding.rvComment.scrollToPosition(0)
            }
        }
        //接口请求在ViewMode的Init方法调用，这个认为首次调用ViewModel的地方是接口请求的开始
        vm.registerToast(PostDetailState::errorToast)
        var trackShow = false
        vm.onAsync(
            PostDetailState::detail,
            onFail = {},
        ) {
            val isMe = vm.isMe(it.uid)
            glide?.run {
                load(it.avatar).error(R.drawable.placeholder)
                    .into(binding.ivAuthorAvatar)
            }
            binding.ivMore.setImageDrawable(
                if(isMe) {
                    getDrawableByRes(R.drawable.ic_title_bar_more)
                } else {
                    getDrawableByRes(R.drawable.ic_share_black)
                }
            )
            binding.tvAuthorName.text = it.nickname
            binding.tvTime.text = it.createTime.formatCreateDate(requireContext())
            binding.tvContent.movementMethod =
                InterceptClickEventLinkMovementMethod(binding.tvContent)

            val content = CommunityUtil.parsePostContent(
                requireContext(),
                it.content,
                it.tagList,
                it.communityTagList,
            ) {
                goTopicDetail(it, args.postId)
            }
            binding.tvContent.text = content
            binding.tvContent.visible(!content.isNullOrEmpty())

            binding.ivMore.visible()
            if (it.mediaList.isNullOrEmpty()) {
                binding.vplv.gone()
                binding.rvImg.gone()
            } else {
                initMedia(it.mediaList)
            }
            binding.rvGame.gone(it.cardList.isEmpty())

            binding.ivOfficial.show(
                it.tagIds,
                it.labelInfo ?: it.user?.labelInfo,
                isOfficial = it.user?.isOfficial == true,
                glide = glide
            )
            val gameStatus = it.userStatus.toLocalStatus()
            binding.ivState.visible((it.canGoRoom && it.userStatus != null) || (gameStatus == FriendStatus.ONLINE || gameStatus == FriendStatus.PLAYING_GAME))
            visibleList(binding.spaceFollow, binding.tvFollowBtn, visible = !isMe)
            PostHelper.updateReviewStatus(binding.tvReviewStatusLabel, it.status)
            binding.vRedDotMore.visible(isMe && it.reviewFail && vm.showEditRedDot)
            binding.lv.hide()
            if (!trackShow) {
                trackShow = true
                trackShow(null)
            }
            vm.initData()

            visibleList(
                commentMorePopup.sortPopupBinding.mtvAuthorOnly,
                commentMorePopup.sortPopupBinding.vAuthorOnlyClick,
                commentMorePopup.sortPopupBinding.vDivider4,
                visible = !isMe
            )
        }
        vm.onEach(PostDetailState::followStatus) {
            if (it) {
                binding.tvFollowBtn.status = FollowView.Status.FOLLOWING
            } else {
                binding.tvFollowBtn.status = FollowView.Status.UNFOLLOW
            }
        }
        vm.onEach(PostDetailState::opinion, PostDetailState::likeCount) { opinion, likeCount ->
            val isLike = OpinionRequestBody.isLike(opinion)

            binding.apply {
                includeAttitude.lavLikeAnim.setMinAndMaxProgress(0.0f, 1.0f)
                if (isLike) {
                    includeAttitude.ivLike.invisible()
                    includeAttitude.lavLikeAnim.visible()
                    if (shouldPlayLikeAnim) {
                        includeAttitude.lavLikeAnim.progress = 0.0f
                        includeAttitude.lavLikeAnim.playAnimation()
                    } else {
                        includeAttitude.lavLikeAnim.progress = 1.0f
                    }
                } else {
                    includeAttitude.ivLike.visible()
                    includeAttitude.lavLikeAnim.cancelAnimationIfAnimating()
                    includeAttitude.lavLikeAnim.gone()
                }
                includeAttitude.tvLike.text = UnitUtil.formatKMCount(likeCount)
                includeAttitude.tvLike.setTextColorByRes(if (isLike) R.color.color_FF5F42 else R.color.color_666666)
                bottomCommentInput.updateLike(isLike, likeCount, shouldPlayLikeAnim)
                shouldPlayLikeAnim = false
            }
        }
        vm.onEach(
            PostDetailState::commentList,
            PostDetailState::commentCount,
            PostDetailState::loadMore,
        ) { commentList, count, loadMore ->
            val commentCount = UnitUtil.formatKMCount(count)
            binding.includeAttitude.tvComment.text = commentCount
            binding.tvCommentCountHang.setTextWithArgs(R.string.comment_count, commentCount)
            binding.bottomCommentInput.tvComment.text = commentCount
            if (commentList is Success && loadMore is Success) {
                binding.tvCommentSortHang.visible()
            }
        }
        vm.onEach(PostDetailState::shareCount) {
            binding.includeAttitude.tvShare.text = UnitUtil.formatKMCount(it)
        }
        vm.onEach(
            PostDetailState::queryType,
            PostDetailState::filterType,
        ) { sortType, filterType ->
            updateSortTypeText(sortType, filterType)
        }
        vm.onEach(PostDetailState::deletePostResult) {
            it ?: return@onEach
            isDelete = true
            navigateUp()
        }
        vm.onEach(PostDetailState::reportData, uniqueOnly()) {
            it ?: return@onEach
            MetaRouter.Report.postReport(this, it.second, it.first) { submit ->
                if (submit) {
                    val analyticsParams = if (it.first == ReportType.Post) {
                        ReportSuccessDialogAnalyticsParams.Post(
                            postId = it.second,
                        )
                    } else if (it.first == ReportType.PostComment) {
                        ReportSuccessDialogAnalyticsParams.PostComment(
                            postId = args.postId,
                            commentId = it.second,
                        )
                    } else if (it.first == ReportType.PostReply) {
                        ReportSuccessDialogAnalyticsParams.PostCommentReply(
                            postId = args.postId,
                            replyId = it.second,
                        )
                    } else {
                        null
                    }
                    ReportReasonDialog.showReportSuccessDialog(this, analyticsParams)
                }
            }
        }
        vm.onEach(PostDetailState::muteStatus, uniqueOnly()) { tripleNullable ->
            val triple = tripleNullable ?: return@onEach
            val target = triple.first
            val postCommentContent = triple.second
            val muteStatus = triple.third
            if (!muteStatus.complete) return@onEach
            if (muteStatus.invoke()?.isMuted == true) {
                toast(getString(R.string.community_ban, getString(R.string.commenting)))
            } else {
                if (vm.oldState.needPermissionDialog) {
                    Timber.d("needPermissionDialog")
                    vm.updatePermissionDialog()
                    showPermissionDialog()
                } else {
                    if (target.isTargetPost) {
                        vm.addCommentViaNet(postCommentContent)
                    } else {
                        vm.addReplyViaNet(target,postCommentContent)
                    }
                }
            }
        }
        vm.setupRefreshLoadingCustomFail(
            PostDetailState::detail,
            binding.lv,
            binding.refreshLayout,
            onFail = { e, _ ->
                if ((e as? ApiResultCodeException)?.code == 1016) {
                    binding.lv.showEmpty(
                        msg = getString(R.string.post_not_exist),
                        resId = R.drawable.icon_no_recent_activity
                    )
                } else {
                    binding.lv.showError()
                }
                binding.ivMore.gone()
            }
        ) {
            vm.initData(isRefresh = true)
        }
        vm.checkPermission(requireContext())

        friendInteractor.friendList.observe(viewLifecycleOwner) { friendList ->
            if (friendList.isNullOrEmpty()) {
                return@observe
            }
            vm.updateFriendState(friendList)
        }
    }

    fun tryScrollToComment(): Boolean {
        val commentDividerY = binding.vDividerComment.getLocationInWindow().y
        val bottomInputY = binding.bottomCommentInput.getLocationInWindow().y
        if (commentDividerY > bottomInputY) {
            val titleDividerY = binding.tbl.getLocationInWindow().y + binding.tbl.height
            val distance = commentDividerY - titleDividerY
            val layoutParams = binding.abl.layoutParams as CoordinatorLayout.LayoutParams
            val animator = ValueAnimator.ofInt(0, distance)
            animator.duration = 200
            animator.addUpdateListener(
                this,
                true,
                object : ValueAnimator.AnimatorUpdateListener {
                    var lastValue = 0
                    override fun onAnimationUpdate(valueAnimator: ValueAnimator) {
                        if (!isBindingAvailable()) {
                            return
                        }
                        val value = valueAnimator.animatedValue as Int
                        layoutParams.behavior?.onNestedPreScroll(
                            binding.cdl,
                            binding.abl,
                            binding.rvComment,
                            0,
                            value - lastValue,
                            intArrayOf(0, 0),
                            ViewCompat.TYPE_TOUCH
                        )
                        lastValue = value
                    }
                })
            animator.start()
            return true
        }
        return false
    }

    fun updateSortTypeText(sortType: Int, filterType: Int?) {
        val resId = when (filterType) {
            PostCommentListRequestBody.FILTER_AUTHOR -> {
                R.string.sort_author_only
            }

            PostCommentListRequestBody.FILTER_SELF -> {
                R.string.sort_self_only
            }

            else -> {
                when (sortType) {
                    PostCommentListRequestBody.QUERY_TYPE_DEFAULT -> {
                        R.string.sort_hot
                    }

                    PostCommentListRequestBody.QUERY_TYPE_TOP -> {
                        R.string.sort_newest
                    }

                    PostCommentListRequestBody.QUERY_TYPE_LIKE -> {
                        R.string.sort_hottest
                    }

                    else -> {
                        return
                    }
                }
            }
        }
        binding.tvCommentSortHang.setText(resId)
    }

    private fun showPermissionDialog() {
        Analytics.track(EventConstants.EVENT_INTERACT_PUS_POST_SHOW)
        NotificationPermissionManager.showPermissionDialog(
            this,
            title = getString(R.string.notification_im_title),
            content = getString(R.string.notification_im_content),
            cancelText = getString(R.string.notification_app_cancel),
            confirmText = getString(R.string.notification_app_sure),
            dismissCallBack = {

            }, confirmCallBack = { resut ->
                if (resut) {
                    Analytics.track(
                        EventConstants.EVENT_INTERACT_PUS_POST_CLICK,
                        "result" to "0"
                    )
                } else {
                    Analytics.track(
                        EventConstants.EVENT_INTERACT_PUS_POST_CLICK,
                        "result" to "1"
                    )
                }
            }
        )


    }

    private fun goTopicDetail(tagInfo: PostTag, postId: String) {
        MetaRouter.Post.topicDetail(
            this,
            tagInfo,
            EventParamConstants.SOURCE_TOPIC_POST_DETAIL,
            postId
        )
    }

    private fun initMedia(medias: List<PostMediaResource>) {
        val video = medias.firstOrNull { media -> media.isVideo }
        if (video == null) {
            binding.rvImg.visible()
            binding.vplv.gone()
        } else {
            binding.rvImg.gone()
            binding.vplv.apply {
                visible()
                val videoPath = video.resPath
                currentFragment = this@PostDetailFragment
                FeedImageVideoUtil.setVideoSize(
                    null,
                    this,
                    video,
                    videoPath,
                    ScreenUtil.screenWidth - 32.dp,
                    glide,
                    fullContentWidth = false
                )
                setDataResource(PlayerVideoResource(videoPath, videoPath))
                setOnClickFullScreen {
                    itemListener.openMedia(video, 0)
                }
                setOnVideoResume {
                    onVideoStateChange(true)
                }
                setOnVideoPause {
                    onVideoStateChange(false)
                }
                val newPlayer = SimpleExoPlayer.Builder(requireContext())
                    .setMediaSourceFactory(videoCacheInteractor.mediaSourceFactory)
                    .setLoadControl(FeedVideoHelper.fetchLodeControl(requireContext()))
                    .build()
                    .apply {
                        repeatMode = Player.REPEAT_MODE_OFF
                        volume = if (FeedVideoHelper.feedMuted) 0f else 1f
                        addListener(viewLifecycleOwner, this@PostDetailFragment)
                    }
                changeMuteView(FeedVideoHelper.feedMuted)
                addPlayer(newPlayer)
                if (videoProgress == -1L) {
                    videoProgress = args.videoProgress
                }
                setProgress(videoProgress)
                onActive(autoPlay = false)
            }
            initScrollCallback {
                playVideo()
            }
        }
    }

    private fun onVideoStateChange(resume: Boolean) {
        if (resume) {
            videoTime = System.currentTimeMillis()
        } else {
            val duration = System.currentTimeMillis() - videoTime
            if (duration > 0) {
                Analytics.track(
                    EventConstants.EVENT_POST_VIEW_VIDEO,
                    "playtime" to duration,
                    "postid" to args.postId
                )
            }
        }
    }

    private fun initPopup() {
        commentMorePopup.initPopup(
            onDefaultClick = {
                vm.setQueryType(PostCommentListRequestBody.QUERY_TYPE_DEFAULT)
            },
            onNewestClick = {
                vm.setQueryType(PostCommentListRequestBody.QUERY_TYPE_TOP)
            },
            onHotClick = {
                vm.setQueryType(PostCommentListRequestBody.QUERY_TYPE_LIKE)
            },
            onAuthorOnlyClick = {
                vm.updateFilterType(PostCommentListRequestBody.FILTER_AUTHOR)
            },
            onSelfOnlyClick = {
                vm.updateFilterType(PostCommentListRequestBody.FILTER_SELF)
            },
        )

        binding.tvCommentSortHang.setOnAntiViolenceClickListener {
            commentMorePopup.showSortPopup(
                targetView = it,
                rootLayout = binding.root,
                currentSortType = vm.oldState.queryType,
                currentFilterType = vm.oldState.filterType,
            )
        }
    }

    private fun showReplyInput(target: AddPostCommentReplyTarget) {
        showReplyDialog(target)
    }

    private fun showReplyDialog(
        target: AddPostCommentReplyTarget,
        showEmoji: Boolean = false,
        showImage: Boolean = false,
    ) {
        AccPwdV7Dialog.show(this, AccPwdV7DialogArgs.SOURCE_PUBLISH_REPLY_COMMENT) {
            if (it && isBindingAvailable()) {
                val replyType: Long
                val reviewId: String
                val type: Int
                if (target.isTargetComment) {
                    replyType = 0L
                    reviewId = target.asComment.commentId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_COMMENT
                } else if (target.isTargetReply) {
                    replyType = 1L
                    reviewId = target.asReply.replyId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_REPLAY
                } else {
                    replyType = -1L
                    reviewId = vm.oldState.postId
                    type = ArticleCommentInputDialog.TYPE_COMMENT_ARTICLE
                }
                vm.setReplyTarget(target)
                ArticleCommentInputDialog.show(
                    this,
                    replyUniqueKey = "$${args.postId}-$reviewId",
                    target.toNickname,
                    null,
                    null,
                    type,
                    0.7f,
                    showEmoji,
                    showImage,
                    getPageName(),
                    ArticleCommentInputDialogParams.CONTENT_TYPE_POST_DETAIL,
                    1,
                    true,
                    pageName = getPageName()
                ) {
                    if (it == null || !it.valid) return@show
                    // 检查当前用户是否已被禁言
                    vm.checkUserMuteStatus(target, it)
                }
            }
        }
    }

    private fun playVideo() {
        binding.vplv.getPlayer()?.run {
            if (binding.vplv.visiblePercent() > VideoFeedShowInfo.VIDEO_CAN_PLAY_SHOW_PERCENT) {
                autoPlayed = true
                play()
            } else {
                pause()
            }
        }

    }

    private fun pauseVideo() {
        binding.vplv.getPlayer()?.run {
            pause()
            videoProgress = currentPosition
        }
    }

    private fun clearVideo() {
        autoPlayed = false
        binding.vplv.getPlayer()?.run {
            removeListener(this@PostDetailFragment)
            binding.vplv.inActive()
            binding.vplv.removePlayer()
            release()
        }
    }

    private fun initScrollCallback(listener: (() -> Unit)?) {
        binding.cdl.stopScrollCallback = listener
        ((binding.abl.layoutParams as CoordinatorLayout.LayoutParams).behavior as? CustomAppBarBehavior)?.onFlingFinishedCallback =
            listener
    }

    override fun onDeviceVolumeChanged(volume: Int, muted: Boolean) {
        super.onDeviceVolumeChanged(volume, muted)
        binding.vplv.getPlayer()?.run {
            if (isPlaying) {
                FeedVideoHelper.feedMuted = false
                this.volume = 1F
                binding.vplv.changeMuteView(false)
            }
        }
    }

    private fun showMoreDialog(needFeature: Boolean) {
        val detail = vm.detail ?: return
        GlobalShareDialog.show(
            childFragmentManager,
            ShareRawData.post(detail),
            features = if (needFeature) {
                buildList {
                    if (vm.isMe(detail.uid)) {
                        add(
                            ShareFeature(
                                FEAT_DELETE,
                                R.drawable.ic_share_feat_delete,
                                titleRes = R.string.delete_cap
                            )
                        )
                        if (detail.reviewFail) {
                            add(
                                ShareFeature(
                                    FEAT_EDIT,
                                    R.drawable.ic_share_feat_edit,
                                    titleRes = R.string.edit,
                                    showRedDot = vm.showEditRedDot
                                )

                            )
                        }
                    } else {
                        add(
                            ShareFeature(
                                FEAT_REPORT,
                                R.drawable.ic_share_feat_report,
                                titleRes = R.string.report
                            )
                        )
                    }
                }
            } else {
                null
            }
        )
    }

    private val screenWidth:Int by lazy { ScreenUtil.getScreenWidth(requireContext()) }
    private val showImageDetail: (medias: List<PostMediaResource>, position: Int) -> Unit =
        { medias, position ->
            if (position in medias.indices) {
                itemListener.openMedia(medias[position], position)
            }
        }

    private fun buildMediaController() = simpleController(
        vm,
        PostDetailState::detail
    ) {
        val medias = it.invoke()?.mediaList ?: return@simpleController
        if (medias.any { media -> media.isVideo }) return@simpleController
        val totalImages = medias.filter { media -> media.resPath.isNotEmpty() }
        if (totalImages.isEmpty()) {
            return@simpleController
        }
        if (isPad && isBindingAvailable()) {
            // 如果是pad, 就不能撑满屏幕
            val params = binding.rvImg.layoutParams as ConstraintLayout.LayoutParams
            when (totalImages.size) {
                1 -> {
                    params.matchConstraintMaxWidth = screenWidth
                }

                2, 4 -> {
                    params.matchConstraintMaxWidth = dp(88) * 2 + dp(6)
                }

                else -> {
                    params.matchConstraintMaxWidth = dp(88) * 3 + dp(6) * 2
                }
            }
        }

        if (totalImages.size == 1) {
            // 单图
            add {
                CommunityFeedItemSingleImage(
                    contentWidth = screenWidth - dp(32),
                    topSpace = dp(4),
                    gridSpanSize = 6,
                    totalImageList = totalImages,
                    imageInfo = totalImages[0],
                    localPublishing = false,
                    position = 0,
                    glide = glide,
                    showImageDetail = showImageDetail,
                    startSpace = 6.dp,
                    rootClicked = {}
                ).id("CommunityFeedItemSingleImage-${totalImages[0].itemId}")
            }
        } else {
            // 多图
            totalImages.forEachIndexed { index, media ->
                postMediaItem(media, index, totalImages.size, itemListener)
            }
        }
    }

    private fun buildGameController() = simpleController(
        vm,
        PostDetailState::detail
    ) {
        val postDetail = it.invoke()?:return@simpleController
        if (isPad && isBindingAvailable()) {
            // 如果是pad, 就不能撑满屏幕
            val params = binding.rvGame.layoutParams as ConstraintLayout.LayoutParams
            params.matchConstraintMaxWidth = dp(288)
        }
        postDetail.cardList.forEach { card ->
            postAssetsItem(card, postDetail.nickname, dp10, itemListener)
        }
    }

    private fun buildTagController() = simpleController(
        vm,
        PostDetailState::detail
    ) {
        it.invoke()?.tagList?.forEach { tag ->
            postTagItem(tag, dp14, false, blueColor, itemListener)
        }
    }

    private fun buildCommentController() = simpleController(
        vm,
        PostDetailState::commentList,
        PostDetailState::loadMore,
        PostDetailState::uniqueTag,
        PostDetailState::showCommentPinRedDot,
    ) { comments, loadMore, uniqueTag, firstPin ->
        when (comments) {
            is Success -> {
                val list = comments().dataList
                if (list.isNullOrEmpty()) {
                    empty(
                        iconRes = R.drawable.icon_no_recent_activity,
                        descRes = R.string.let_comm_begin_with_your_comment,
                        top = dp(32)
                    ) {
                        vm.getCommentList(true)
                    }
                } else {
                    val dp05 = dp(0.5)
                    val dp8 = dp(8)
                    val dp62 = dp(62)
                    val dp16 = dp(16)
                    val commentContentWidth = screenWidth - 78.dp
                    val replyContentWidth = screenWidth - 112.dp
                    val atColor = getColorByRes(R.color.color_0083FA)
                    list.forEachIndexed { commentPosition, comment ->
                        if (comment.isNewAdd) {
                            comment.isNewAdd = false
                            scrollToTop = true
                        }
                        ugcCommentItem(
                            uniqueTag,
                            comment,
                            commentPosition,
                            commentContentWidth,
                            true,
                            firstPin,
                            itemListener
                        )
                        if (comment.needLocate) {
                            comment.needLocate = false
                            needLocate = true
                            locatePosition = buildItemIndex
                        }
                        var showReplyItem = false
                        if (!comment.collapse) {
                            showReplyItem = (comment.authorReply?.size
                                ?: 0) + (comment.replyCommonPage?.dataList?.size ?: 0) > 0
                            comment.authorReply?.forEachIndexed { replyPosition, reply ->
                                ugcReplyItem(
                                    uniqueTag,
                                    reply,
                                    replyPosition,
                                    commentPosition,
                                    true,
                                    atColor,
                                    replyContentWidth,
                                    true,
                                    itemListener
                                )
                                if (reply.needLocate) {
                                    reply.needLocate = false
                                    needLocate = true
                                    locatePosition = buildItemIndex
                                }
                            }
                            comment.replyCommonPage?.dataList?.forEachIndexed { replyPosition, reply ->
                                ugcReplyItem(
                                    uniqueTag,
                                    reply,
                                    replyPosition,
                                    commentPosition,
                                    false,
                                    atColor,
                                    replyContentWidth,
                                    true,
                                    itemListener
                                )
                                if (reply.needLocate) {
                                    reply.needLocate = false
                                    needLocate = true
                                    locatePosition = buildItemIndex
                                }
                            }
                        } else {
                            comment.replyCommonPage?.dataList?.forEachIndexed { replyPosition, reply ->
                                if (reply.forceShow) {
                                    showReplyItem = true
                                    ugcReplyItem(
                                        uniqueTag,
                                        reply,
                                        replyPosition,
                                        commentPosition,
                                        false,
                                        atColor,
                                        replyContentWidth,
                                        true,
                                        itemListener
                                    )
                                    if (reply.needLocate) {
                                        reply.needLocate = false
                                        needLocate = true
                                        locatePosition = buildItemIndex
                                    }
                                }
                            }
                        }
                        val showReplyButtons = comment.showReplyButtons
                        if (comment.loading) {
                            ugcCommentLoading(
                                uniqueTag,
                                comment
                            )
                        } else if (showReplyButtons) {
                            ugcCommentExpandItem(
                                uniqueTag,
                                comment,
                                commentPosition,
                                showReplyItem,
                                itemListener
                            )
                        }
                    }
                    loadMoreFooter(
                        loadMore,
                        idStr = "PostCommentFooter-${uniqueTag}",
                        endText = getString(R.string.community_article_comment_empty),
                        endTextColorRes = R.color.textColorSecondary
                    ) {
                        vm.getCommentList(false)
                    }
                }
            }

            is Loading -> {
                loadMoreFooter(idStr = "PostCommentFooterLoading") {}
            }

            is Fail -> {
                empty(
                    iconRes = R.drawable.icon_no_recent_activity,
                    descRes = R.string.footer_load_failed,
                    top = dp(32)
                ) {
                    vm.initCommentList()
                }
            }

            else -> {}
        }

    }

    override fun onShareCountIncrease(data: ShareRawData) {
        if (data.scene == ShareHelper.SCENE_POST_DETAIL && data.postDetail != null) {
            vm.addShareCount(data.postDetail.postId)
        }
    }

    override fun invokeShareFeature(feature: ShareFeature) {
        when (feature.featureId) {
            FEAT_DELETE -> {
                vm.detail?.let {
                    Analytics.track(
                        EventConstants.POST_DELETE_CLICK,
                        EventParamConstants.KEY_POSTID to it.postId,
                        EventParamConstants.KEY_REVIEW_TAG to it.reviewStatus2TrackParam
                    )
                }
                ConfirmDialog.Builder(this)
                    .content(getString(R.string.feed_delete_post_confirm))
                    .cancelBtnTxt(getString(R.string.dialog_cancel))
                    .confirmBtnTxt(getString(R.string.delete_cap))
                    .isRed(true)
                    .confirmCallback {
                        vm.detail?.let {
                            Analytics.track(
                                EventConstants.POST_DELETE_CONFIRM,
                                EventParamConstants.KEY_POSTID to it.postId,
                                EventParamConstants.KEY_REVIEW_TAG to it.reviewStatus2TrackParam
                            )
                        }
                        vm.deletePost()
                    }
                    .show()
            }

            FEAT_REPORT -> {
                vm.reportPost()
            }

            FEAT_EDIT -> {
                vm.showEditRedDot = false
                vm.detail?.let {
                    Analytics.track(
                        EventConstants.POST_EDIT_CLICK,
                        EventParamConstants.KEY_POSTID to it.postId.orEmpty(),
                        EventParamConstants.KEY_REVIEW_TAG to it.reviewStatus2TrackParam
                    )
                    MetaRouter.Post.goPublishPost(
                        this,
                        ResIdBean().setCategoryID(CategoryId.OUTFIT_SHARE_POST_DETAIL),
                        showRule = false,
                        postPublish = it.toPostPublish()
                    )
                }
            }
        }
    }

    override fun onDestroyView() {
        clearVideo()
        initScrollCallback(null)
        withState(vm) { s ->
            if (s.detail is Success) {
                setFragmentResult(
                    KEY_ACTION_POST,
                    bundleOf(
                        KEY_PARAM_POST_ID to args.postId,
                        KEY_PARAM_IS_DELETE to isDelete,
                        KEY_PARAM_OPINION to s.opinion,
                        KEY_PARAM_LIKE_COUNT to s.likeCount,
                        KEY_PARAM_COMMENT_COUNT to s.commentCount,
                        KEY_PARAM_SHARE_COUNT to s.shareCount
                    )
                )
            }
        }
        commentMorePopup.release()
        super.onDestroyView()
    }

    private fun goReport(reportId: String, reportType: ReportType) {
        MetaRouter.Report.postReport(this, reportId, reportType) {
            if (it) {
                Analytics.track(
                    EventConstants.LIBRARY_REVIEW_REPORT_SUCCESS,
                    "metrialidid" to args.postId,
                    "reviewid" to reportId,
                    "reviewtype" to if (reportType == ReportType.PostComment) 1L else 2L
                )
                val analyticsParams = when (reportType) {
                    ReportType.PostComment -> {
                        ReportSuccessDialogAnalyticsParams.PostComment(
                            postId = args.postId,
                            commentId = reportId,
                        )
                    }

                    ReportType.PostReply -> {
                        ReportSuccessDialogAnalyticsParams.PostCommentReply(
                            postId = args.postId,
                            replyId = reportId,
                        )
                    }

                    else -> {
                        null
                    }
                }
                ReportReasonDialog.showReportSuccessDialog(this, analyticsParams)
            }
        }
    }

    override fun onNewDuration(duration: Long) {
        super.onNewDuration(duration)
        trackShow(duration)
    }

    private fun trackShow(time: Long?) {
        val detail = vm.detail
        Analytics.track(EventConstants.EVENT_POST_DETAIL_SHOW) {
            put(EventParamConstants.KEY_POSTID, args.postId)
            time?.let { it1 -> put(EventParamConstants.KEY_PLAYTIME, it1) }
            put(EventParamConstants.KEY_SOURCE, args.source.toString())
            if (detail != null) {
                detail.tagList?.map { it.tagId }?.let {
                    put(EventParamConstants.KEY_TAG_LIST, it)
                }
                put(EventParamConstants.KEY_REVIEW_TAG, detail.reviewStatus2TrackParam)
            }
        }
    }

    override fun onDestroy() {
        vm.clearReplyTarget()
        super.onDestroy()
    }

    override fun invalidate() {}

    override fun getPageName() = PageNameConstants.FRAGMENT_POST_DETAIL
}