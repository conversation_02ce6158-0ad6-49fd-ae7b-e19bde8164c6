package com.socialplay.gpark.ui.toolkit

import android.view.View
import android.view.ViewGroup
import com.airbnb.epoxy.VisibilityState
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.outfit.UgcDesignFeed
import com.socialplay.gpark.data.model.outfit.UgcDesignToolkit
import com.socialplay.gpark.databinding.ItemToolkitDividerBinding
import com.socialplay.gpark.databinding.ItemToolkitGameUsedBinding
import com.socialplay.gpark.databinding.ItemToolkitRecommendBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.util.GradientTransformation
import com.socialplay.gpark.util.UnitUtil
import com.socialplay.gpark.util.UnitUtilWrapper
import com.socialplay.gpark.util.extension.context
import com.socialplay.gpark.util.extension.gone
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setWidth
import com.socialplay.gpark.util.extension.visible
import com.socialplay.gpark.util.extension.visibleList

interface IUgcItemClickedListener {
    fun onBuyClicked(item: UgcDesignToolkit)
    fun onClicked(item: UgcDesignToolkit)
    fun onItemVisible(item: UgcDesignToolkit)
}

interface IItemClickedListener {
    fun onClicked(item: UgcDesignFeed)
    fun onItemVisible(item: UgcDesignFeed)
}

class ToolkitUsedItem(
    val glide: RequestManager?,
    /**
     * 是否是主态
     */
    val isMainState: Boolean,
    val currentUuid: String,
    val itemWidth: Int? = null,
    val item: UgcDesignToolkit,
    val viewWidth: Int? = null,
    val listener: IUgcItemClickedListener,
) : ViewBindingItemModel<ItemToolkitGameUsedBinding>(
    R.layout.item_toolkit_game_used,
    ItemToolkitGameUsedBinding::bind
) {
    override fun ItemToolkitGameUsedBinding.onBind() {
        if (viewWidth == null) {
            root.setWidth(ViewGroup.LayoutParams.MATCH_PARENT)
        } else {
            root.setWidth(viewWidth)
        }
        root.setOnAntiViolenceClickListener {
            listener.onClicked(item)
        }
        glide?.apply {
            load(item.cover).placeholder(R.drawable.placeholder_corner_12)
                .into(ivToolkit)
        }
        tvToolkitTitle.text = item.title.orEmpty()
        if (isMainState) {
            visibleList(
                ivToolkitPrice,
                tvToolkitPrice,
                ivToolkitAdd,
                tvObtained,
                visible = false
            )
            visibleList(
                ivToolkitSold,
                tvToolkitSold,
                visible = true
            )
            tvToolkitSold.text = UnitUtil.formatNumberWithUnit(num = item.popularity ?: 0)
        } else {
            visibleList(
                ivToolkitSold,
                tvToolkitSold,
                visible = false
            )
            val isObtained = item.isObtained(currentUuid)
            if (isObtained) {
                // 当前用户已拥有的情况
                visibleList(
                    ivToolkitPrice,
                    tvToolkitPrice,
                    ivToolkitAdd,
                    visible = false
                )
                tvObtained.visible()
            } else {
                tvObtained.gone()
                visibleList(
                    ivToolkitPrice,
                    tvToolkitPrice,
                    ivToolkitAdd,
                    visible = true
                )
                val isFree = item.isPriced != true || (item.price != null && item.price <= 0)
                if (isFree) {
                    tvToolkitPrice.setText(R.string.assets_price_free)
                } else if (item.price == null) {
                    // 不免费, 但服务器没下发价格, 数据有异常
                    tvToolkitPrice.text = "---"
                    ivToolkitAdd.gone()
                } else {
                    tvToolkitPrice.text = UnitUtilWrapper.formatCoinCont(item.price)
                }
                ivToolkitAdd.setOnAntiViolenceClickListener {
                    listener.onBuyClicked(item)
                }
            }
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.FOCUSED_VISIBLE) {
            listener.onItemVisible(item)
        }
    }
}

class ToolkitDividerItem() : ViewBindingItemModel<ItemToolkitDividerBinding>(
    R.layout.item_toolkit_divider,
    ItemToolkitDividerBinding::bind
) {
    override fun ItemToolkitDividerBinding.onBind() {
        // ignore
    }
}

class ToolkitRecommendItem(
    val glide: RequestManager?,
    val item: UgcDesignFeed,
    val listener: IItemClickedListener,
) : ViewBindingItemModel<ItemToolkitRecommendBinding>(
    R.layout.item_toolkit_recommend,
    ItemToolkitRecommendBinding::bind
) {
    override fun ItemToolkitRecommendBinding.onBind() {
        root.setOnAntiViolenceClickListener {
            listener.onClicked(item)
        }
        glide?.apply {
            load(item.cover).placeholder(R.drawable.placeholder_corner_12)
                .into(ivToolkit)
        }
        mlvPlayers.setLikeText(UnitUtil.formatNumberWithUnit(num = item.favorites))
        tvToolkitTitle.text = item.title.orEmpty()
        val tags = item.tags
        if (tags.isNullOrEmpty()) {
            tagsAssets.gone()
        } else {
            tagsAssets.visible()
            tagsAssets.setTags(tags)
        }
        val isFree = item.isPriced != true || (item.price != null && item.price <= 0)
        if (isFree) {
            tvToolkitPrice.setText(R.string.assets_price_free)
        } else if (item.price == null) {
            // 不免费, 但服务器没下发价格, 数据有异常
            tvToolkitPrice.text = "---"
        } else {
            tvToolkitPrice.text = UnitUtilWrapper.formatCoinCont(item.price)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        super.onVisibilityStateChanged(visibilityState, view)
        if (visibilityState == VisibilityState.FOCUSED_VISIBLE) {
            listener.onItemVisible(item)
        }
    }
}