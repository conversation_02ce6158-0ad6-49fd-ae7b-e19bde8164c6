package com.socialplay.gpark.ui.view

import android.view.View
import timber.log.Timber

/**
 * MetaLikeView智能模糊效果扩展函数
 * 
 * 使用此函数替代直接调用setBlurSource和startBlur
 * 该扩展函数会自动管理模糊效果的启动时机，确保在合适的时机应用模糊效果
 * 只在滚动停止后应用模糊效果，避免拖拽时的性能问题
 * 
 * @param viewId 视图唯一标识符，用于在BlurEffectManager中注册
 * @param sourceView 模糊源视图
 * @param cacheKey 缓存键，用于缓存模糊效果
 * @param overlayColor 覆盖颜色资源ID，可选
 */
fun MetaLikeView.enableSmartBlur(
    viewId: String,
    sourceView: View?,
    cacheKey: String?,
    overlayColor: Int? = null
) {
//    // 注册到BlurEffectManager，传递overlayColor参数
//    BlurEffectManager.getInstance().registerBlurView(
//        "$viewId-${id}",
//        this,
//        sourceView,
//        "${cacheKey}-${id}",
//        overlayColor
//    )
}

fun MetaLikeView.enableSmartBlur(
    viewId: Long,
    sourceView: View?,
    cacheKey: String?,
    overlayColor: Int? = null
) {
//    // 注册到BlurEffectManager，传递overlayColor参数
//    BlurEffectManager.getInstance().registerBlurView(
//        "$viewId-${id}",
//        this,
//        sourceView,
//        "${cacheKey}-${id}",
//        overlayColor
//    )
}

/**
 * 标记视图准备就绪（图片加载完成等）
 * 
 * @param viewId 视图唯一标识符
 */
fun MetaLikeView.markReady(viewId: String) {
//    BlurEffectManager.getInstance().markViewReady("$viewId-${id}")
}

fun MetaLikeView.markReady(viewId: Long) {
//    BlurEffectManager.getInstance().markViewReady("$viewId-${id}")
}

/**
 * 取消注册模糊视图
 * 
 * @param viewId 视图唯一标识符
 */
fun MetaLikeView.disableSmartBlur(viewId: String) {
//    BlurEffectManager.getInstance().unregisterBlurView("$viewId-${id}")
}

fun MetaLikeView.disableSmartBlur(viewId: Long) {
//    BlurEffectManager.getInstance().unregisterBlurView("$viewId-${id}")
}