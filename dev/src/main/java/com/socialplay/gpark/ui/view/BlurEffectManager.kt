package com.socialplay.gpark.ui.view

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.epoxy.VisibilityState
import timber.log.Timber
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap

/**
 * 智能模糊效果管理器
 * 
 * 功能特性：
 * - 只在滚动停止后开始模糊效果，避免拖拽时的性能问题
 * - 自动管理模糊状态，避免重复计算
 * - 支持批量操作和性能优化
 * - 内存友好的资源管理
 * 
 * 使用方法：
 * 
 * 1. 在Fragment中初始化：
 * ```kotlin
 * class MyFragment : Fragment() {
 *     private val blurEffectManager = BlurEffectManager.getInstance()
 *     
 *     override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
 *         super.onViewCreated(view, savedInstanceState)
 *         
 *         // 注册RecyclerView（自动监听滚动状态）
 *         blurEffectManager.registerRecyclerView(recyclerView)
 *     }
 *     
 *     override fun onDestroyView() {
 *         super.onDestroyView()
 *         blurEffectManager.clear()
 *     }
 * }
 * ```
 * 
 * 2. 在Item中使用：
 * ```kotlin
 * data class MyItem(...) : ViewBindingItemModel<MyBinding>(...) {
 *     private val blurEffectManager = BlurEffectManager.getInstance()
 *     private val viewId = "MyItem_${item.id}_${item.hashCode()}"
 *     
 *     override fun MyBinding.onBind() {
 *         // 注册模糊视图
 *         blurEffectManager.registerBlurView(
 *             viewId = viewId,
 *             metaLikeView = mlvLike,
 *             sourceView = ivImage,
 *             cacheKey = item.imageUrl
 *         )
 *         
 *         // 图片加载完成后标记准备就绪
 *         loadImage(item.imageUrl) {
 *             blurEffectManager.markViewReady(viewId)
 *         }
 *     }
 *     
 *     override fun MyBinding.onUnbind() {
 *         blurEffectManager.unregisterBlurView(viewId)
 *     }
 * }
 * ```
 * 
 * 3. 使用扩展函数（推荐方式）：
 * ```kotlin
 * import com.socialplay.gpark.ui.view.BlurEffectExtensions.*
 * 
 * // 在onBind中
 * mlvLike.enableSmartBlur(viewId, ivImage, item.imageUrl)
 * 
 * // 图片加载完成后
 * mlvLike.markReady(viewId)
 * 
 * // 在onUnbind中
 * mlvLike.disableSmartBlur(viewId)
 * ```
 */
class BlurEffectManager private constructor() {
    
    companion object {
        private const val TAG = "BlurEffectManager"
        
        @Volatile
        private var INSTANCE: BlurEffectManager? = null
        
        fun getInstance(): BlurEffectManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: BlurEffectManager().also { INSTANCE = it }
            }
        }
    }
    
    // 存储所有注册的MetaLikeView及其状态
    private val blurViews = ConcurrentHashMap<String, BlurViewInfo>()
    
    // RecyclerView滚动状态
    private var isScrolling = false
    private var scrollState = RecyclerView.SCROLL_STATE_IDLE
    

    
    /**
     * 注册RecyclerView，开始监听滚动状态
     */
    fun registerRecyclerView(recyclerView: RecyclerView) {
//        // 使用更高效的滚动监听器
//        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//            private var lastScrollState = RecyclerView.SCROLL_STATE_IDLE
//            private var scrollStateChangeTime = 0L
//
//            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
//                super.onScrollStateChanged(recyclerView, newState)
//
//                // 避免频繁的状态变化
//                if (lastScrollState == newState) return
//
//                val currentTime = System.currentTimeMillis()
//                // 防止快速连续的状态变化
//                if (currentTime - scrollStateChangeTime < 50) return
//
//                lastScrollState = newState
//                scrollStateChangeTime = currentTime
//                scrollState = newState
//                isScrolling = newState != RecyclerView.SCROLL_STATE_IDLE
//
//                Timber.tag(TAG).d("Scroll state changed: $newState, isScrolling: $isScrolling")
//
//                // 有一种特殊情况，目前在华为Pad上面遇到过，惯性滚动过程中，手指触摸一下屏幕，滚动会暂停，但是不会改变状态为静止状态
//
//                // 只在滚动停止时，处理所有待模糊的视图
//                if (!isScrolling) {
//                    // 延迟处理，避免在状态变化过程中立即处理
//                    recyclerView.post {
//                        if (!isScrolling) {
//                            processPendingBlurViews()
//                        }
//                    }
//                }
//            }
//
//            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
//                super.onScrolled(recyclerView, dx, dy)
//                // 减少滚动时的处理，只在必要时更新状态
//            }
//        })
    }
    

    
    /**
     * 注册MetaLikeView，准备进行模糊效果
     */
    fun registerBlurView(
        viewId: String,
        metaLikeView: MetaLikeView,
        sourceView: View?,
        cacheKey: String?,
        overlayColor: Int? = null
    ) {
//        // 先设置模糊源
//        if (overlayColor != null) {
//            metaLikeView.setBlurSource(sourceView, cacheKey, overlayColor)
//        } else {
//            metaLikeView.setBlurSource(sourceView, cacheKey)
//        }
//
//        val info = BlurViewInfo(
//            metaLikeView = WeakReference(metaLikeView),
//            sourceView = if (sourceView != null) WeakReference(sourceView) else null,
//            cacheKey = cacheKey,
//            isBlurReady = false,
//            isBlurApplied = false
//        )
//
//        blurViews[viewId] = info
//        Timber.tag(TAG).d("Registered blur view: $viewId, isScrolling: $isScrolling")
//
//        // 移除缓存检查，等待图片加载完成后再应用模糊效果
//        // checkAndApplyCachedBlur(info)
    }
    

    
    /**
     * 标记视图准备就绪（图片加载完成等）
     */
    fun markViewReady(viewId: String) {
//        blurViews[viewId]?.let { info ->
//            info.isBlurReady = true
//            Timber.tag(TAG).d("Marked view ready: $viewId, isScrolling: $isScrolling, scrollState: $scrollState")
//
//            // 如果不在滚动且准备就绪，立即应用模糊
//            if (!isScrolling && !info.isBlurApplied) {
//                applyBlurEffect(info)
//            }
//        }
    }
    
    /**
     * 取消注册视图
     */
    fun unregisterBlurView(viewId: String) {
//        blurViews.remove(viewId)?.let { info ->
//            // 清理资源
//            info.metaLikeView.get()?.setBlurSource(null, null)
//            Timber.tag(TAG).d("Unregistered blur view: $viewId")
//        }
    }
    
    /**
     * 清理所有资源
     */
    fun clear() {
//        blurViews.values.forEach { info ->
//            info.metaLikeView.get()?.setBlurSource(null, null)
//        }
//        blurViews.clear()
//        Timber.tag(TAG).d("Cleared all blur views")
    }
    

    
    /**
     * 应用模糊效果
     */
    private fun applyBlurEffect(info: BlurViewInfo) {
//        val metaLikeView = info.metaLikeView.get() ?: return
//        val sourceView = info.sourceView?.get() ?: return
//
//        try {
//            Timber.tag(TAG).d("Applying blur effect - isScrolling: $isScrolling, cacheKey: ${info.cacheKey}")
//            metaLikeView.startBlur(sourceView, info.cacheKey)
//            info.isBlurApplied = true
//            Timber.tag(TAG).d("Successfully applied blur effect to view with cacheKey: ${info.cacheKey}")
//        } catch (e: Exception) {
//            Timber.tag(TAG).e(e, "Failed to apply blur effect")
//        }
    }
    

    
    /**
     * 处理所有待模糊的视图
     */
    private fun processPendingBlurViews() {
        Timber.tag(TAG).d("Processing pending blur views, count: ${blurViews.size}")
        blurViews.values.forEach { info ->
            if (info.isBlurReady && !info.isBlurApplied) {
                applyBlurEffect(info)
            }
        }
    }
    

    
    /**
     * 获取当前滚动状态
     */
    fun isCurrentlyScrolling(): Boolean = isScrolling
    
    /**
     * 获取当前滚动状态值
     */
    fun getCurrentScrollState(): Int = scrollState
    
    /**
     * 获取注册的模糊视图数量
     */
    fun getRegisteredViewCount(): Int = blurViews.size
    
    /**
     * 模糊视图信息数据类
     */
    private data class BlurViewInfo(
        val metaLikeView: WeakReference<MetaLikeView>,
        val sourceView: WeakReference<View>?,
        val cacheKey: String?,
        var isBlurReady: Boolean,
        var isBlurApplied: Boolean
    )
}

 