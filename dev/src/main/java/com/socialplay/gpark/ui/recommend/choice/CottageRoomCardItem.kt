package com.socialplay.gpark.ui.recommend.choice

import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.VisibilityState
import com.socialplay.gpark.R
import com.socialplay.gpark.data.model.choice.ChoiceCardInfo
import com.socialplay.gpark.data.model.room.House
import com.socialplay.gpark.databinding.ItemHomeRoomBinding
import com.socialplay.gpark.ui.core.ViewBindingItemModel
import com.socialplay.gpark.ui.core.views.MetaModelCollector
import com.socialplay.gpark.ui.core.views.carouselNoSnapWrapBuilder
import com.socialplay.gpark.ui.room.RoomCardUtil
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setSize
import com.socialplay.gpark.util.extension.setWidth

/**
 * <pre>
 *     author : cuiyongchun
 *     e-mail : <EMAIL>
 *     time   : 2024/11/01
 *     desc   :
 * </pre>
 */
fun MetaModelCollector.choiceCottageRoomCard(
    card: ChoiceCardInfo,
    cardPosition: Int,
    spanSize: Int,
    listener: IChoiceListener
) {
    val roomList = card.homeRoomList
    if (roomList.isNullOrEmpty()) return
    add(
        ChoiceTitleMoreItem(card, spanSize, listener).id("ChoiceCottageRoomCardTitle-$cardPosition")
            .spanSizeOverride { totalSpanCount, _, _ -> spanSize.coerceAtMost(totalSpanCount) }
    )
    carouselNoSnapWrapBuilder {
        id("ChoiceCottageRoomCardList-$cardPosition")
        padding(Carousel.Padding.dp(16, 16, 6, 8, 0))
        hasFixedSize(true)
        initialPrefetchItemCount(1)
        onVisibilityStateChanged { _, _, visibilityState ->
            if (visibilityState == VisibilityState.VISIBLE) {
                listener.onCardShow(cardPosition, card)
            }
        }
        // 在StaggeredGridLayoutManager中，spanSizeOverride不会生效
        // 在GridLayoutManager中，spanSize通过spanSizeOverride控制
        // Carousel需要占满整行，但不超过总的 span 数量
        spanSizeOverride { totalSpanCount, _, _ -> spanSize.coerceAtMost(totalSpanCount) }
        val itemWidth = RoomCardUtil.getRoomCardWidth()
        roomList.forEachIndexed { position, room ->
            add(
                ChoiceCottageRoomCardItem(
                    room,
                    position,
                    card,
                    cardPosition,
                    false,
                    itemWidth,
                    spanSize,
                    listener
                ).id("ChoiceCottageRoomCard-${card.cardId}-$cardPosition-$position-${room.roomId}")
            )
        }
    }
}

data class ChoiceCottageRoomCardItem(
    val item: House,
    val position: Int,
    val card: ChoiceCardInfo,
    val cardPosition: Int,
    val matchParent: Boolean,
    val fitItemWidth: Int,
    val spanSize: Int,
    val listener: IChoiceListener
) : ViewBindingItemModel<ItemHomeRoomBinding>(
    R.layout.item_home_room,
    ItemHomeRoomBinding::bind
) {

    override fun ItemHomeRoomBinding.onBind() {
        if (matchParent) {
            root.setSize(
                RecyclerView.LayoutParams.MATCH_PARENT,
                RecyclerView.LayoutParams.WRAP_CONTENT
            )
            clContent.setWidth(ConstraintLayout.LayoutParams.MATCH_PARENT)
        } else {
            root.setSize(
                RecyclerView.LayoutParams.WRAP_CONTENT,
                RecyclerView.LayoutParams.WRAP_CONTENT
            )
            clContent.setWidth(fitItemWidth)
        }
        root.setMargin(right = dp(10))
        tvUserName.text = item.ownerNickname
        listener.getGlideOrNull()?.run {
            load(item.image).error(R.drawable.placeholder)
                .into(imgRoomCover)
            load(item.ownerAvatar).error(R.drawable.icon_default_avatar)
                .into(imgUserAvatar)
        }
        tvRoomName.text = item.description
        tvRoomNumber.text = (item.number + "/" + item.limitNumber)
        root.setOnAntiViolenceClickListener {
            listener.onItemClick(cardPosition, card, position, item, false)
        }
    }

    override fun onVisibilityStateChanged(visibilityState: Int, view: View) {
        if (visibilityState == VisibilityState.VISIBLE) {
            listener.onItemShow(cardPosition, card, position, item, false)
        }
    }
}